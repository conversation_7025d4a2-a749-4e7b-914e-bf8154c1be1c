import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { readFileSync } from 'fs';
import { Pool } from 'pg';

@Injectable()
export class DatabaseService {
  private readonly pool: Pool;
  private readonly logger = new Logger(DatabaseService.name);

  constructor(private readonly configService: ConfigService) {
    const certificateBundlePath = 'certificate-bundle.pem';

    this.pool = new Pool({
      user: this.configService.get<string>('DB_USER'),
      host: this.configService.get<string>('DB_HOST'),
      database: this.configService.get<string>('DB_DATABASE'),
      password: this.configService.get<string>('DB_PASSWORD'),
      port: this.configService.get<number>('DB_PORT'),
      ssl:
        process.env.ENV === 'production'
          ? {
              ca: readFileSync(certificateBundlePath),
            }
          : true,
    });

    this.logDatabaseConnection();
  }

  private async logDatabaseConnection() {
    let retries = 5;
    const delay = 3000;

    while (retries > 0) {
      try {
        await this.pool.connect();
        this.logger.log('Database connected successfully');
        break;
      } catch (error) {
        this.logger.error(
          `Error connecting to database: ${error.message}`,
          error.stack,
        );
        retries--;

        if (retries > 0) {
          this.logger.log(`Retrying in ${delay / 1000} seconds...`);
          await new Promise((resolve) => setTimeout(resolve, delay));
        } else {
          throw new Error('Failed to connect to the database after retries.');
        }
      }
    }
  }

  async query(sql: string, params: any[] = [], isBatch = true): Promise<any> {
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      if (
        Array.isArray(params) &&
        params.length > 0 &&
        Array.isArray(params[0]) &&
        isBatch
      ) {
        const batchValues = params
          .map(
            (paramSet, index) =>
              `(${paramSet.map((_, i) => `$${index * paramSet.length + i + 1}`).join(', ')})`,
          )
          .join(', ');

        const fullSql = `${sql} ${batchValues}`;
        const flattenedParams = params.flat();

        const result = await client.query(fullSql, flattenedParams);
        await client.query('COMMIT');
        return result.rows;
      } else {
        const result = await client.query(sql, params);
        await client.query('COMMIT');
        return result.rows;
      }
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }
}

import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Socket } from 'socket.io';
import { AuthService } from './../auth.service';

@Injectable()
export class WsJwtGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly authService: AuthService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const client: Socket = context.switchToWs().getClient();
      const token = this.extractTokenFromSocket(client);

      if (!token) {
        return false;
      }

      const payload = await this.jwtService.verifyAsync(token);
      const userId = payload.sub || payload.id || payload.userId;

      if (!userId) {
        return false;
      }

      if (!client.data.userId || !client.data.username) {
        const userDetails =
          await this.authService.getUserDetailsForTracking(userId);
        if (!userDetails) {
          return false;
        }

        const { username, shop_id, branch_id } = userDetails;

        client.data.user = payload;
        client.data.userId = userId;
        client.data.username = username;
        client.data.shopId = shop_id;
        client.data.branchId = branch_id;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  private extractTokenFromSocket(client: Socket): string | null {
    const authHeader = client.handshake.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    const token = client.handshake.query.token;
    if (typeof token === 'string') {
      return token;
    }

    const auth = client.handshake.auth;
    if (auth && auth.token) {
      return auth.token;
    }

    return null;
  }
}

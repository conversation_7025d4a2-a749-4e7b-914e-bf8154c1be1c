import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import axios from 'axios';
import { Observable } from 'rxjs';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private async sendTelegramMessage(message: string): Promise<void> {
    const url = `https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`;
    const params = new URLSearchParams({
      chat_id: process.env.CHAT_ID,
      disable_notification: 'true',
    });

    const maxMessageLength = 4096;
    const chunks = this.splitMessage(message, maxMessageLength);

    let attempts = 0;
    const maxAttempts = 3;

    for (const chunk of chunks) {
      while (attempts < maxAttempts) {
        try {
          await axios.post(
            `${url}?${params.toString()}&text=${encodeURIComponent(chunk)}`,
          );
          this.logger.log('Message sent successfully');
          break;
        } catch (error) {
          if (error.response?.status === 429) {
            this.logger.warn('Rate limit hit. Retrying...');
            attempts++;
            await this.delay(1000 * attempts);
          } else {
            this.logger.error(
              `Error sending Telegram message: ${error.message}`,
            );
            break;
          }
        }
      }

      if (attempts === maxAttempts) {
        this.logger.error(
          'Max retry attempts reached. Could not send message.',
        );
        break;
      }
    }
  }

  private splitMessage(message: string, maxLength: number): string[] {
    const chunks: string[] = [];
    while (message.length > maxLength) {
      chunks.push(message.slice(0, maxLength));
      message = message.slice(maxLength);
    }
    if (message.length > 0) {
      chunks.push(message);
    }
    return chunks;
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();

    if (request.method !== 'PATCH') {
      return next.handle();
    }

    const ip =
      `${request.headers['x-forwarded-for']}`.split(',')[0] ||
      request.connection.remoteAddress;

    const logMessage = `Request from IP: ${ip}\nURL: ${request.originalUrl}\nMethod: ${request.method}\nBody: ${JSON.stringify(request.body)}`;

    this.logger.log(logMessage);
    this.sendTelegramMessage(logMessage);

    return next.handle();
  }
}

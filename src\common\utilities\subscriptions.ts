import {
  CallHandler,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import Stripe from 'stripe';
import { DatabaseService } from '../config/database.services';

@Injectable()
export class SubscriptionService {
  private readonly logger = new Logger(SubscriptionService.name);
  constructor(private readonly databaseService: DatabaseService) {}

  async getSubscriptionDetails(
    shopId: string,
    next?: CallHandler,
  ): Promise<any> {
    const stripe = new Stripe(
      shopId === 'Cravin'
        ? process.env.STRIPE_API_KEY
        : process.env.STRIPE_PROD_API_KEY,
    );

    try {
      const shopSubscriptionStatus = await this.getSubscriptionStatus(shopId);

      if (shopSubscriptionStatus.length === 0) {
        throw new NotFoundException('Invalid shopId to fetch subscription');
      }

      const { subscriptionId, customerId, subscription_graceperiod } =
        shopSubscriptionStatus[0];

      if (!subscriptionId || !customerId) {
        throw new NotFoundException(
          'Subscription not found for the provided ShopId',
        );
      }

      const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
        expand: ['latest_invoice.payment_intent'],
      });
      const subscriptionItems = subscription.items.data;
      const upcomingInvoice =
        subscription.status !== 'canceled' && subscriptionItems.length > 0
          ? await stripe.invoices.createPreview({
              customer: customerId,
              subscription: subscriptionId,
              expand: ['payment_intent'],
            })
          : null;

      const pastInvoices = await stripe.invoices.list({
        customer: customerId,
        subscription: subscriptionId,
        limit: 12,
        expand: ['data.payment_intent'],
      });

      const customerCharges =
        pastInvoices.data.length > 0 && pastInvoices.data[0].customer
          ? await stripe.charges
              .list({
                customer:
                  typeof pastInvoices.data[0].customer === 'string'
                    ? pastInvoices.data[0].customer
                    : pastInvoices.data[0].customer.id,
                limit: 12,
              })
              .catch(() => ({ data: [] }))
          : { data: [] };

      const getPaymentDetails = async (invoice: Stripe.Invoice) => {
        try {
          const expandedInvoice = invoice as any;
          const pi = expandedInvoice.payment_intent as
            | Stripe.PaymentIntent
            | string
            | undefined;

          // Method 1: PaymentIntent (new API)
          if (pi && typeof pi !== 'string') {
            console.log(
              `Invoice ${invoice.number}: Using PaymentIntent for payment details`,
            );
            const chargeId = pi.latest_charge as string | undefined;
            if (chargeId) {
              const charge = await stripe.charges.retrieve(chargeId);
              return {
                paymentStatus: pi.status,
                cardDetails: charge.payment_method_details?.card && {
                  brand: charge.payment_method_details.card.brand,
                  lastFourDigits: charge.payment_method_details.card.last4,
                },
              };
            }
          }

          // Method 2: Charge (legacy API)
          if (expandedInvoice.charge) {
            console.log(
              `Invoice ${invoice.number}: Using legacy charge property for payment details`,
            );
            const chargeId =
              typeof expandedInvoice.charge === 'string'
                ? expandedInvoice.charge
                : expandedInvoice.charge.id;

            if (chargeId) {
              const charge = await stripe.charges.retrieve(chargeId);
              return {
                paymentStatus: charge.status,
                cardDetails: charge.payment_method_details?.card && {
                  brand: charge.payment_method_details.card.brand,
                  lastFourDigits: charge.payment_method_details.card.last4,
                },
              };
            }
          }

          // Method 3: For paid invoices
          if (invoice.status === 'paid' && customerCharges.data.length > 0) {
            console.log(
              `Invoice ${invoice.number}: Searching in pre-fetched charges`,
            );

            let bestMatch = null;
            let closestTimeDiff = Infinity;

            const candidateCharges = customerCharges.data.filter((charge) => {
              const amountMatches = charge.amount === invoice.amount_paid;
              const statusMatches = charge.status === 'succeeded';
              return amountMatches && statusMatches;
            });

            for (const charge of candidateCharges) {
              const timeDiff = Math.abs(charge.created - invoice.created);
              console.log(
                `Checking charge ${charge.id}: amount(true), status(true), time diff: ${timeDiff}s`,
              );

              if (timeDiff < closestTimeDiff) {
                closestTimeDiff = timeDiff;
                bestMatch = charge;
              }
            }

            // Accept matches within 24 hours
            if (bestMatch && closestTimeDiff <= 86400) {
              console.log(
                `Invoice ${invoice.number}: Found best matching charge ${bestMatch.id} with ${closestTimeDiff}s difference`,
              );
              return {
                paymentStatus: bestMatch.status,
                cardDetails: bestMatch.payment_method_details?.card && {
                  brand: bestMatch.payment_method_details.card.brand,
                  lastFourDigits: bestMatch.payment_method_details.card.last4,
                },
              };
            } else if (bestMatch) {
              console.log(
                `Invoice ${invoice.number}: Found charge ${bestMatch.id} but time difference too large: ${closestTimeDiff}s`,
              );
            }
          }

          console.log(`Invoice ${invoice.number}: No payment details found`);
          return {};
        } catch (error) {
          this.logger.error(
            `Error retrieving payment details for invoice ${invoice.number}:`,
            error,
          );
          return {};
        }
      };

      const selectedData = {
        subscriptionGracePeriod: subscription_graceperiod,
        subscription: {
          startDate: new Date(subscription.start_date * 1000),
          endDate: subscription.cancel_at
            ? new Date(subscription.cancel_at * 1000)
            : null,
          status: subscription.status,
          subscriptionId,
        },
        invoice: upcomingInvoice
          ? {
              amountDue: upcomingInvoice.amount_due / 100,
              dueDate: new Date(
                (subscription as any).current_period_end * 1000,
              ),
            }
          : null,
        pastInvoices: await Promise.all(
          pastInvoices.data.map(async (invoice) => ({
            id: invoice.number,
            status: invoice.status,
            amountPaid: invoice.amount_paid / 100,
            date: new Date(invoice.created * 1000),
            ...(await getPaymentDetails(invoice)),
            pdf: invoice.invoice_pdf,
          })),
        ),
      };

      return selectedData;
    } catch (error) {
      this.logger.error('Error fetching subscription details:', error);
      if (next) {
        return next.handle();
      }
      throw error;
    }
  }

  async updateSubscriptionIds(
    shopId: string,
    subscriptionId: string,
    customerId: string,
  ): Promise<void> {
    const sql = `
      UPDATE "Shops"
      SET stripe_subscription_id = $2,
          stripe_customer_id = $3
      WHERE shop_id = $1
    `;
    const params = [shopId, subscriptionId, customerId];

    await this.databaseService.query(sql, params);
  }

  async setSubscriptionStatusForAllShops(next?: CallHandler): Promise<void> {
    try {
      const shops = await this.getSubscriptionStatus();
      for (const shop of shops) {
        try {
          const subscriptionDetails = await this.getSubscriptionDetails(
            shop.shopId,
          );

          const { subscription, subscriptionGracePeriod, pastInvoices } =
            subscriptionDetails;

          if (
            subscription.status === 'active' ||
            subscription.status === 'incomplete'
          ) {
            await this.updateSubscriptionStatus(shop.shopId, true);
          } else if (
            subscription.status === 'incomplete_expired' ||
            subscription.status === 'canceled' ||
            subscription.status === 'unpaid'
          ) {
            await this.updateSubscriptionStatus(shop.shopId, false);
          } else if (subscription.status === 'past_due') {
            if (pastInvoices.length > 0) {
              const latestInvoice = pastInvoices[0];
              if (latestInvoice.status !== 'paid') {
                const invoiceDateObj = new Date(latestInvoice.date);
                invoiceDateObj.setDate(
                  invoiceDateObj.getDate() + subscriptionGracePeriod,
                );
                invoiceDateObj.setHours(0, 0, 0, 0);
                const currentDate = new Date();
                currentDate.setHours(0, 0, 0, 0);
                if (invoiceDateObj < currentDate) {
                  {
                    await this.updateSubscriptionStatus(
                      shop.shopId,
                      false,
                      subscription?.subscriptionId,
                    );
                  }
                }
              }
            }
          }
        } catch (error) {
          next;
        }
      }
    } catch (error) {
      next;
    }
  }

  async getSubscriptionStatus(shopId?: string): Promise<any[]> {
    let sql = `
            SELECT c.shop_id AS "shopId",
                  c.stripe_subscription_id AS "subscriptionId",
                  c.stripe_customer_id AS "customerId",
                  c.subscription_graceperiod 
            FROM "Shops" c
            WHERE subscription_status = true 
            AND (c.stripe_subscription_id IS NOT NULL AND c.stripe_customer_id IS NOT NULL)`;
    if (shopId) {
      sql += ` AND c.shop_id = $1`;
    }
    return await this.databaseService.query(sql, shopId ? [shopId] : []);
  }

  async updateSubscriptionStatus(
    shopId: string,
    status: boolean,
    subscriptionId?: string,
  ): Promise<void> {
    let sql = `
      UPDATE "Shops"
      SET subscription_status = $2, `;

    if (!status) {
      sql += `
        take_orders = $2, 
        pickup_module = $2, 
      `;
    }

    sql += `
      product_level_status = $2
      WHERE shop_id = $1
    `;
    const params = [shopId, status];
    await this.databaseService.query(sql, params);
    try {
      if (!status && subscriptionId) {
        const stripe = new Stripe(
          shopId === 'Cravin'
            ? process.env.STRIPE_API_KEY
            : process.env.STRIPE_PROD_API_KEY,
        );
        // cancel subscription
        await stripe.subscriptions.cancel(subscriptionId);
      }
    } catch (error) {}
  }
}

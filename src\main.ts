import { NestFactory } from '@nestjs/core';
import { IoAdapter } from '@nestjs/platform-socket.io';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './common/app/app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.enableCors();
  app.useWebSocketAdapter(new IoAdapter(app));

  if (process.env.ENV === 'production') {
    app.setGlobalPrefix('commerce/api/v1');
  }

  const options = new DocumentBuilder()
    .setTitle('Cravin Shop API')
    .setDescription('Allow users to perform all the functionalities')
    .setVersion('1.0')
    .addBearerAuth(
      {
        description: `Please enter token in following format: Bearer JWT`,
        name: 'Authorization',
        bearerFormat: 'Bearer',
        scheme: 'Bearer',
        type: 'http',
        in: 'Header',
      },
      'access-token',
    )
    .build();

  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('api', app, document, {
    swaggerOptions: {
      security: [{ 'access-token': [] }],
    },
  });

  await app.listen(process.env.PORT);
}
bootstrap();

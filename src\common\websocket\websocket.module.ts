import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { AuthModule } from '../auth/auth.module';
import { WsJwtGuard } from '../auth/guards/ws-jwt.guard';
import { RedisModule } from '../redis/redis.module';
import { UserTrackingService } from './user-tracking.service';
import { CustomWebSocketGateway } from './websocket.gateway';

@Module({
  imports: [
    forwardRef(() => AuthModule),
    RedisModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: '30d' },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [CustomWebSocketGateway, UserTrackingService, WsJwtGuard],
  exports: [CustomWebSocketGateway, UserTrackingService],
})
export class WebSocketModule {}

import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { UserTrackingService } from '../websocket/user-tracking.service';

@Injectable()
export class UserTrackingCronService {
  private readonly logger = new Logger(UserTrackingCronService.name);

  constructor(private readonly userTrackingService: UserTrackingService) {}

  @Cron(CronExpression.EVERY_5_MINUTES)
  async cleanupOfflineUser() {
    try {
      this.logger.log('Starting cleanup of offline users...');
      await this.userTrackingService.cleanupOfflineUsers(5);
      this.logger.log('Completed cleanup of offline users');
    } catch (error) {
      this.logger.error('Error during offline users cleanup:', error.message);
    }
  }

  @Cron(CronExpression.EVERY_HOUR)
  async performHealthCheck() {
    try {
      const health = await this.userTrackingService.healthCheck();
      if (!health.redis || !health.service) {
        this.logger.warn('Health check failed:', health);
      } else {
        this.logger.log('Health check passed');
      }
    } catch (error) {
      this.logger.error('Error during health check:', error.message);
    }
  }
}

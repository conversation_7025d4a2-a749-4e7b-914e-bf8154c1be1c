import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { BranchesService } from '../../modules/branches/branches.service';
import { BreakTimings } from '../../modules/branches/dto/branches.dto';
import { SubscriptionService } from '../utilities/subscriptions';
import { CustomerQueryService } from './../../modules/customers/customer-queries';

@Injectable()
export class CronService {
  private readonly logger = new Logger(CronService.name);

  constructor(
    private readonly customerQueryService: CustomerQueryService,
    private readonly branchesService: BranchesService,
    private readonly subscriptionService: SubscriptionService,
  ) {}

  @Cron(CronExpression.EVERY_10_MINUTES)
  async markOrderAsDelivered(): Promise<void> {
    try {
      await this.customerQueryService.markOrderAsDelivered();
    } catch (error) {
      this.logger.error('Error during order delivery:', error.message);
    }
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async autoRejectOrders(): Promise<void> {
    try {
      await this.customerQueryService.autoRejectOrders();
    } catch (error) {
      this.logger.error('Error during auto rejecting orders:', error.message);
    }
  }

  @Cron(CronExpression.EVERY_HOUR)
  async deactivatePaymentLinks(): Promise<void> {
    try {
      const deactivatedCount =
        await this.customerQueryService.deactivatePaymentLinks();
      if (deactivatedCount > 0) {
        this.logger.log(
          `Deactivated payment links for ${deactivatedCount} orders`,
        );
      }
    } catch (error) {
      this.logger.error('Error during deactivating links:', error.message);
    }
  }

  @Cron(CronExpression.EVERY_2_HOURS)
  async deleteProcessingOrders(): Promise<void> {
    try {
      const deletedOrders =
        await this.customerQueryService.deleteProcessingOrders();
      if (deletedOrders > 0) {
        this.logger.log(`Deleted ${deletedOrders} processing orders`);
      }
    } catch (error) {
      this.logger.error(
        'Error during deleting processing orders:',
        error.message,
      );
    }
  }
  @Cron(CronExpression.EVERY_10_MINUTES)
  async checkAndSetSubscriptionStatus() {
    try {
      await this.subscriptionService.setSubscriptionStatusForAllShops();
    } catch (error) {
      this.logger.error(
        'Error during subscription status update:',
        error.message,
      );
    }
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async toggleBranchStatus() {
    try {
      const branches = await this.branchesService.getAllBranches();

      for (const branch of branches) {
        const {
          break_status_switch,
          status,
          pickup_status,
          break_override_until,
          branch_timezone,
        } = branch;

        const { time: currentTime } =
          await this.customerQueryService.getCurrentTimeForBranch(
            null,
            branch_timezone,
          );
        const currentDate = new Date(currentTime);

        if (!break_status_switch && !status && !pickup_status) continue;

        if (break_override_until) {
          const breakOverrideUntil = new Date(break_override_until);
          if (currentDate < breakOverrideUntil) {
            continue;
          }

          await this.branchesService.updateBranchBreakOverride(
            branch.branch_id,
          );
        }

        const breakTimings: BreakTimings[] = branch.break_timings || [];
        const currentDay = currentDate.getDay();

        const dayTiming = breakTimings.find(
          (timing: BreakTimings) => timing.day === currentDay && timing.status,
        );

        if (!dayTiming?.breaks?.[0]) continue;

        let isBreakTime = false;
        const breaks = dayTiming.breaks[0];

        for (const breakKey of ['break1', 'break2', 'break3'] as const) {
          const breakTiming = breaks[breakKey];
          if (!breakTiming?.start || !breakTiming?.end) continue;

          try {
            const [startHours, startMinutes] =
              await this.branchesService.parseTime(breakTiming.start);
            const [endHours, endMinutes] = await this.branchesService.parseTime(
              breakTiming.end,
            );

            const currentHours = currentDate.getHours();
            const currentMinutes = currentDate.getMinutes();

            const isAfterStart =
              currentHours > startHours ||
              (currentHours === startHours && currentMinutes >= startMinutes);

            const isBeforeEnd =
              currentHours < endHours ||
              (currentHours === endHours && currentMinutes <= endMinutes);

            if (isAfterStart && isBeforeEnd) {
              isBreakTime = true;
              break;
            }
          } catch (error) {
            this.logger.error(
              `Error processing break ${breakKey} for branch ${branch.branch_id}:`,
              error.message,
            );
            continue;
          }
        }

        if (branch.break_status_switch) {
          await this.branchesService.updateBranchOrderStatus(
            branch.branch_id,
            !isBreakTime,
          );
        }
      }
    } catch (error) {
      this.logger.error('Error during toggling branch status:', error.message);
    }
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async restockOutOfStockItems() {
    try {
      const restockedItems =
        await this.customerQueryService.restockOutOfStockItems();
      if (restockedItems > 0) {
        this.logger.log(`Restocked ${restockedItems} items`);
      }
    } catch (error) {
      this.logger.error('Failed to restock items:', error);
    }
  }
}

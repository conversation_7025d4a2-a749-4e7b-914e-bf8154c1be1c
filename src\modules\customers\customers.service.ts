import { Injectable, NotFoundException } from '@nestjs/common';
import booleanPointInPolygon from '@turf/boolean-point-in-polygon';
import distance from '@turf/distance';
import { point, polygon } from '@turf/helpers';
import { addDays, isAfter, isBefore, isEqual, parse } from 'date-fns';
import { format } from 'date-fns-tz';
import { DatabaseService } from '../../common/config/database.services';
import { Address } from '../branches/dto/branches.dto';
import { CustomerQueryService } from './customer-queries';
import { CustomerOrderReview } from './dto/customers.dto';

@Injectable()
export class CustomersService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly customerQueryService: CustomerQueryService,
  ) {}

  async addCustomerAddress(
    customerId: string,
    addressTag: string,
    address: Address,
  ): Promise<any[]> {
    const serializedAddress = JSON.stringify(address);

    const currentAddressesQuery = `
        SELECT customer_address
        FROM public."Customers"
        WHERE customer_id = $1
    `;
    const currentAddressesResult = await this.databaseService.query(
      currentAddressesQuery,
      [customerId],
    );
    const currentAddresses = currentAddressesResult[0]?.customer_address || {};

    const updatedAddresses = {
      ...currentAddresses,
      [addressTag]: JSON.parse(serializedAddress),
    };

    let updateQuery = `
        UPDATE public."Customers"
        SET customer_address = $1
    `;
    const updateParams = [JSON.stringify(updatedAddresses), customerId];

    if (Object.keys(currentAddresses).length === 0) {
      updateQuery += `, customer_favourite_address_tag = $3`;
      updateParams.push(addressTag);
    }

    updateQuery += ` WHERE customer_id = $2`;

    try {
      await this.databaseService.query(updateQuery, updateParams);
      const data = await this.getCustomerAddresses(customerId);
      const addressArray = Object.keys(data).map((tag: string) => {
        const address = data[tag];
        return {
          deliverToKey: tag,
          deliverToObj: address,
        };
      });

      return addressArray;
    } catch (error) {
      throw new Error('Failed to add address for customer');
    }
  }

  async deleteCustomerAddressTag(
    customerId: string,
    addressTag: string,
  ): Promise<void> {
    const currentAddressesQuery = `
        SELECT customer_address, customer_favourite_address_tag
        FROM public."Customers"
        WHERE customer_id = $1
    `;
    const currentAddressesResult = await this.databaseService.query(
      currentAddressesQuery,
      [customerId],
    );
    const currentAddresses = currentAddressesResult[0]?.customer_address || {};
    const favouriteAddressTag =
      currentAddressesResult[0]?.customer_favourite_address_tag;

    if (!currentAddresses.hasOwnProperty(addressTag)) {
      throw new NotFoundException('The tag does not exist for the customer');
    }

    delete currentAddresses[addressTag];

    let updateQuery = `
        UPDATE public."Customers"
        SET customer_address = $1
    `;
    const updateParams = [JSON.stringify(currentAddresses), customerId];

    if (favouriteAddressTag === addressTag) {
      updateQuery += `, customer_favourite_address_tag = NULL`;
    }

    updateQuery += ` WHERE customer_id = $2`;

    try {
      await this.databaseService.query(updateQuery, updateParams);
    } catch (error) {
      throw new Error('Failed to delete address tag for customer');
    }
  }

  async getCustomerAddresses(customerId: string): Promise<string[]> {
    try {
      const sql = `
        WITH address_data AS (
          SELECT
            jsonb_object_keys(customer_address) AS tag,
            customer_address,
            customer_favourite_address_tag
          FROM
            public."Customers"
          WHERE
            customer_id = $1
        ),
        prioritized_addresses AS (
          SELECT
            tag,
            customer_address->tag AS address,
            customer_favourite_address_tag,
            CASE
              WHEN tag = customer_favourite_address_tag THEN 1
              WHEN tag = 'home' THEN 2
              WHEN tag = 'work' THEN 3
              WHEN tag = 'hotel' THEN 4
              ELSE 5
            END AS priority
          FROM
            address_data
        )
        SELECT
          json_object_agg(tag, 
            jsonb_build_object('google_address', address->>'google_address',
                              'sector', address->>'sector',
                              'building', address->>'building',
                              'landmark', address->>'landmark',
                              'latitude', (address->>'latitude')::numeric,
                              'longitude', (address->>'longitude')::numeric,
                              'recipient_name', address->>'recipient_name',
                              'recipient_contact', address->>'recipient_contact',
                              'favourite', 
                                CASE WHEN tag = customer_favourite_address_tag THEN true ELSE false END
            ) ORDER BY priority) AS addresses
        FROM (
          SELECT
            tag,
            address,
            customer_favourite_address_tag,
            priority
          FROM
            prioritized_addresses
        ) AS subquery
        GROUP BY
          customer_favourite_address_tag
      `;
      const params = [customerId];
      const result = await this.databaseService.query(sql, params);

      const addresses = result[0]?.addresses || [];
      return addresses;
    } catch (error) {
      throw new Error('Failed to fetch customer addresses');
    }
  }

  async updateFavouriteAddressTag(
    customerId: string,
    favouriteAddressTag: string,
  ): Promise<void> {
    try {
      const sql = `
        UPDATE public."Customers"
        SET customer_favourite_address_tag = $1
        WHERE customer_id = $2
      `;
      const params = [favouriteAddressTag, customerId];
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to update customer favourite address');
    }
  }

  async getAllCustomers(
    shopId: string,
    branchIds: string[] | null,
    customerType: string,
    allowNull: boolean,
  ): Promise<
    {
      customer_id: string;
      customer_name: string;
      phone_number: string;
      email_address: string | null;
      gender: string | null;
      nationality: string | null;
      customer_address: object;
      total_orders: string;
      total_spending: string;
      broadcast: boolean;
      branch_names?: string;
    }[]
  > {
    try {
      let sql = `
        WITH filtered_orders AS (
          SELECT DISTINCT o.order_id, o.fk_customer_id, (o.bill->>'total_bill')::numeric AS total_bill
          FROM public."ShopOrders" o
          WHERE o.status IN ('delivered', 'picked_up')
          AND o.fk_shop_id = $1
          AND o.fk_branch_id = ANY($2::text[])
        ), customer_branches AS (
          SELECT cr.fk_customer_id, array_agg(DISTINCT b.branch_name) AS branch_names
          FROM public."Customer_Shops" cr
          LEFT JOIN public."ShopBranches" b ON b.branch_id = ANY(cr.branch_ids)
          WHERE cr.fk_shop_id = $1
          AND (${allowNull ? 'cr.branch_ids IS NULL OR' : ''} cr.branch_ids::text[] && $2::text[])
          GROUP BY cr.fk_customer_id
        )
        SELECT c.customer_id,
          COALESCE(cr.customer_name, c.customer_name) AS customer_name,
          c.phone_number,
          c.email_address,
          c.gender,
          c.nationality,
          CASE
            WHEN c.customer_address IS NOT NULL THEN jsonb_agg(DISTINCT 
              jsonb_build_object(
                'address', 
                  (addr.value->>'sector')::text || ', ' || 
                  (addr.value->>'building')::text || ', ' || 
                  (addr.value->>'google_address')::text,
                'landmark', (addr.value->>'landmark')::text,
                'latitude', (addr.value->>'latitude')::numeric,
                'longitude', (addr.value->>'longitude')::numeric,
                'recipient_name', (addr.value->>'recipient_name')::text,
                'recipient_contact', (addr.value->>'recipient_contact')::text,
                'type', addr.key::text,
                'is_customer_favourite', (addr.key = c.customer_favourite_address_tag)::boolean
              )
            )
            ELSE NULL
          END AS customer_address,
          COUNT(DISTINCT fo.order_id)::int AS total_orders,
          ROUND(COALESCE(SUM(DISTINCT fo.total_bill), 0), 2)::real AS total_spending,
          CASE
            WHEN cr.broadcast = true THEN 'yes'
            ELSE 'no'
          END AS broadcast,
          cb.branch_names,cr.created_at
        FROM public."Customers" c
        JOIN public."Customer_Shops" cr ON c.customer_id = cr.fk_customer_id
        LEFT JOIN filtered_orders fo ON cr.fk_customer_id = fo.fk_customer_id
        LEFT JOIN customer_branches cb ON c.customer_id = cb.fk_customer_id
        LEFT JOIN LATERAL (select * from jsonb_each(c.customer_address)) addr ON true
        WHERE cr.fk_shop_id = $1
        AND (${allowNull ? 'cr.branch_ids IS NULL OR' : ''} cr.branch_ids::text[] && $2::text[])
      `;
      if (customerType === 'opencart') {
        sql += `
          AND c.customer_id
          IN (
            SELECT fk_customer_id 
            FROM public."CustomerAnalytics"
            WHERE fk_shop_id = $1    
            AND type = 'OPEN_CART'
            AND fk_branch_id = ANY($2::text[])
          )`;
      }
      sql += `  GROUP BY c.customer_id, cr.customer_name, c.customer_name, c.phone_number, c.customer_address, cr.broadcast, cb.branch_names,cr.created_at`;
      if (customerType !== 'all') {
        if (customerType === 'potential') {
          sql += ` HAVING COUNT(DISTINCT fo.order_id) = 0`;
        } else if (customerType === 'active') {
          sql += ` HAVING COUNT(DISTINCT fo.order_id) > 0`;
        }
      }

      const params =
        branchIds && branchIds.length > 0 ? [shopId, branchIds] : [shopId];
      return this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error(
        'Failed to fetch customers by specified shops and branch IDs',
      );
    }
  }

  async getCustomerLatestOrder(
    shopId: string,
    customerId: string,
    orderId?: string | null,
  ): Promise<any> {
    let sql = `
      SELECT
        o.order_id,
        o.fk_branch_id AS branch_id,
        CASE 
          WHEN sr.fk_order_id IS NOT NULL THEN true 
          WHEN (CURRENT_TIMESTAMP - o.modified_at) > INTERVAL '2 days' THEN true
          ELSE false 
        END AS "is_reviewed",
        b.cancellation_number AS contact_number
      FROM
        public."ShopOrders" o
      LEFT JOIN
        public."ShopReviews" sr ON o.order_id = sr.fk_order_id AND sr.fk_customer_id = o.fk_customer_id
      LEFT JOIN
        public."ShopBranches" b ON b.branch_id = o.fk_branch_id
      WHERE
        o.fk_shop_id = $1
      AND
        o.fk_customer_id = $2
      AND
        o.status IN ('delivered', 'picked_up')`;

    const params = [shopId, customerId];
    if (orderId) {
      sql += `
      AND
        o.order_id = $3`;
      params.push(orderId);
    }

    if (!orderId) {
      sql += `
      ORDER BY
        o.modified_at DESC
      LIMIT 1`;
    }

    try {
      const result = await this.databaseService.query(sql, params);
      return result;
    } catch (error) {
      throw new Error('Failed to fetch last order for customer');
    }
  }

  async addCustomerOrderReview(
    orderReview: CustomerOrderReview,
  ): Promise<void> {
    const {
      customer_id,
      shop_id,
      branch_id,
      order_id,
      order_name,
      order_rating,
      item_ratings,
      review_text,
    } = orderReview;

    try {
      const addReviewSql = `
        INSERT INTO public."ShopReviews" (
          fk_customer_id,
          fk_shop_id,
          fk_branch_id,
          fk_order_id,
          order_name,
          order_rating,
          item_ratings,
          review_text
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `;

      const params = [
        customer_id,
        shop_id,
        branch_id,
        order_id,
        order_name,
        order_rating,
        JSON.stringify(item_ratings),
        review_text,
      ];

      await this.databaseService.query(addReviewSql, params);

      if (
        item_ratings &&
        Array.isArray(item_ratings) &&
        item_ratings.length > 0
      ) {
        for (const ratingEntry of item_ratings) {
          if (ratingEntry.item_rating === 0) {
            continue;
          }

          const updateRatingSql = `
            UPDATE public."ShopItems"
            SET 
              rating_sum = COALESCE(rating_sum, 0) + $1,
              rating_count = COALESCE(rating_count, 0) + 1
            WHERE item_id = $2
          `;

          await this.databaseService.query(updateRatingSql, [
            ratingEntry.item_rating,
            ratingEntry.item_id,
          ]);
        }
      }
    } catch (error) {
      throw new Error(`Failed to add customer order review: ${error}`);
    }
  }

  async getCustomerOrderHistory(
    shopId: string,
    customerId: string,
    branchId: string | null = null,
  ): Promise<any[]> {
    try {
      let sql = `
        WITH preferred_customer AS (
          SELECT
            c.customer_id,
            COALESCE(cr.customer_name, c.customer_name) AS customer_name,
            ROW_NUMBER() OVER (PARTITION BY c.customer_id ORDER BY cr.customer_name IS NULL) AS rn
          FROM
            public."Customers" c
          LEFT JOIN
            public."Customer_Shops" cr ON cr.fk_customer_id = c.customer_id
          AND (
            ($3::varchar = ANY(cr.branch_ids))
            AND cr.fk_shop_id = $1
          )
        )
        SELECT
          pc.customer_name,
          o.order_name AS order_id, 
          o.created_at,
          o.items,
          o.order_type,
          CONCAT_WS(', ', 
            o.customer_address->>'sector', 
            o.customer_address->>'building', 
            o.customer_address->>'landmark', 
            o.customer_address->>'google_address'
          ) AS address,
          o.bill->>'total_bill' AS total_bill,
          o.payment_method`;

      if (!branchId) {
        sql += `,
          b.branch_name`;
      }

      sql += `
        FROM public."ShopOrders" o
        LEFT JOIN public."Customers" c ON o.fk_customer_id = c.customer_id
        LEFT JOIN preferred_customer pc ON c.customer_id = pc.customer_id AND pc.rn = 1`;

      if (!branchId) {
        sql += `
            LEFT JOIN public."ShopBranches" b ON o.fk_branch_id = b.branch_id`;
      }

      sql += `
        WHERE o.fk_shop_id = $1
        AND o.fk_customer_id = $2
        AND o.status IN ('delivered', 'cancelled', 'picked_up')`;

      if (branchId) {
        sql += `
            AND o.fk_branch_id = $3`;
      }

      sql += ` ORDER BY o.created_at DESC`;

      const params = [shopId, customerId, branchId];
      const result = await this.databaseService.query(sql, params);
      return result;
    } catch (error) {
      throw new Error('Failed to get customer order history');
    }
  }

  async getDiscounts(data: {
    shop_id: string;
    branch_id: string;
    customer_id: string;
    date: string;
    service_type: string;
    promo_code?: string;
  }): Promise<object[]> {
    const {
      shop_id,
      branch_id,
      customer_id,
      date,
      service_type = 'delivery',
      promo_code,
    } = data;

    const possibleMethodUsage = promo_code
      ? ['promo', 'auto', 'manual']
      : ['auto', 'manual'];

    const discountsQueryBase = `
      SELECT DISTINCT
        fd.discount_id,
        fd.discount_name,
        fd.discount_code,
        fd.usage_method,
        fd.discount_type,
        fd.discount_value,
        fd.min_amount,
        fd.max_amount,
        fd.discount_date::jsonb,
        fd.discount_duration_day
      FROM public."ShopDiscounts" fd
      LEFT JOIN public."Customer_ShopDiscounts" cfd ON fd.discount_id = cfd.fk_discount_id
      AND cfd.fk_customer_id = $3 
      WHERE
        fd.fk_shop_id = $1
        AND $2 = ANY(fd.branch_ids)
        AND fd.status = true
        AND (
          fd.applicable_customer = 'all'
          OR (
            fd.applicable_customer = 'new'
            AND NOT EXISTS (
              SELECT 1
              FROM public."Customer_ShopDiscounts" cfd2
              WHERE
                cfd2.fk_customer_id = $3
                AND cfd2.fk_discount_id = fd.discount_id
            )
          )
        )
        AND fd.discount_date->>'start_date' IS NOT NULL
        AND TO_TIMESTAMP(fd.discount_date->>'start_date', 'DD-MM-YYYY') <= TO_TIMESTAMP($4, 'DD-MM-YYYY')
        AND (
          fd.discount_date->>'end_date' IS NULL
          OR (
            TO_TIMESTAMP(fd.discount_date->>'end_date', 'DD-MM-YYYY') >= TO_TIMESTAMP($4, 'DD-MM-YYYY')
            AND TO_TIMESTAMP(fd.discount_date->>'start_date', 'DD-MM-YYYY') <= TO_TIMESTAMP($4, 'DD-MM-YYYY')
          )
        )
        AND (
          (fd.total_uses IS NULL OR fd.actual_usage < fd.total_uses)
          AND COALESCE(cfd.no_of_usage, 0) < COALESCE(fd.uses_per_customer, 0)
        )
        AND fd.usage_method = ANY($5)
        AND $6::varchar = ANY(fd.applicable_order_type)
    `;

    const discountsQuery = promo_code
      ? `${discountsQueryBase} AND fd.discount_code = $7`
      : discountsQueryBase;

    const queryParams = promo_code
      ? [
          shop_id,
          branch_id,
          customer_id,
          date,
          possibleMethodUsage,
          service_type,
          promo_code,
        ]
      : [
          shop_id,
          branch_id,
          customer_id,
          date,
          possibleMethodUsage,
          service_type,
        ];

    try {
      const result = await this.databaseService.query(
        discountsQuery,
        queryParams,
      );
      const convertedResult = result.map((item) => ({
        ...item,
        discount_value: +item.discount_value,
        min_amount: item.min_amount ? +item.min_amount : null,
        max_amount: item.max_amount ? +item.max_amount : null,
      }));

      return convertedResult;
    } catch (error) {
      throw new Error('Error fetching discounts');
    }
  }

  async getDeliverableBranches(
    shopId: string,
    customerLocation: {
      latitude: number;
      longitude: number;
      service_type?: string;
    },
  ): Promise<any[]> {
    try {
      let sql = `
        SELECT
          dz.zone_id,
          dz.fk_branch_id,
          dz.coordinates,
          dz.delivery_fee,
          dz.min_cart_amount,
          b.branch_name,
          b.branch_location,
          b.branch_address,`;
      
      if (customerLocation.service_type === 'delivery') {
        sql += `
        CASE 
          WHEN s.product_level_status = true 
          AND s.take_orders = true 
          AND b.branch_delivery_status = true 
          AND b.status = true 
          THEN true ELSE false 
        END AS branch_status, `;
      }

      if (customerLocation.service_type === 'pickup') {
        sql += `
        CASE 
          WHEN s.product_level_status = true 
          AND s.pickup_module = true 
          AND b.branch_pickup_status = true 
          AND b.pickup_status = true 
          THEN true ELSE false 
        END AS branch_status, `;
      }

      sql += `
          b.break_status_switch,
          b.branch_timings,
          b.break_timings,
          b.branch_timezone 
        FROM
          public."ShopDeliveryZones" dz
        JOIN
          public."ShopBranches" b ON dz.fk_branch_id = b.branch_id
        LEFT JOIN 
          public."Shops" s ON b.fk_shop_id = s.shop_id
        LEFT JOIN
          public."ShopBranchOrdering" sbo
            ON dz.fk_branch_id = sbo.fk_branch_id AND dz.fk_shop_id = sbo.fk_shop_id
        WHERE
          dz.fk_shop_id = $1
          AND dz.status = true
      `;

      if (customerLocation.service_type === 'delivery') {
        sql += `
          AND b.branch_delivery = true`;
      }

      if (customerLocation.service_type === 'pickup') {
        sql += `
          AND b.branch_pickup = true`;
      }

      sql += `
        ORDER BY
          sbo.display_order NULLS LAST`;

      const params = [shopId];
      const branches = await this.databaseService.query(sql, params);
      const { latitude, longitude } = customerLocation;
      const deliverabilityMap = new Map();

      for (const branch of branches) {
        const { time } =
          await this.customerQueryService.getCurrentTimeForBranch(
            null,
            branch.branch_timezone,
          );
        const currentDate = new Date(time);
        const currentDay = currentDate.getDay();

        const branchTimings = branch.branch_timings;
        let isBranchOpen = false;
        let reopenTime = null;
        let reopenDayNumber = null;
        let reopenDay = null;
        let daysUntilReopen = null;

        const todayTiming = branchTimings.find(
          (timing: { day: number }) => timing.day === currentDay,
        );

        // Handle the case where yesterday's schedule extends past midnight into today
        const yesterdayDay = (currentDay + 6) % 7; // Previous day
        const yesterdayTiming = branchTimings.find(
          (timing: { day: number }) => timing.day === yesterdayDay,
        );

        // Check if yesterday's schedule extends past midnight and we're still in that period
        if (yesterdayTiming && yesterdayTiming.status) {
          const currentTime = format(currentDate, 'h:mm a');
          const currentTimeObj = parse(currentTime, 'h:mm a', currentDate);
          const yesterdayStartTime = parse(
            yesterdayTiming.start,
            'h:mm a',
            currentDate,
          );
          const yesterdayEndTime = parse(
            yesterdayTiming.end,
            'h:mm a',
            currentDate,
          );

          // Only consider yesterday's hours if they extend past midnight
          if (
            isBefore(yesterdayEndTime, yesterdayStartTime) &&
            (isBefore(currentTimeObj, yesterdayEndTime) ||
              isEqual(currentTimeObj, yesterdayEndTime))
          ) {
            isBranchOpen = true;
          }
        }

        // Check if branch is open during today's timings (if not already open from yesterday)
        if (!isBranchOpen && todayTiming && todayTiming.status) {
          const startTime = parse(todayTiming.start, 'h:mm a', currentDate);
          const endTime = parse(todayTiming.end, 'h:mm a', currentDate);
          const currentTime = format(currentDate, 'h:mm a');
          const currentTimeObj = parse(currentTime, 'h:mm a', currentDate);

          // For normal hours (not crossing midnight)
          if (!isBefore(endTime, startTime)) {
            isBranchOpen =
              (isAfter(currentTimeObj, startTime) &&
                isBefore(currentTimeObj, endTime)) ||
              isEqual(currentTimeObj, startTime) ||
              isEqual(currentTimeObj, endTime);
          }
          // For hours crossing midnight, only check if we're after the start time
          else {
            isBranchOpen =
              isAfter(currentTimeObj, startTime) ||
              isEqual(currentTimeObj, startTime);
          }
        }

        // First check if today's branch opens later today
        if (todayTiming && todayTiming.status) {
          const startTime = parse(todayTiming.start, 'h:mm a', currentDate);
          const currentTime = format(currentDate, 'h:mm a');
          const currentTimeObj = parse(currentTime, 'h:mm a', currentDate);

          if (isAfter(startTime, currentTimeObj)) {
            reopenTime = todayTiming.start;
            reopenDayNumber = currentDay;
          }
        }

        // If no reopenTime set yet, find the next open day
        if (!reopenTime) {
          // Start looking from tomorrow
          let nextOpenDay = (currentDay + 1) % 7;
          let daysChecked = 0;

          // Look through the next 7 days to find the next open day
          while (daysChecked < 7) {
            const nextDayTiming = branchTimings.find(
              (timing: { day: number }) => timing.day === nextOpenDay,
            );

            if (nextDayTiming && nextDayTiming.status) {
              // Found the next open day
              reopenTime = nextDayTiming.start;
              reopenDayNumber = nextOpenDay;
              break;
            }

            // Check the next day
            nextOpenDay = (nextOpenDay + 1) % 7;
            daysChecked++;
          }
        }

        // Calculate days until reopen and set the reopen day name using date-fns
        if (reopenDayNumber !== null) {
          // Create a date for the reopen day
          const reopenDate = new Date(currentDate);

          // Calculate days until reopen
          if (reopenDayNumber === currentDay) {
            daysUntilReopen = 0; // Same day
            reopenDay = format(reopenDate, 'EEEE'); // Full day name
          } else {
            // Add the difference in days to get to reopen day
            const daysToAdd = (reopenDayNumber - currentDay + 7) % 7;
            // If reopenDayNumber is before currentDay in the week, it means it's next week
            daysUntilReopen = daysToAdd === 0 ? 7 : daysToAdd;

            // Add the days to the current date to get the reopen date
            const reopenFullDate = addDays(reopenDate, daysUntilReopen);
            reopenDay = format(reopenFullDate, 'EEEE'); // Full day name
          }
        }

        const branchId = branch.fk_branch_id;

        const branchObject = {
          branch_id: branchId,
          branch_name: branch.branch_name,
          min_cart_amount: branch.min_cart_amount,
          branch_location: branch.branch_location ?? null,
          branch_address: branch.branch_address ?? null,
          branch_status: branch.branch_status,
          break_flag: branch.break_status_switch,
          is_branch_open: isBranchOpen,
          reopen_time: reopenTime,
          reopen_day: reopenDay,
          days_until_reopen: daysUntilReopen,
          break_timings: branch.break_timings,
        };

        if (!latitude || !longitude) {
          if (!deliverabilityMap.has(branchId)) {
            deliverabilityMap.set(branchId, {
              ...branchObject,
              deliverable: true,
              distance: null,
            });
          }
          continue;
        }

        const userLocation = point([longitude, latitude]);
        const zoneCoordinates = branch.coordinates.map((vertex) => [
          vertex.longitude,
          vertex.latitude,
        ]);
        zoneCoordinates.push(zoneCoordinates[0]);
        const zonePolygon = polygon([zoneCoordinates]);

        const branchLocation = point([
          branch.branch_location.longitude,
          branch.branch_location.latitude,
        ]);
        const dist = distance(userLocation, branchLocation, {
          units: 'kilometres',
        });

        const isDeliverable = booleanPointInPolygon(userLocation, zonePolygon);

        if (!deliverabilityMap.has(branchId) || isDeliverable) {
          deliverabilityMap.set(branchId, {
            ...branchObject,
            deliverable: isDeliverable,
            distance: dist,
          });
        }
      }

      const branchesArray = Array.from(deliverabilityMap.values());
      branchesArray.sort((a, b) => {
        if (a.branch_status !== b.branch_status) {
          return a.branch_status ? -1 : 1;
        }
        if (a.is_branch_open !== b.is_branch_open) {
          return a.is_branch_open ? -1 : 1;
        }
        if (a.deliverable !== b.deliverable) {
          return a.deliverable ? -1 : 1;
        }
        return (a.distance ?? Infinity) - (b.distance ?? Infinity);
      });

      return branchesArray;
    } catch (error) {
      throw new Error('Deliverable branches for customer location failed');
    }
  }

  async getDeliveryDetails(
    shopId: string,
    branchId: string,
    customerId: string,
    serviceType: string,
  ): Promise<any> {
    try {
      const customerSql = `
        SELECT customer_address
        FROM public."Customers"
        WHERE customer_id = $1
      `;

      const customerParams = [customerId];
      const customerResult = await this.databaseService.query(
        customerSql,
        customerParams,
      );

      if (customerResult.length === 0) {
        return 'Customer not found';
      }

      const customerAddresses = customerResult[0].customer_address;

      let branchSql = `
        SELECT
          dz.zone_id,
          dz.coordinates,
          dz.delivery_fee,
          dz.min_cart_amount,
          b.branch_name,
          b.branch_location,
          b.branch_timings,
          b.break_timings,
          b.branch_address,
          b.branch_display_name,
          b.break_status_switch, `;

      if (serviceType === 'delivery') {
        branchSql += `
          b.branch_delivery AS module_status, 
          CASE 
            WHEN s.product_level_status = true 
            AND s.take_orders = true 
            AND b.branch_delivery_status = true 
            AND b.branch_delivery = true 
            AND b.status = true 
            THEN true 
        `;
      }

      if (serviceType === 'pickup') {
        branchSql += `
          b.branch_pickup AS module_status, 
          CASE 
            WHEN s.product_level_status = true 
            AND s.pickup_module = true 
            AND b.branch_pickup_status = true 
            AND b.branch_pickup = true 
            AND b.pickup_status = true 
            THEN true 
        `;
      }

      branchSql += `
            ELSE false
          END as branch_status
        FROM
          public."ShopDeliveryZones" dz
        JOIN
          public."ShopBranches" b ON dz.fk_branch_id = b.branch_id
        JOIN
          public."Shops" s ON dz.fk_shop_id = s.shop_id
        WHERE
          dz.fk_shop_id = $1
          AND dz.fk_branch_id = $2
          AND dz.status = true
      `;

      const branchParams = [shopId, branchId];
      const branchResult = await this.databaseService.query(
        branchSql,
        branchParams,
      );

      if (branchResult.length === 0) {
        return 'Branch or delivery zones not found for the given shop and branch';
      }

      const branchDetails = branchResult[0];
      const zones = branchResult.map((zone) => ({
        zone_id: zone.zone_id,
        coordinates: zone.coordinates,
        delivery_fee: zone.delivery_fee,
        min_cart_amount: zone.min_cart_amount,
      }));

      const deliverableAddresses = {};

      const checkDeliverability = (address) => {
        const userLocation = point([address.longitude, address.latitude]);
        let deliverable = false;
        let minDeliveryFee = Infinity;
        let minZoneId = null;
        let minCartAmount = null;

        for (const zone of zones) {
          const zoneCoordinates = zone.coordinates.map((vertex) => [
            vertex.longitude,
            vertex.latitude,
          ]);

          zoneCoordinates.push(zoneCoordinates[0]);

          const zonePolygon = polygon([zoneCoordinates]);

          if (booleanPointInPolygon(userLocation, zonePolygon)) {
            deliverable = true;
            if (zone.delivery_fee < minDeliveryFee) {
              minDeliveryFee = zone.delivery_fee;
              minZoneId = zone.zone_id;
              minCartAmount = zone.min_cart_amount;
            }
          }
        }

        return { deliverable, minDeliveryFee, minZoneId, minCartAmount };
      };

      for (const [addressType, address] of Object.entries(customerAddresses)) {
        const { deliverable, minDeliveryFee, minZoneId, minCartAmount } =
          checkDeliverability(address);

        deliverableAddresses[addressType] = {
          sector: address['sector'],
          building: address['building'],
          landmark: address['landmark'],
          latitude: address['latitude'],
          longitude: address['longitude'],
          google_address: address['google_address'],
          recipient_name: address['recipient_name'],
          recipient_contact: address['recipient_contact'],
          favourite: address['favourite'],
          deliverable,
          delivery_fee: deliverable ? +minDeliveryFee : null,
          zoneId: deliverable ? minZoneId : null,
          min_cart_amount: deliverable ? +minCartAmount : null,
        };
      }

      return {
        addresses: deliverableAddresses,
        branch_timings: branchDetails.branch_timings,
        branch_address: branchDetails.branch_address,
        branch_display_name: branchDetails.branch_display_name,
        break_timings: branchDetails.break_timings,
        break_flag: branchDetails.break_status_switch,
        take_orders: branchDetails.branch_status,
        module_status: branchDetails.module_status,
      };
    } catch (error) {
      throw new Error('Deliverable addresses for customer location failed');
    }
  }

  async editCustomerName(
    fk_customer_id: string,
    shopId: string,
    new_customer_name: string,
  ) {
    const sql = `
      UPDATE public."Customer_Shops"
      SET customer_name = $1
      WHERE fk_customer_id = $2 AND fk_shop_id = $3
    `;

    await this.databaseService.query(sql, [
      new_customer_name,
      fk_customer_id,
      shopId,
    ]);
  }

  async getCustomerInfo(customer_id: string) {
    const sql = `
      SELECT customer_id, customer_name,  
        email_address,  
        gender, nationality
      FROM public."Customers"
      WHERE customer_id=$1;
    `;

    return await this.databaseService.query(sql, [customer_id]);
  }

  async updateCustomerProfile(customerBody: {
    customerId: string;
    customerName: string;
    customerEmail: string;
    gender: string;
    nationality: string;
  }) {
    const { customerId, customerName, customerEmail, gender, nationality } =
      customerBody;
    const sql = `
      UPDATE public."Customers"
      SET 
        customer_name=$2,
        email_address=$3,
        gender=$4,
        nationality=$5
      WHERE  customer_id=$1;
    `;

    await this.databaseService.query(sql, [
      customerId,
      customerName,
      customerEmail,
      gender,
      nationality,
    ]);
  }
}

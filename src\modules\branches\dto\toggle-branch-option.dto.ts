import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsString } from 'class-validator';

export class ToggleBranchOptionDto {
  @ApiProperty({ description: 'Branch ID' })
  @IsNotEmpty()
  @IsString()
  branchId: string;

  @ApiProperty({ description: 'Status to set (true/false)' })
  @IsNotEmpty()
  @IsBoolean()
  status: boolean;

  @ApiProperty({
    description: 'Option to toggle (branch_delivery or branch_pickup)',
    enum: ['branch_delivery', 'branch_pickup'],
  })
  @IsNotEmpty()
  @IsString()
  option: 'branch_delivery' | 'branch_pickup';
}

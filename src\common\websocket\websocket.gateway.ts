import { Injectable, Logger, UseGuards } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { AuthService } from '../auth/auth.service';
import { WsJwtGuard } from '../auth/guards/ws-jwt.guard';
import { UserTrackingService } from './user-tracking.service';

@WebSocketGateway({
  name: '',
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
    credentials: true,
  },
  namespace:
    process.env.ENV === 'production'
      ? '/commerce/api/v1/user-tracking'
      : '/user-tracking',
  path:
    process.env.ENV === 'production'
      ? '/commerce/api/v1/socket.io'
      : '/socket.io',
})
@Injectable()
export class CustomWebSocketGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(WebSocketGateway.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly userTrackingService: UserTrackingService,
    private readonly authService: AuthService,
  ) {}

  afterInit() {
    this.logger.log('WebSocket Gateway initialized');
  }

  async handleConnection(client: Socket) {
    try {
      const token = this.extractTokenFromSocket(client);
      if (!token) {
        this.logger.warn(`Client ${client.id} connected without token`);
        client.disconnect();
        return;
      }

      const payload = await this.jwtService.verifyAsync(token);
      const userId = payload.sub || payload.id || payload.userId;
      const userType = payload.userType;

      if (!userId) {
        this.logger.warn(`Invalid token for client ${client.id}`);
        client.disconnect();
        return;
      }

      const deviceId = client.handshake.auth?.deviceId;
      if (!deviceId) {
        this.logger.warn(`Client ${client.id} connected without deviceId`);
        client.disconnect();
        return;
      }

      const userDetails =
        await this.authService.getUserDetailsForTracking(userId);
      if (!userDetails) {
        this.logger.warn(`User not found for client ${client.id}`);
        client.disconnect();
        return;
      }
      const { username, shop_id, branch_id, branch_name } = userDetails;
      const wasOffline = !(await this.userTrackingService.isUserOnline(userId));

      client.data.userId = userId;
      client.data.deviceId = deviceId;
      client.data.username = username;
      client.data.shopId = shop_id;
      client.data.branchId = branch_id;
      client.data.user = payload;

      client.join(`user_${userId}`);
      client.join(`shop_${shop_id}`);
      client.join(`device_${deviceId}`);

      const { isFirstLogin } = await this.userTrackingService.trackUserActivity(
        userId,
        deviceId,
        {
          username,
          userType,
          shopId: shop_id,
          branchId: branch_id,
          branchName: branch_name,
          userAgent: client.handshake.headers['user-agent'],
          ipAddress: client.handshake.address,
        },
      );

      this.logger.log(
        `User ${username} (${userId}) connected with socket ${client.id} from device ${deviceId}${isFirstLogin ? ' (first login)' : ' (returning)'}`,
      );

      client.emit('connection_status', {
        status: 'connected',
        message: 'Successfully connected to user tracking',
        timestamp: new Date(),
        username,
        shopId: shop_id,
        branchId: branch_id,
        deviceId: deviceId,
      });

      if (wasOffline) {
        this.broadcastUserStatus(userId, 'online', username, userType);
      }

      if (isFirstLogin) {
        this.broadcastShopSessionChange(
          shop_id,
          {
            userId,
            username,
            deviceId,
            shopId: shop_id,
            branchId: branch_id,
            branchName: branch_name,
            loginTime: new Date(),
            userType,
          },
          'connected',
        );
      }
    } catch (error) {
      this.logger.error(
        `Connection error for client ${client.id}:`,
        error.message,
      );
      client.disconnect();
    }
  }

  async handleDisconnect(client: Socket) {
    try {
      const userId = client.data?.userId;
      const username = client.data?.username;
      const shopId = client.data?.shopId;
      const branchId = client.data?.branchId;
      const branchName = client.data?.branchName;
      const deviceId = client.data?.deviceId;
      const userType = client.data?.user?.userType;

      if (userId && deviceId) {
        await this.userTrackingService.handleDeviceDisconnect(
          userId,
          deviceId,
          username,
          shopId,
          branchId,
          branchName,
        );

        const isStillOnline =
          await this.userTrackingService.isUserOnline(userId);
        if (!isStillOnline) {
          this.broadcastUserStatus(userId, 'offline', username, userType);
        }
      }
    } catch (error) {
      this.logger.error(
        `Disconnect error for client ${client.id}:`,
        error.message,
      );
    }
  }

  @SubscribeMessage('ping')
  @UseGuards(WsJwtGuard)
  handlePing(@ConnectedSocket() client: Socket): void {
    const userId = client.data?.userId;
    if (userId) {
      this.userTrackingService.updateLastSeen(userId);
    }
    client.emit('pong', { timestamp: new Date() });
  }

  @SubscribeMessage('get_online_users')
  @UseGuards(WsJwtGuard)
  async handleGetOnlineUsers(@ConnectedSocket() client: Socket) {
    try {
      const shopId = client.data?.shopId;
      const onlineUsers =
        await this.userTrackingService.getOnlineUsersByShop(shopId);
      client.emit('online_users', onlineUsers);
    } catch (error) {
      client.emit('error', { message: 'Failed to fetch online users' });
    }
  }

  @SubscribeMessage('get_shop_sessions')
  @UseGuards(WsJwtGuard)
  async handleGetShopActivity(@ConnectedSocket() client: Socket) {
    try {
      const shopId = client.data?.shopId;
      const activity = await this.userTrackingService.getShopActivity(shopId);
      client.emit('shop_sessions', activity);
    } catch (error) {
      client.emit('error', { message: 'Failed to fetch shop activity' });
    }
  }

  @SubscribeMessage('get_user_activity')
  @UseGuards(WsJwtGuard)
  async handleGetUserActivity(
    @MessageBody() data: { targetUserId: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const activity = await this.userTrackingService.getUserActivity(
        data.targetUserId,
      );
      client.emit('user_activity', activity);
    } catch (error) {
      client.emit('error', { message: 'Failed to fetch user activity' });
    }
  }

  async notifyUser(userId: string, event: string, data: any) {
    this.server.to(`user_${userId}`).emit(event, data);
  }

  async broadcastToShopUsers(shopId: string, event: string, data: any) {
    this.server.to(`shop_${shopId}`).emit(event, data);
  }

  private extractTokenFromSocket(client: Socket): string | null {
    const authHeader = client.handshake.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    const token = client.handshake.query.token;
    if (typeof token === 'string') {
      return token;
    }

    const auth = client.handshake.auth;
    if (auth && auth.token) {
      return auth.token;
    }

    return null;
  }

  private async broadcastUserStatus(
    userId: string,
    status: 'online' | 'offline',
    username?: string,
    userType?: string,
  ) {
    if (userType && userType.toLowerCase() === 'owner') {
      this.logger.debug(
        `Skipping status broadcast for owner user: ${username} (${userId})`,
      );
      return;
    }

    this.server.emit('user_status_change', {
      userId,
      username,
      status,
      timestamp: new Date(),
    });
  }

  public broadcastShopSessionChange(
    shopId: string,
    sessionData: any,
    changeType: 'connected',
  ) {
    this.server.to(`shop_${shopId}`).emit('shop_session_change', {
      sessionData,
      changeType,
      timestamp: new Date(),
      shopId,
    });
  }
}

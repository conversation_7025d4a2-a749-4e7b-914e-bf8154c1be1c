import {
  BadRequestException,
  Body,
  ConflictException,
  Controller,
  Delete,
  Get,
  HttpStatus,
  InternalServerErrorException,
  NotFoundException,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { SubscriptionService } from 'src/common/utilities/subscriptions';
import { JwtAuthGuard } from '../../common/auth/guards/jwt-auth.guard';
import { Public } from '../../common/decorators/public.decorator';
import {
  CreateShopOnboardingDto,
  LandingPageDto,
  UpdateShopBranchesDto,
} from './dto/shop-onboarding.dto';
import { CreateShopDto, UpdateSubscriptionDto } from './dto/shops.dto';
import { ToggleVisibilityDto } from './dto/toggle-visibility.dto';
import { ShopsService } from './shops.service';

@ApiTags('Shops')
@Controller('shops')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('access-token')
export class ShopsController {
  constructor(
    private readonly shopsService: ShopsService,
    private readonly subscriptionService: SubscriptionService,
  ) {}

  @Public()
  @Get('details/:id')
  @ApiOperation({
    summary: 'Get shop name and phone details by shop ID',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: 'Successful retrieval of shop details',
  })
  async getShopPhoneDetails(@Param('id') id: string): Promise<object> {
    try {
      return await this.shopsService.getShopPhoneById(id);
    } catch (error) {
      throw new Error('Failed to get shop phone details');
    }
  }

  @Post()
  @ApiOperation({ summary: 'Add a new shop' })
  @ApiResponse({ status: 201, description: 'Shop added successfully' })
  @ApiBadRequestResponse({ description: 'Invalid shop data' })
  @ApiBody({ type: CreateShopDto })
  async addShop(@Body() shopData: CreateShopDto): Promise<void> {
    try {
      await this.shopsService.addShop(shopData);
    } catch (error) {
      throw new Error('Failed to add shop');
    }
  }

  @Post(':shopId/edit-shop-name')
  @ApiOperation({
    summary: 'Edit shop name by shop ID',
  })
  @ApiParam({
    name: 'shopId',
    type: 'string',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        shopName: {
          type: 'string',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Successful updated the shop name',
  })
  async editShopName(
    @Param('shopId') shopId: string,
    @Body() body: { shopName: string },
  ): Promise<void> {
    try {
      const { shopName } = body;
      await this.shopsService.editShopName(shopId, shopName);
    } catch (error) {
      throw new Error('Failed to update shop name');
    }
  }

  @Get(':shopId/order-policy')
  @ApiOperation({ summary: 'Get order policy by shop ID' })
  @ApiResponse({
    status: 200,
    description: 'Order policy retrieved successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid request' })
  @ApiParam({ name: 'shopId', description: 'Shop ID' })
  async getShopOrderPolicy(@Param('shopId') shopId: string): Promise<any[]> {
    try {
      return await this.shopsService.getShopOrderPolicy(shopId);
    } catch (error) {
      throw new Error('Failed to get order policy');
    }
  }

  @Get(':shopId/branches')
  @ApiOperation({ summary: 'Get branch names by shop ID' })
  @ApiResponse({
    status: 200,
    description: 'Branch names retrieved successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid request' })
  @ApiParam({ name: 'shopId', description: 'Shop ID' })
  async getBranchNames(@Param('shopId') shopId: string): Promise<any[]> {
    try {
      return await this.shopsService.getBranchNames(shopId);
    } catch (error) {
      throw new Error('Failed to get branch names');
    }
  }

  @Get(':shopId/menu')
  @ApiOperation({
    summary: 'Get shop menu by shop ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Shop menu retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Branch not found' })
  @ApiParam({
    name: 'shopId',
    type: String,
  })
  @ApiQuery({
    name: 'branchId',
    type: String,
    required: false,
  })
  async getShopMenuItems(
    @Param('shopId') shopId: string,
    @Query('branchId') branchId?: string,
  ): Promise<any> {
    try {
      const branchData = await this.shopsService.getShopMenuItems(
        shopId,
        branchId,
      );
      if (!branchData) {
        throw new Error('Branch not found');
      }
      return branchData;
    } catch (error) {
      throw new Error('Failed to fetch shop data');
    }
  }

  @Get(':shopId/orders/past')
  @ApiOperation({
    summary: 'Get shop past orders by shop ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Shop past orders retrieved successfully',
  })
  @ApiQuery({
    name: 'branchId',
    type: String,
    required: false,
  })
  async getShopPastOrders(
    @Param('shopId') shopId: string,
    @Query('branchId') branchId?: string,
  ): Promise<any> {
    try {
      const branchData = await this.shopsService.getShopPastOrders(
        shopId,
        branchId,
      );
      if (!branchData) {
        throw new Error('Branch not found');
      }
      return branchData;
    } catch (error) {
      throw new Error('Failed to fetch shop data');
    }
  }

  
  @Get('order-reviews/:shopId')
  @ApiOperation({ summary: 'Get order reviews by customers' })
  @ApiResponse({
    status: 200,
    description: 'Customer order reviews retrieved successfully',
  })
  @ApiParam({
    name: 'shopId',
    type: 'string',
    description: 'The ID of the shop',
  })
  @ApiQuery({
    name: 'branchId',
    type: 'string',
    description: 'The ID of the branch',
    required: false
  })
  async getCustomerOrderReviews(
    @Param('shopId') shopId: string,
    @Query('branchId') branchId?: string,
  ): Promise<object[]> {
    try {
      const orderReviews = await this.shopsService.getCustomerOrderReviews(
        shopId,
        branchId,
      );
      return orderReviews;
    } catch (error) {
      throw new Error('Unable to fetch customer reviews');
    }
  }

  @Get(':shopId/sales')
  @ApiOperation({
    summary: 'Get shop sales data by shop ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Shop sales data retrieved successfully',
  })
  @ApiQuery({
    name: 'branchId',
    type: String,
    required: false,
  })
  @ApiQuery({
    name: 'orderType',
    type: String,
    enum: ['all', 'delivery', 'pickup'],
    required: false,
  })
  @ApiQuery({
    name: 'paymentMode',
    type: String,
    enum: ['cash', 'card', 'online'],
    required: false,
  })
  @ApiQuery({
    name: 'dateOption',
    type: String,
    enum: [
      'today',
      'last_seven_days',
      'last_thirty_days',
      'last_ninety_days',
      'date_range',
    ],
    required: true,
    description:
      'Date filter option: today, last_seven_days, last_thirty_days, last_ninety_days, date_range',
  })
  @ApiQuery({
    name: 'fromDate',
    type: String,
    required: false,
    description: 'Start date in DD-MM-YYYY format',
  })
  @ApiQuery({
    name: 'toDate',
    type: String,
    required: false,
    description: 'End date in DD-MM-YYYY format',
  })
  async getShopSales(
    @Param('shopId') shopId: string,
    @Query('branchId') branchId?: string,
    @Query('orderType') orderType?: string,
    @Query('paymentMode') paymentMode?: string,
    @Query('dateOption') dateOption?: string,
    @Query('fromDate') fromDate?: string,
    @Query('toDate') toDate?: string,
  ): Promise<any> {
    try {
      const dateRange =
        fromDate && toDate ? { from: fromDate, to: toDate } : undefined;
      const salesData = await this.shopsService.getShopSales(
        shopId,
        branchId,
        orderType,
        paymentMode,
        dateOption,
        dateRange,
      );
      if (!salesData) {
        throw new Error('Sales data not found');
      }
      return salesData;
    } catch (error) {
      throw new Error('Failed to fetch shop sales data');
    }
  }

  @Get(':shopId/analytics')
  @ApiOperation({
    summary: 'Get shop analytics data by shop ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Shop analytics data retrieved successfully',
  })
  @ApiQuery({
    name: 'branchId',
    type: String,
    required: false,
  })
  @ApiQuery({
    name: 'type',
    type: String,
    enum: [
      'statistics',
      'top_customers',
      'top_selling_items',
      'top_locations',
      'payment_mode',
      'peak_hours',
      'top_added_items',
      'total_users_landed',
      'total_users_texted',
      'top_viewed_items',
      'order_count',
    ],
    required: true,
  })
  @ApiQuery({
    name: 'date_option',
    type: String,
    enum: [
      'today',
      'week',
      'month',
      'year',
      'date_range',
      'last_seven_days',
      'last_thirty_days',
      'last_ninety_days',
      'last_24_hours',
    ],
    required: true,
    description: 'Date option for analytics',
  })
  @ApiQuery({
    name: 'date_range.from',
    type: String,
    required: false,
    description: 'Start date of the date range (DD-MM-YYYY)',
  })
  @ApiQuery({
    name: 'date_range.to',
    type: String,
    required: false,
    description: 'End date of the date range (DD-MM-YYYY)',
  })
  @ApiQuery({
    name: 'time_zone',
    type: String,
    required: false,
    description: 'Time zone, defaults to Asia/Dubai (GMT +4)',
  })
  @ApiQuery({
    name: 'order_by',
    type: String,
    enum: ['order_based', 'sales_based'],
    required: false,
    description: 'Order of customers',
  })
  async getShopAnalyticsCount(
    @Param('shopId') shopId: string,
    @Query('type')
    type:
      | 'statistics'
      | 'top_customers'
      | 'top_selling_items'
      | 'top_locations'
      | 'payment_mode'
      | 'peak_hours'
      | 'top_added_items'
      | 'total_users_landed'
      | 'total_users_texted'
      | 'top_viewed_items'
      | 'order_count',
    @Query('date_option')
    dateOption:
      | 'today'
      | 'week'
      | 'month'
      | 'year'
      | 'date_range'
      | 'last_seven_days'
      | 'last_thirty_days'
      | 'last_ninety_days'
      | 'last_24_hours',
    @Query('branchId') branchId?: string,
    @Query('date_range.from') dateRangeFrom?: string,
    @Query('date_range.to') dateRangeTo?: string,
    @Query('time_zone') timeZone?: string,
    @Query('order_by') orderBy?: 'order_based' | 'sales_based',
  ): Promise<any> {
    try {
      let dateRange: { from: string; to: string };

      if (dateOption === 'date_range') {
        dateRange = { from: dateRangeFrom, to: dateRangeTo };
      }
      const branchData = await this.shopsService.getShopAnalyticsCount(
        shopId,
        branchId,
        type,
        dateOption,
        timeZone,
        dateRange,
        orderBy,
      );
      if (!branchData) {
        throw new Error('No data available');
      }
      return branchData;
    } catch (error) {
      throw new Error('Failed to fetch shop data');
    }
  }

  @Get('customer-url/:shopId')
  @ApiOperation({
    summary: 'Get customer url by shop ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Customer url  retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Customer url not found' })
  async getCustomerUrlData(@Param('shopId') shopId: string): Promise<any> {
    try {
      const branchData = await this.shopsService.getCustomerUrlData(shopId);
      if (!branchData) {
        throw new Error('Customer url not found');
      }
      return branchData;
    } catch (error) {
      throw new Error('Failed to fetch Customer url  data');
    }
  }

  @Public()
  @Get('orders/:orderId/details')
  @ApiOperation({ summary: 'Get order details for customer order summary' })
  @ApiResponse({
    status: 200,
    description: 'Order status fetched successfully',
  })
  @ApiParam({ name: 'orderId', description: 'Order ID' })
  async getOrderDetails(@Param('orderId') orderId: string): Promise<any[]> {
    try {
      const orderDetails = await this.shopsService.getOrderDetails(orderId);
      return orderDetails;
    } catch (error) {
      throw new Error('Failed to get order details for customer');
    }
  }
  @Get(':shopId/cancellations')
  @ApiOperation({
    summary: 'Get shop cancellations data by shop ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Shop cancellations data retrieved successfully',
  })
  @ApiQuery({
    name: 'branchId',
    type: String,
    required: false,
  })
  @ApiQuery({
    name: 'paymentMode',
    type: String,
    enum: ['cash', 'card', 'online'],
    required: false,
  })
  @ApiQuery({
    name: 'dateOption',
    type: String,
    enum: [
      'today',
      'week',
      'month',
      'date_range',
      'last_thirty_days',
      'last_ninety_days',
      'last_seven_days',
    ],
    required: true,
    description: 'Date filter option: week, month, date_range',
  })
  @ApiQuery({
    name: 'fromDate',
    type: String,
    required: false,
    description: 'Start date in DD-MM-YYYY format',
  })
  @ApiQuery({
    name: 'toDate',
    type: String,
    required: false,
    description: 'End date in DD-MM-YYYY format',
  })
  async getShopCancellations(
    @Param('shopId') shopId: string,
    @Query('branchId') branchId?: string,
    @Query('paymentMode') paymentMode?: string,
    @Query('dateOption') dateOption?: string,
    @Query('fromDate') fromDate?: string,
    @Query('toDate') toDate?: string,
  ): Promise<any> {
    try {
      const dateRange =
        fromDate && toDate ? { from: fromDate, to: toDate } : undefined;
      const cancellationData = await this.shopsService.getShopCancellations(
        shopId,
        branchId,
        paymentMode,
        dateOption,
        dateRange,
      );
      if (!cancellationData) {
        throw new Error('cancellationData data not found');
      }
      return cancellationData;
    } catch (error) {
      throw new Error('Failed to fetch shop cancellationData data');
    }
  }
  @Get(':shopId/subscription')
  @ApiOperation({ summary: 'Get subscription and invoice details by shop Id' })
  @ApiParam({ name: 'shopId', description: 'shop Id' })
  @ApiResponse({
    status: 200,
    description: 'Subscription and invoice details fetched successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Invalid shopId to fetch subscription',
  })
  @ApiResponse({
    status: 404,
    description: 'Subscription not found for the provided shopId',
  })
  async getSubscriptionDetailsByshopId(
    @Param('shopId') shopId: string,
  ): Promise<any> {
    const details =
      await this.subscriptionService.getSubscriptionDetails(shopId);
    if (!details) {
      throw new NotFoundException(
        'Subscription not found for the provided shopId',
      );
    }
    return details;
  }

  @Put(':shopId/subscription')
  @ApiOperation({ summary: 'Update subscription IDs by shop Id' })
  @ApiParam({ name: 'shopId', description: 'shop Id' })
  @ApiBody({ type: UpdateSubscriptionDto })
  @ApiResponse({
    status: 200,
    description: 'Subscription IDs updated successfully',
  })
  async updateSubscriptionIdsByshopId(
    @Param('shopId') shopId: string,
    @Body() updateSubscriptionDto: UpdateSubscriptionDto,
  ): Promise<void> {
    const { subscriptionId, customerId } = updateSubscriptionDto;
    await this.subscriptionService.updateSubscriptionIds(
      shopId,
      subscriptionId,
      customerId,
    );
  }

  @Get(':shopId/subscription-status')
  @ApiOperation({ summary: 'Get shop subscription status' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Get shop subscription status',
    type: Boolean,
  })
  @ApiParam({
    name: 'shopId',
    description: 'ID of the shop',
    type: String,
  })
  async getShopSubscriptionStatus(
    @Param('shopId') shopId: string,
  ): Promise<{ subscription_status: boolean }> {
    return this.shopsService.getShopSubscriptionStatus(shopId);
  }

  @Get('status/:shopId')
  @ApiOperation({ summary: 'Get shop status' })
  @ApiResponse({
    status: 200,
    description: 'Shop status fetched successfully',
  })
  @ApiParam({ name: 'shopId', description: 'Shop ID' })
  @ApiQuery({ name: 'serviceType', description: 'Service Type' })
  async getShopStatus(
    @Param('shopId') shopId: string,
    @Query('serviceType') serviceType: string,
  ): Promise<any[]> {
    try {
      const status = await this.shopsService.getShopStatus(shopId, serviceType);
      return status;
    } catch (error) {
      throw new Error('Failed to get shop status');
    }
  }

  @Patch('status/:shopId')
  @ApiOperation({ summary: 'Update shop status' })
  @ApiResponse({
    status: 200,
    description: 'Shop status updated successfully',
  })
  @ApiParam({ name: 'shopId', description: 'Shop ID' })
  @ApiQuery({ name: 'serviceType', description: 'Service Type' })
  @ApiBody({
    schema: { type: 'object', properties: { status: { type: 'boolean' } } },
  })
  async toggleShopStatus(
    @Param('shopId') shopId: string,
    @Query('serviceType') serviceType: string,
    @Body('status') status: boolean,
  ): Promise<void> {
    try {
      await this.shopsService.updateShopStatus(shopId, status, serviceType);
    } catch (error) {
      throw new Error('Failed to update shop status');
    }
  }

  @Get('branch-info/:shopId')
  @ApiOperation({ summary: 'Get branch status by branch ID' })
  @ApiResponse({
    status: 200,
    description: 'Branch info status fetched successfully',
  })
  @ApiParam({ name: 'shopId', description: 'Shop ID' })
  @ApiQuery({ name: 'serviceType', description: 'Service Type' })
  @ApiQuery({ name: 'branchId', required: false, description: 'Branch ID' })
  async getBranchToggleStatus(
    @Param('shopId') shopId: string,
    @Query('serviceType') serviceType: string,
    @Query('branchId') branchId?: string,
  ): Promise<any[]> {
    try {
      const status = await this.shopsService.getBranchInfoStatus(
        shopId,
        serviceType,
        branchId,
      );
      return status;
    } catch (error) {
      throw new Error('Failed to get branch info status');
    }
  }

  @Patch('branch-info/:branchId')
  @ApiOperation({ summary: 'Update branch status by branch ID' })
  @ApiResponse({
    status: 200,
    description: 'Branch status updated successfully',
  })
  @ApiParam({ name: 'branchId', description: 'Branch ID' })
  @ApiQuery({ name: 'serviceType', description: 'Service Type' })
  @ApiBody({
    schema: { type: 'object', properties: { status: { type: 'boolean' } } },
  })
  async toggleBranchInfoStatus(
    @Param('branchId') branchId: string,
    @Query('serviceType') serviceType: string,
    @Body('status') status: boolean,
  ): Promise<void> {
    try {
      await this.shopsService.updateBranchInfoStatus(
        branchId,
        serviceType,
        status,
      );
    } catch (error) {
      throw new Error('Failed to update branch info status');
    }
  }

  @Public()
  @Get('landing-page/listing-page-details')
  @ApiOperation({
    summary: 'Get shops listing details for listing page',
    description:
      'Returns all shops or those within a specified radius from given coordinates',
  })
  @ApiQuery({
    name: 'pageNumber',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    type: Number,
    description: 'Page size (defaults to 12)',
  })
  @ApiQuery({
    name: 'latitude',
    required: false,
    type: Number,
    description: 'User latitude coordinate for distance filtering',
  })
  @ApiQuery({
    name: 'longitude',
    required: false,
    type: Number,
    description: 'User longitude coordinate for distance filtering',
  })
  @ApiQuery({
    name: 'radius',
    required: false,
    type: Number,
    description: 'Search radius in kilometres (default: 10)',
  })
  @ApiQuery({
    name: 'emirate',
    required: false,
    type: String,
    description: 'Filter by branch emirate',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description:
      'Search query for shop name, branch name and branch tags',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successful retrieval of shops listing details',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Shops not found',
  })
  async getShopListingPageDetails(
    @Query('pageNumber') pageNumber: number = 1,
    @Query('pageSize') pageSize: number = 12,
    @Query('latitude') latitude?: string,
    @Query('longitude') longitude?: string,
    @Query('radius') radius?: string,
    @Query('emirate') emirate?: string,
    @Query('search') searchParams?: string,
  ) {
    const userLatitude = latitude ? parseFloat(latitude) : undefined;
    const userLongitude = longitude ? parseFloat(longitude) : undefined;
    const radiusInKm = radius ? parseFloat(radius) : 10;

    if (
      (latitude && isNaN(userLatitude)) ||
      (longitude && isNaN(userLongitude))
    ) {
      throw new Error('Invalid coordinates provided');
    }

    const details = await this.shopsService.getShopListingPageDetails(
      +pageNumber,
      +pageSize,
      userLatitude,
      userLongitude,
      radiusInKm,
      emirate,
      searchParams,
    );

    if (!details) {
      throw new NotFoundException('Shops not found');
    }
    return details;
  }

  @Public()
  @Get('onboarding/:shopId')
  @ApiOperation({
    summary: 'Get complete shop details by ID',
  })
  @ApiParam({
    name: 'shopId',
    type: 'string',
    description: 'Shop ID',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Shop details retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Shop not found',
  })
  async getShopOnboardingDetailsById(@Param('shopId') shopId: string) {
    const shop = await this.shopsService.getShopOnboardingDetailsById(shopId);
    if (!shop) {
      throw new NotFoundException('Shop not found');
    }
    return shop;
  }

  @Public()
  @Post('onboard-new-shop')
  @ApiOperation({
    summary: 'Create a new shop',
    description: 'Creates a new shop',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Shop created successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request payload',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Shop with this ID already exists',
  })
  async createShopOnboarding(
    @Body() createShopOnboardingDto: CreateShopOnboardingDto,
  ) {
    try {
      return await this.shopsService.createShopOnboarding(
        createShopOnboardingDto,
      );
    } catch (error) {
      if (error.message.includes('already exists')) {
        throw new ConflictException(error.message);
      }
      throw new BadRequestException(`Failed to create shop: ${error.message}`);
    }
  }

  @Public()
  @Post('onboard-new-shop/update-details')
  @ApiOperation({
    summary: 'Update shop  details',
    description: 'Updates existing  details of a shop',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Shop  details updated successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request payload',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Shop not found',
  })
  async updateShopBranches(@Body() updateBranchesDto: UpdateShopBranchesDto) {
    try {
      return await this.shopsService.updateShopBranches(updateBranchesDto);
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new NotFoundException(error.message);
      }
      throw new BadRequestException(
        `Failed to update shop branches: ${error.message}`,
      );
    }
  }

  @Public()
  @Get('landing-page-listing-details/:shopId')
  @ApiOperation({
    summary: 'Get Shop onboarding listing details',
    description: 'Fetches the onboarding listing details for a specific Shop',
  })
  @ApiParam({
    name: 'shopId',
    type: 'string',
    description: 'Shop ID',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Shop listing details retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Shop listing details not found',
  })
  async getOnboardingListingDetails(@Param('shopId') shopId: string) {
    try {
      const listingDetails =
        await this.shopsService.getOnboardingListingDetails(shopId);
      if (!listingDetails) {
        throw new NotFoundException('Shop listing details not found');
      }
      return listingDetails;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch listing details: ${error.message}`,
      );
    }
  }
  @Post('landing-page-listing/:shopId')
  @ApiOperation({
    summary: 'Save Shop landing page details',
    description: 'Saves the landing page details for a specific Shop branch',
  })
  @ApiParam({
    name: 'shopId',
    type: 'string',
    description: 'Shop ID',
  })
  @ApiBody({ type: LandingPageDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Landing page details saved successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  async saveLandingPageDetails(
    @Param('shopId') shopId: string,
    @Body() landingPageData: LandingPageDto,
  ) {
    try {
      const result = await this.shopsService.saveLandingPageDetails(
        shopId,
        landingPageData,
      );
      return result;
    } catch (error) {
      throw new BadRequestException(
        `Failed to save landing page details: ${error.message}`,
      );
    }
  }

  @Get('onboarding-dashboard/shops-list')
  @ApiOperation({
    summary: 'Get list of all Shops',
    description: 'Retrieves a list of all Shops with basic information',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of Shops retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', example: 'r001' },
          shopId: { type: 'string', example: 'al-manaar' },
          name: { type: 'string', example: 'Al Manaar shop' },
          phone: { type: 'string', example: '+971 55 123 4567' },
          isWhatsappOnboardingCompleted: { type: 'boolean', example: true },
          addedOn: {
            type: 'string',
            format: 'date-time',
            example: '2023-10-01T12:00:00Z',
          },
          is_only_for_listing: { type: 'boolean', example: true },
          isVisible: {
            type: 'boolean',
            example: true,
            description:
              'Indicates if the shop is visible (product_level_status)',
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Failed to retrieve Shops list',
  })
  async getShopsList(): Promise<any[]> {
    try {
      return await this.shopsService.getShopsList();
    } catch (error) {
      throw new BadRequestException(
        `Failed to fetch Shops list: ${error.message}`,
      );
    }
  }

  @Delete('delete/:shopId')
  @ApiOperation({
    summary: 'Delete a shop and all related data',
    description:
      'Deletes a shop and all its related data including branches, orders, etc.',
  })
  @ApiParam({
    name: 'shopId',
    type: 'string',
    description: 'Shop ID to delete',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Shop deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Shop not found',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Failed to delete shop',
  })
  async deleteShop(
    @Param('shopId') shopId: string,
  ): Promise<{ message: string }> {
    try {
      await this.shopsService.deleteShop(shopId);
      return { message: 'Shop deleted successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to delete shop: ${error.message}`,
      );
    }
  }

  @Patch('toggle-visibility/:shopId')
  @ApiOperation({
    summary: 'Toggle visibility of a shop and all its branches',
    description:
      'Updates the product_level_status, take_orders, and related branch statuses',
  })
  @ApiParam({
    name: 'shopId',
    type: 'string',
    description: 'Shop ID to toggle visibility',
  })
  @ApiBody({ type: ToggleVisibilityDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Shop visibility toggled successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Shop not found',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Failed to toggle shop visibility',
  })
  async toggleShopVisibility(
    @Param('shopId') shopId: string,
    @Body() toggleVisibilityDto: ToggleVisibilityDto,
  ): Promise<{ message: string }> {
    try {
      await this.shopsService.toggleShopVisibility(
        shopId,
        toggleVisibilityDto.status,
      );
      return {
        message: `Shop visibility ${toggleVisibilityDto.status ? 'enabled' : 'disabled'} successfully`,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to toggle shop visibility: ${error.message}`,
      );
    }
  }
}

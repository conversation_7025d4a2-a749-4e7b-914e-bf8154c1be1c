import {
  Injectable,
  Logger,
  On<PERSON><PERSON>ule<PERSON><PERSON><PERSON>,
  OnModuleInit,
} from '@nestjs/common';
import { Redis } from 'ioredis';

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RedisService.name);
  private client: Redis;

  async onModuleInit() {
    try {
      this.client = new Redis({
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT),
        password: process.env.REDIS_PASSWORD,
        username: process.env.REDIS_USERNAME,
        maxRetriesPerRequest: 3,
        enableReadyCheck: false,
        lazyConnect: true,
        keepAlive: 30000,
        tls: process.env.REDIS_TLS === 'true' ? {} : undefined,
      });

      await this.client.connect();
      this.logger.log('Connected to Redis Cloud successfully');
    } catch (error) {
      this.logger.error('Failed to connect to Redis:', error.message);
      throw error;
    }
  }

  async onModuleDestroy() {
    if (this.client) {
      this.client.disconnect();
      this.logger.log('Disconnected from Redis');
    }
  }

  private getOnlineUserKey(userId: string): string {
    return `shop_online:${userId}`;
  }

  private getLastSeenKey(userId: string): string {
    return `shop_last_seen:${userId}`;
  }

  private getFirstLoginKey(
    userId: string,
    deviceId: string,
    shopId: string,
  ): string {
    return `first_shop_login:${shopId}:${userId}:${deviceId}`;
  }

  private getUserFirstLoginsKey(userId: string): string {
    return `shop_user_first_logins:${userId}`;
  }

  private getShopFirstLoginsKey(shopId: string): string {
    return `shop_first_logins:${shopId}`;
  }

  private getOnlineUsersSetKey(): string {
    return 'shop_online_users_set';
  }

  private getShopDevicesKey(shopId: string): string {
    return `shop_devices:${shopId}`;
  }

  private getTodayFirstLoginsKey(shopId: string): string {
    const today = new Date().toISOString().split('T')[0];
    return `today_first_shop_logins:${shopId}:${today}`;
  }

  async isFirstDeviceLogin(
    userId: string,
    deviceId: string,
    shopId: string,
  ): Promise<boolean> {
    const key = this.getFirstLoginKey(userId, deviceId, shopId);
    const exists = await this.client.exists(key);
    return exists === 0;
  }

  async recordFirstLogin(
    userId: string,
    deviceId: string,
    shopId: string,
    metadata: any,
  ): Promise<void> {
    const firstLoginKey = this.getFirstLoginKey(userId, deviceId, shopId);
    const userFirstLoginsKey = this.getUserFirstLoginsKey(userId);
    const shopFirstLoginsKey =
      this.getShopFirstLoginsKey(shopId);
    const shopDevicesKey = this.getShopDevicesKey(shopId);
    const todayKey = this.getTodayFirstLoginsKey(shopId);

    const loginData = {
      userId,
      deviceId,
      shopId,
      ...metadata,
      firstLoginTime: metadata.firstLoginTime.toISOString(),
    };

    const pipeline = this.client.pipeline();
    pipeline.setex(firstLoginKey, 30 * 24 * 60 * 60, '1');

    pipeline.zadd(userFirstLoginsKey, Date.now(), JSON.stringify(loginData));
    pipeline.expire(userFirstLoginsKey, 30 * 24 * 60 * 60);

    pipeline.zadd(
      shopFirstLoginsKey,
      Date.now(),
      JSON.stringify(loginData),
    );
    pipeline.expire(shopFirstLoginsKey, 30 * 24 * 60 * 60);

    pipeline.sadd(shopDevicesKey, `${userId}:${deviceId}`);
    pipeline.expire(shopDevicesKey, 30 * 24 * 60 * 60);

    pipeline.incr(todayKey);
    pipeline.expire(todayKey, 24 * 60 * 60);

    await pipeline.exec();
  }

  private getUserDevicesKey(userId: string): string {
    return `shop_user_devices:${userId}`;
  }

  async setUserOnline(
    userId: string,
    deviceId: string,
    data: any,
  ): Promise<void> {
    const onlineKey = this.getOnlineUserKey(userId);
    const onlineSetKey = this.getOnlineUsersSetKey();
    const userDevicesKey = this.getUserDevicesKey(userId);

    const onlineData = {
      ...data,
      deviceId,
      lastActivity: data.lastActivity.toISOString(),
    };

    const pipeline = this.client.pipeline();

    pipeline.setex(onlineKey, 60 * 60, JSON.stringify(onlineData));
    pipeline.sadd(onlineSetKey, userId);
    pipeline.expire(onlineSetKey, 60 * 60);
    pipeline.sadd(userDevicesKey, deviceId);
    pipeline.expire(userDevicesKey, 60 * 60);

    await pipeline.exec();
  }

  async removeDeviceFromOnline(
    userId: string,
    deviceId: string,
  ): Promise<void> {
    const userDevicesKey = this.getUserDevicesKey(userId);
    const onlineSetKey = this.getOnlineUsersSetKey();
    const onlineKey = this.getOnlineUserKey(userId);

    await this.client.srem(userDevicesKey, deviceId);
    const remainingDevices = await this.client.scard(userDevicesKey);

    if (remainingDevices === 0) {
      const pipeline = this.client.pipeline();
      pipeline.srem(onlineSetKey, userId);
      pipeline.del(onlineKey);
      pipeline.del(userDevicesKey);
      await pipeline.exec();
    } else {
      const onlineData = await this.getUserOnlineStatus(userId);
      if (onlineData) {
        const pipeline = this.client.pipeline();
        pipeline.setex(
          onlineKey,
          60 * 60,
          JSON.stringify({
            ...onlineData,
            lastActivity: new Date().toISOString(),
          }),
        );
        await pipeline.exec();
      }
    }
  }

  private async cleanupUserOnlineStatus(userId: string): Promise<void> {
    const onlineSetKey = this.getOnlineUsersSetKey();
    const onlineKey = this.getOnlineUserKey(userId);

    const pipeline = this.client.pipeline();
    pipeline.srem(onlineSetKey, userId);
    pipeline.del(onlineKey);
    await pipeline.exec();
  }

  private async cleanupUserDevices(userId: string): Promise<void> {
    const userDevicesKey = this.getUserDevicesKey(userId);
    await this.client.del(userDevicesKey);
  }

  async hasOnlineDevices(userId: string): Promise<boolean> {
    const userDevicesKey = this.getUserDevicesKey(userId);
    const deviceCount = await this.client.scard(userDevicesKey);
    return deviceCount > 0;
  }

  async updateLastSeen(
    userId: string,
    username?: string,
    shopId?: string,
    branchId?: string,
    branchName?: string,
  ): Promise<void> {
    const lastSeenKey = this.getLastSeenKey(userId);
    const onlineSetKey = this.getOnlineUsersSetKey();
    const onlineKey = this.getOnlineUserKey(userId);
    const userDevicesKey = this.getUserDevicesKey(userId);

    let userData = await this.getUserOnlineStatus(userId);
    if (!userData) {
      userData = await this.getLastSeen(userId);
    }

    const lastSeenData = {
      userId,
      username: username || userData?.username,
      shopId: shopId || userData?.shopId,
      branchId: branchId || userData?.branchId,
      branchName: branchName || userData?.branchName,
      lastSeen: new Date().toISOString(),
    };

    const pipeline = this.client.pipeline();
    pipeline.setex(
      lastSeenKey,
      30 * 24 * 60 * 60,
      JSON.stringify(lastSeenData),
    );
    pipeline.srem(onlineSetKey, userId);
    pipeline.del(onlineKey);
    pipeline.del(userDevicesKey);

    await pipeline.exec();
  }

  async updateUserLastActivity(userId: string): Promise<void> {
    const onlineKey = this.getOnlineUserKey(userId);
    const currentData = await this.client.get(onlineKey);

    if (currentData) {
      const data = JSON.parse(currentData);
      data.lastActivity = new Date().toISOString();
      await this.client.setex(onlineKey, 60 * 60, JSON.stringify(data));
    }
  }

  async getUserOnlineStatus(userId: string): Promise<any | null> {
    const onlineKey = this.getOnlineUserKey(userId);
    const data = await this.client.get(onlineKey);

    if (!data) return null;

    const parsed = JSON.parse(data);
    return {
      ...parsed,
      lastActivity: new Date(parsed.lastActivity),
    };
  }

  async refreshUserOnlineStatus(userId: string): Promise<boolean> {
    const userDevicesKey = this.getUserDevicesKey(userId);
    const onlineKey = this.getOnlineUserKey(userId);
    const onlineSetKey = this.getOnlineUsersSetKey();

    const [onlineData, deviceCount, isInSet] = await Promise.all([
      this.client.get(onlineKey),
      this.client.scard(userDevicesKey),
      this.client.sismember(onlineSetKey, userId),
    ]);

    const shouldBeOnline = !!onlineData && deviceCount > 0;

    if (shouldBeOnline && !isInSet) {
      await this.client.sadd(onlineSetKey, userId);
    } else if (!shouldBeOnline && isInSet) {
      await this.client.srem(onlineSetKey, userId);
    }

    if (deviceCount === 0) {
      const pipeline = this.client.pipeline();
      pipeline.srem(onlineSetKey, userId);
      pipeline.del(onlineKey);
      pipeline.del(userDevicesKey);
      await pipeline.exec();
      return false;
    }

    return shouldBeOnline;
  }

  async getLastSeen(userId: string): Promise<any | null> {
    const lastSeenKey = this.getLastSeenKey(userId);
    const data = await this.client.get(lastSeenKey);

    if (!data) return null;

    const parsed = JSON.parse(data);
    return {
      ...parsed,
      lastSeen: new Date(parsed.lastSeen),
    };
  }

  async getFirstLoginData(userId: string): Promise<{
    firstLoginTime?: Date;
    deviceCount: number;
  }> {
    const userFirstLoginsKey = this.getUserFirstLoginsKey(userId);
    const firstLogins = await this.client.zrange(userFirstLoginsKey, 0, -1);

    if (firstLogins.length === 0) {
      return { deviceCount: 0 };
    }

    const oldest = JSON.parse(firstLogins[0]);
    return {
      firstLoginTime: new Date(oldest.firstLoginTime),
      deviceCount: firstLogins.length,
    };
  }

  async isUserOnline(userId: string): Promise<boolean> {
    const userDevicesKey = this.getUserDevicesKey(userId);
    const onlineKey = this.getOnlineUserKey(userId);

    const [onlineData, deviceCount] = await Promise.all([
      this.client.get(onlineKey),
      this.client.scard(userDevicesKey),
    ]);

    const hasValidOnlineData = !!onlineData;
    const hasDevices = deviceCount > 0;

    if (hasValidOnlineData && !hasDevices) {
      await this.cleanupUserOnlineStatus(userId);
      return false;
    }

    if (!hasValidOnlineData && hasDevices) {
      await this.cleanupUserDevices(userId);
      return false;
    }

    return hasValidOnlineData && hasDevices;
  }

  async getOnlineUserCount(): Promise<number> {
    const onlineSetKey = this.getOnlineUsersSetKey();
    return await this.client.scard(onlineSetKey);
  }

  async getShopFirstLogins(shopId: string): Promise<any[]> {
    const key = this.getShopFirstLoginsKey(shopId);
    const firstLogins = await this.client.zrevrange(key, 0, -1);

    return firstLogins.map((loginStr) => {
      const login = JSON.parse(loginStr);
      return {
        ...login,
        loginTime: new Date(login.firstLoginTime),
      };
    });
  }

  async getShopDeviceCount(shopId: string): Promise<number> {
    const key = this.getShopDevicesKey(shopId);
    return await this.client.scard(key);
  }

  async getTodayFirstLogins(shopId: string): Promise<number> {
    const key = this.getTodayFirstLoginsKey(shopId);
    const count = await this.client.get(key);
    return count ? parseInt(count) : 0;
  }

  async cleanupInactiveUsers(maxInactivityMinutes: number = 30): Promise<void> {
    const onlineSetKey = this.getOnlineUsersSetKey();
    const onlineUsers = await this.client.smembers(onlineSetKey);
    const maxInactivity = maxInactivityMinutes * 60 * 1000;
    const now = Date.now();

    const usersToRemove: string[] = [];

    for (const userId of onlineUsers) {
      const [onlineData, deviceCount] = await Promise.all([
        this.getUserOnlineStatus(userId),
        this.client.scard(this.getUserDevicesKey(userId)),
      ]);

      if (!onlineData || deviceCount === 0) {
        usersToRemove.push(userId);
        continue;
      }

      const timeSinceActivity = now - onlineData.lastActivity.getTime();
      if (timeSinceActivity > maxInactivity) {
        usersToRemove.push(userId);

        await this.updateLastSeen(
          userId,
          onlineData.username,
          onlineData.shopId,
          onlineData.branchId,
          onlineData.branchName,
        );
      }
    }

    if (usersToRemove.length > 0) {
      const pipeline = this.client.pipeline();

      for (const userId of usersToRemove) {
        const userDevicesKey = this.getUserDevicesKey(userId);
        const onlineKey = this.getOnlineUserKey(userId);

        pipeline.srem(onlineSetKey, userId);
        pipeline.del(onlineKey);
        pipeline.del(userDevicesKey);
      }

      await pipeline.exec();
      this.logger.log(`Cleaned up ${usersToRemove.length} inactive users`);
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      await this.client.ping();
      return true;
    } catch (error) {
      this.logger.error('Redis health check failed:', error.message);
      return false;
    }
  }

  async get(key: string): Promise<string | null> {
    return await this.client.get(key);
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    if (ttl) {
      await this.client.setex(key, ttl, value);
    } else {
      await this.client.set(key, value);
    }
  }

  async del(key: string): Promise<void> {
    await this.client.del(key);
  }

  async exists(key: string): Promise<boolean> {
    return (await this.client.exists(key)) === 1;
  }
}

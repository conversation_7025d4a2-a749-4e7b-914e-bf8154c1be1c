import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { BranchesService } from '../../modules/branches/branches.service';
import { CommonService } from '../../modules/common/common.service';
import { CustomerQueryService } from '../../modules/customers/customer-queries';
import { ShopsService } from '../../modules/shops/shops.service';
import { ConfigAppModule } from '../config/config.module';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { OTPStrategy } from './strategies/otp.strategy';

@Module({
  imports: [
    JwtModule.registerAsync({
      useFactory: async (configService: ConfigService) => ({
        global: true,
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRY_TIME'),
        },
      }),
      inject: [ConfigService],
    }),
    PassportModule,
    ConfigAppModule,
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    LocalStrategy,
    OTPStrategy,
    JwtStrategy,
    JwtService,
    JwtAuthGuard,
    CustomerQueryService,
    BranchesService,
    CommonService,
    ShopsService
  ],
  exports: [AuthService],
})
export class AuthModule {}

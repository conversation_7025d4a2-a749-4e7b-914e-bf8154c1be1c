name: Release Notifications

on:
  release:
    types: [published]
  
  workflow_run:
    workflows: ["Commerce Backend Server Deployment"]
    types: [requested, completed]

jobs:
  notify-telegram:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Format Release Notification
      if: github.event_name == 'release'
      id: format-release
      run: |
        RELEASE_NAME="${{ github.event.release.name }}"
        RELEASE_TAG="${{ github.event.release.tag_name }}"
        RELEASE_AUTHOR="${{ github.event.release.author.login }}"
        RELEASE_PRERELEASE="${{ github.event.release.prerelease }}"
        
        if [[ "$RELEASE_PRERELEASE" == "true" ]]; then
          EMOJI="🚀"; TYPE="Pre-release"
        else
          EMOJI="🎉"; TYPE="Release"
        fi
        
        MESSAGE="$EMOJI **New $TYPE Published!**"$'\n'
        MESSAGE+=""$'\n'
        MESSAGE+="📂 **Repository:** \`${{ github.repository }}\`"$'\n'
        MESSAGE+="🏷️ **Tag:** \`$RELEASE_TAG\`"$'\n'
        MESSAGE+="📝 **Name:** $RELEASE_NAME"$'\n'
        MESSAGE+="👤 **Author:** $RELEASE_AUTHOR"$'\n'
        MESSAGE+=""$'\n'
        MESSAGE+="🔗 [View Release](${{ github.event.release.html_url }})"
        
        echo "message<<EOF" >> $GITHUB_OUTPUT
        echo "$MESSAGE" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

    - name: Format AWS Deployment Notification
      if: github.event_name == 'workflow_run'
      id: format-deployment
      run: |
        WORKFLOW_NAME="${{ github.event.workflow_run.name }}"
        WORKFLOW_STATUS="${{ github.event.workflow_run.status }}"
        WORKFLOW_CONCLUSION="${{ github.event.workflow_run.conclusion }}"
        WORKFLOW_BRANCH="${{ github.event.workflow_run.head_branch }}"
        WORKFLOW_COMMIT="${{ github.event.workflow_run.head_sha }}"
        
        COMMON_INFO="📂 **Repository:** \`${{ github.repository }}\`"$'\n'
        COMMON_INFO+="⚙️ **Workflow:** $WORKFLOW_NAME"$'\n'
        COMMON_INFO+="🌿 **Branch:** \`$WORKFLOW_BRANCH\`"$'\n'
        COMMON_INFO+="📝 **Commit:** \`${WORKFLOW_COMMIT:0:7}\`"$'\n'
        COMMON_INFO+="🕐 **Started:** ${{ github.event.workflow_run.run_started_at }}"$'\n'
        COMMON_INFO+=""$'\n'
        COMMON_INFO+="🔗 [View Workflow](${{ github.event.workflow_run.html_url }})"
        
        if [[ "$WORKFLOW_STATUS" == "requested" || "$WORKFLOW_STATUS" == "queued" || "$WORKFLOW_STATUS" == "in_progress" ]]; then
          MESSAGE="🚀 **AWS Deployment Started**"$'\n'$'\n'"$COMMON_INFO"
        else
          case $WORKFLOW_CONCLUSION in
            "success") EMOJI="✅"; STATUS_TEXT="succeeded" ;;
            "failure") EMOJI="❌"; STATUS_TEXT="failed" ;;
            "cancelled") EMOJI="🚫"; STATUS_TEXT="was cancelled" ;;
            "skipped") EMOJI="⏭️"; STATUS_TEXT="was skipped" ;;
            *) EMOJI="⚠️"; STATUS_TEXT="completed with status: $WORKFLOW_CONCLUSION" ;;
          esac
          
          MESSAGE="$EMOJI **AWS Deployment $STATUS_TEXT**"$'\n'$'\n'"$COMMON_INFO"
        fi
        
        echo "message<<EOF" >> $GITHUB_OUTPUT
        echo "$MESSAGE" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

    - name: Get Current UTC Timestamp
      id: get-timestamp
      run: echo "timestamp=$(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> $GITHUB_OUTPUT

    - name: Send Telegram Notification
      uses: cbrgm/telegram-github-action@v1
      with:
        token: ${{ secrets.TELEGRAM_TOKEN }}
        to: ${{ secrets.TELEGRAM_CHAT_ID }}
        thread-id: ${{ secrets.TELEGRAM_THREAD_ID }}
        parse-mode: markdown
        disable-web-page-preview: true
        message: |
          ${{ steps.format-release.outputs.message }}${{ steps.format-deployment.outputs.message }}
          
          🕐 *Sent at: ${{ steps.get-timestamp.outputs.timestamp }}*

    - name: Send Failure Notification
      if: failure()
      uses: cbrgm/telegram-github-action@v1
      with:
        token: ${{ secrets.TELEGRAM_TOKEN }}
        to: ${{ secrets.TELEGRAM_CHAT_ID }}
        thread-id: ${{ secrets.TELEGRAM_THREAD_ID }}
        parse-mode: markdown
        message: |
          ⚠️ **Notification System Error**
          
          📂 **Repository:** `${{ github.repository }}`
          🔧 **Event:** ${{ github.event_name }}
          ❌ **Error:** Failed to process notification
          
          🔗 [View Workflow Run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})

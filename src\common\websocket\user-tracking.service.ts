import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../redis/redis.service';
import { AuthService } from './../auth/auth.service';
import { UserActivity } from './user-tracking.dto';

@Injectable()
export class UserTrackingService {
  private readonly logger = new Logger(UserTrackingService.name);

  constructor(
    private readonly redisService: RedisService,
    private readonly authService: AuthService,
  ) {}

  async trackUserActivity(
    userId: string,
    deviceId: string,
    metadata?: {
      username?: string;
      shopId?: string;
      branchId?: string;
      branchName?: string;
      userType?: string;
      userAgent?: string;
      ipAddress?: string;
    },
  ): Promise<{ isFirstLogin: boolean }> {
    try {
      const isFirstLogin = await this.redisService.isFirstDeviceLogin(
        userId,
        deviceId,
        metadata?.shopId,
      );

      await this.redisService.setUserOnline(userId, deviceId, {
        username: metadata?.username,
        shopId: metadata?.shopId,
        branchId: metadata?.branchId,
        branchName: metadata?.branchName,
        userType: metadata?.userType,
        userAgent: metadata?.userAgent,
        ipAddress: metadata?.ipAddress,
        lastActivity: new Date(),
      });

      if (isFirstLogin) {
        await this.redisService.recordFirstLogin(
          userId,
          deviceId,
          metadata?.shopId,
          {
            username: metadata?.username,
            branchId: metadata?.branchId,
            branchName: metadata?.branchName,
            userType: metadata?.userType,
            userAgent: metadata?.userAgent,
            ipAddress: metadata?.ipAddress,
            firstLoginTime: new Date(),
          },
        );

        this.logger.log(
          `First login recorded for user ${metadata?.username || userId} with device ${deviceId} in shop ${metadata?.shopId}`,
        );
      }
      return { isFirstLogin };
    } catch (error) {
      this.logger.error(`Error tracking user activity: ${error.message}`);
      return { isFirstLogin: false };
    }
  }

  async updateLastSeen(userId: string): Promise<void> {
    try {
      await this.redisService.updateUserLastActivity(userId);
      const onlineStatus = await this.redisService.getUserOnlineStatus(userId);

      if (onlineStatus) {
        await this.redisService.updateLastSeen(
          userId,
          onlineStatus.username,
          onlineStatus.shopId,
          onlineStatus.branchId,
          onlineStatus.branchName,
        );
      } else {
        await this.redisService.updateLastSeen(userId);
      }

      this.logger.debug(`Updated last seen for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error updating last seen: ${error.message}`);
    }
  }

  async handleDeviceDisconnect(
    userId: string,
    deviceId: string,
    username?: string,
    shopId?: string,
    branchId?: string,
    branchName?: string,
  ): Promise<void> {
    try {
      await this.redisService.removeDeviceFromOnline(userId, deviceId);

      const hasOtherDevicesOnline =
        await this.redisService.hasOnlineDevices(userId);

      if (!hasOtherDevicesOnline) {
        await this.redisService.updateLastSeen(
          userId,
          username,
          shopId,
          branchId,
          branchName,
        );
      }

      this.logger.debug(`Device ${deviceId} disconnected for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error handling device disconnect: ${error.message}`);
    }
  }

  async isUserOnline(userId: string): Promise<boolean> {
    try {
      return await this.redisService.isUserOnline(userId);
    } catch (error) {
      this.logger.error(`Error checking if user is online: ${error.message}`);
      return false;
    }
  }

  async getUserActivity(userId: string): Promise<UserActivity> {
    try {
      const [onlineStatus, lastSeenData, firstLoginData, hasDevices] =
        await Promise.all([
          this.redisService.getUserOnlineStatus(userId),
          this.redisService.getLastSeen(userId),
          this.redisService.getFirstLoginData(userId),
          this.redisService.hasOnlineDevices(userId),
        ]);

      const isOnline = !!onlineStatus && hasDevices;

      return {
        userId,
        username: onlineStatus?.username || lastSeenData?.username,
        isOnline,
        lastActivity: onlineStatus?.lastActivity || null,
        lastSeen: lastSeenData?.lastSeen || null,
        loginTime: firstLoginData?.firstLoginTime || null,
        deviceCount: firstLoginData?.deviceCount || 0,
        shopId: onlineStatus?.shopId || lastSeenData?.shopId,
        branchId: onlineStatus?.branchId || lastSeenData?.branchId,
        branchName: onlineStatus?.branchName || lastSeenData?.branchName,
      };
    } catch (error) {
      this.logger.error(`Error getting user activity: ${error.message}`);
      return {
        userId,
        isOnline: false,
        lastSeen: null,
        loginTime: null,
        deviceCount: 0,
      };
    }
  }

  async getOnlineUsersByShop(
    shopId: string,
  ): Promise<UserActivity[]> {
    try {
      const allUsers =
        await this.authService.getAllUsersByShop(shopId);

      if (allUsers.length === 0) {
        return [];
      }

      const activities = await Promise.all(
        allUsers.map(async (user) => {
          const activity = await this.getUserActivity(user.user_id);
          return {
            ...activity,
            username: activity.username || user.username,
            shopId: activity.shopId || shopId,
            branchId: activity.branchId || user.branch_id,
            branchName: activity.branchName || user.branch_name,
          };
        }),
      );

      return activities;
    } catch (error) {
      this.logger.error(
        `Error getting users for shop ${shopId}: ${error.message}`,
      );
      return [];
    }
  }

  async getShopActivity(shopId: string): Promise<any[]> {
    try {
      const [firstLogins] = await Promise.all([
        this.redisService.getShopFirstLogins(shopId),
      ]);

      const filteredLogins = firstLogins.filter(
        (login) => login.userType && login.userType.toLowerCase() !== 'owner',
      );

      return filteredLogins;
    } catch (error) {
      this.logger.error(`Error getting shop activity: ${error.message}`);
      return [];
    }
  }

  async getOnlineUserCount(): Promise<number> {
    try {
      return await this.redisService.getOnlineUserCount();
    } catch (error) {
      this.logger.error(`Error getting online user count: ${error.message}`);
      return 0;
    }
  }

  async cleanupOfflineUsers(maxInactivityMinutes: number = 30): Promise<void> {
    try {
      await this.redisService.cleanupInactiveUsers(maxInactivityMinutes);
      this.logger.log('Cleaned up inactive users');
    } catch (error) {
      this.logger.error(`Error cleaning up inactive users: ${error.message}`);
    }
  }

  async healthCheck(): Promise<{ redis: boolean; service: boolean }> {
    try {
      const redisHealth = await this.redisService.healthCheck();
      return {
        redis: redisHealth,
        service: true,
      };
    } catch (error) {
      this.logger.error(`Health check failed: ${error.message}`);
      return {
        redis: false,
        service: false,
      };
    }
  }
}

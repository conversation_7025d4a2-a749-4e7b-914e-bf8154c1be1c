name: Commerce Backend Server Deployment

on:
  push:
    tags:
      - "v*"

env:
  AWS_REGION: me-central-1
  DOCKER_BUILD_SUMMARY: false

permissions:
  id-token: write
  contents: read

jobs:
  check-current-branch:
    name: Check current branch
    runs-on: ubuntu-latest
    outputs:
      branch: ${{ steps.check_step.outputs.branch }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get current branch
        id: check_step
        run: |
          raw=$(git branch -r --contains ${{ github.ref }})
          branch="$(echo ${raw//origin\//} | tr -d '\n')"
          echo "{name}=branch" >> $GITHUB_OUTPUT

  build-and-deploy:
    if: contains(${{ needs.check.outputs.branch }}, 'develop')`
    name: Commerce Backend Deployment
    runs-on: ubuntu-latest
    needs: check-current-branch
    environment: production

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Download Certificate Bundle
        run: |
          curl -o certificate-bundle.pem ${{ secrets.CERTIFICATE_BUNDLE_URL }}

      - name: Create .env file
        run: echo "${{ secrets.PROD_ENV_FILE }}" > .env

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push image to Amazon ECR
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ secrets.AWS_ECR_REGISTRY }}:latest,${{ secrets.AWS_ECR_REGISTRY }}:${{ github.sha }}

      - name: Get Instance ID by Name and Start AWS SSM Session
        run: |
          INSTANCE_NAME="cravin-backend-server"
          INSTANCE_ID=$(aws ec2 describe-instances --filters "Name=tag:Name,Values=$INSTANCE_NAME" --query "Reservations[*].Instances[*].InstanceId" --output text)
          sudo apt-get install -y expect
          unbuffer aws ssm start-session \
            --document-name 'AWS-StartNonInteractiveCommand' \
            --parameters '{"command":["sudo su ubuntu -c \" cd ~ && if [ -f deploy-commerce.sh ]; then rm deploy-commerce.sh; fi && aws s3 cp s3://cravin/deploy-commerce.sh ~/ --region=me-central-1 && chmod +x ~/deploy-commerce.sh && ~/deploy-commerce.sh\""]}' \
            --target "$INSTANCE_ID"

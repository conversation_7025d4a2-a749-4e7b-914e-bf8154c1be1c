import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsUUID,
  <PERSON>,
  Min,
  ValidateNested,
} from 'class-validator';

export interface Customer {
  customer_name: string;
  phone_number: string;
  customer_id: string;
}

class ItemRatings {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  item_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  item_name: string;

  @ApiProperty({
    description: 'Rating from 1-5, or 0 to skip rating this item',
    example: 5,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(5)
  item_rating: number;
}

export class CustomerOrderReview {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  customer_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  shop_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  branch_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  order_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  order_name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(5)
  order_rating: number;

  @ApiProperty({
    type: [ItemRatings],
    description: 'Ratings for individual items in the order',
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ItemRatings)
  item_ratings: ItemRatings[];

  @ApiProperty()
  @IsOptional()
  @IsString()
  review_text: string;
}

import {
  Injectable,
  ConflictException,
  NotFoundException,
} from '@nestjs/common';
import { DatabaseService } from '../../common/config/database.services';
import { TrackingCodeDto } from './dto/tracking-code.dto';

@Injectable()
export class TrackingService {
  constructor(private readonly databaseService: DatabaseService) {}

  async saveTrackingCode(
    shopId: string,
    trackingCode: TrackingCodeDto,
  ): Promise<void> {
    const sql = `
      INSERT INTO public."ShopTrackingCodes" (
        fk_shop_id,
        analytics_name,
        code_value
      ) VALUES ($1, $2, $3)
      ON CONFLICT (fk_shop_id, analytics_name)
      DO UPDATE SET
        code_value = EXCLUDED.code_value,
        modified_at = CURRENT_TIMESTAMP
    `;

    try {
      await this.databaseService.query(sql, [
        shopId,
        trackingCode.analytics_name,
        trackingCode.code_value,
      ]);
    } catch (error) {
      throw new ConflictException(
        `Analytics code "${trackingCode.analytics_name}" already exists`,
      );
    }
  }

  async getTrackingCodes(shopId: string): Promise<any[]> {
    const sql = `
      SELECT 
        tracking_id,
        analytics_name,
        code_value,
        is_active,
        created_at,
        modified_at
      FROM public."ShopTrackingCodes"
      WHERE fk_shop_id = $1
      ORDER BY created_at DESC
    `;

    return await this.databaseService.query(sql, [shopId]);
  }

  async deleteTrackingCode(
    shopId: string,
    trackingId: string,
  ): Promise<void> {
    const sql = `
      DELETE FROM public."ShopTrackingCodes"
      WHERE fk_shop_id = $1 AND tracking_id = $2
    `;

    const result = await this.databaseService.query(sql, [
      shopId,
      trackingId,
    ]);
    if (result.rowCount === 0) {
      throw new NotFoundException('Tracking code not found');
    }
  }

  async toggleTrackingCode(
    shopId: string,
    trackingId: string,
    isActive: boolean,
  ): Promise<void> {
    const sql = `
      UPDATE public."ShopTrackingCodes"
      SET 
        is_active = $3,
        modified_at = CURRENT_TIMESTAMP
      WHERE fk_shop_id = $1 AND tracking_id = $2
    `;

    const result = await this.databaseService.query(sql, [
      shopId,
      trackingId,
      isActive,
    ]);
    if (result.rowCount === 0) {
      throw new NotFoundException('Tracking code not found');
    }
  }

  async getActiveTrackingCodes(shopId: string): Promise<any[]> {
    const sql = `
      SELECT 
        analytics_name,
        code_value
      FROM public."ShopTrackingCodes"
      WHERE 
        fk_shop_id = $1 
        AND is_active = true
      ORDER BY created_at ASC
    `;

    return await this.databaseService.query(sql, [shopId]);
  }
}

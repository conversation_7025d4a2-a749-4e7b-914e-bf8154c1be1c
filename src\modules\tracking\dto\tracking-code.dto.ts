import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, MinLength } from 'class-validator';

export class TrackingCodeDto {
  @ApiProperty({
    description:
      'Name of the analytics service (e.g. "Google Analytics", "Meta Pixel")',
    example: 'Google Analytics',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  analytics_name: string;

  @ApiProperty({
    description: 'The tracking code script/value',
    example: '<script>// Analytics code</script>',
  })
  @IsString()
  @IsNotEmpty()
  code_value: string;
}

import {
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import booleanPointInPolygon from '@turf/boolean-point-in-polygon';
import distance from '@turf/distance';
import { point, polygon } from '@turf/helpers';
import {
  addDays,
  addHours,
  addMonths,
  endOfDay,
  endOfMonth,
  endOfWeek,
  endOfYear,
  format,
  parse,
  startOfDay,
  startOfMonth,
  startOfWeek,
  startOfYear,
  sub,
  subDays,
  subMonths,
  subWeeks,
  subYears,
} from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { google } from 'googleapis';
import { DatabaseService } from '../../common/config/database.services';
import { Customer } from '../customers/dto/customers.dto';
import {
  CreateShopOnboardingDto,
  LandingPageDto,
  UpdateShopBranchesDto,
} from './dto/shop-onboarding.dto';

@Injectable()
export class ShopsService {
  constructor(private readonly databaseService: DatabaseService) {}

  async getShopPhoneById(
    shopId: string,
  ): Promise<{ shopName: string; shopPhone: string }> {
    let query = `SELECT shop_name AS "shopName", phone_number as "shopPhone"
       FROM "Shops" WHERE "shop_id" = $1`;
    const result = await this.databaseService.query(query, [shopId]);
    return result[0];
  }

  async editShopName(shopId: string, shopName: string): Promise<void> {
    let query = `UPDATE "Shops" SET shop_name = $1 WHERE shop_id = $2`;
    await this.databaseService.query(query, [shopName, shopId]);
  }

  async addShop(shopData: {
    shop_id: string;
    shop_name: string;
    shop_social_links: any;
    shop_timings: any;
    phone_number: string;
    order_policy: string;
    payment_methods: any;
  }): Promise<void> {
    const {
      shop_id,
      shop_name,
      shop_social_links,
      shop_timings,
      phone_number,
      order_policy,
      payment_methods,
    } = shopData;

    const sql = `
      INSERT INTO public."Shops" (
        shop_id,
        shop_name,
        shop_social_links,
        shop_timings,
        phone_number,
        order_policy,
        payment_methods
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    `;

    const params = [
      shop_id,
      shop_name,
      shop_social_links,
      shop_timings,
      phone_number,
      order_policy,
      payment_methods,
    ];

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to add shop');
    }
  }

  async getBranchesByShopId(shopId: string): Promise<{ branch_id: string }[]> {
    try {
      const sql = `
        SELECT branch_id
        FROM public."ShopBranches"
        WHERE fk_shop_id = $1
      `;
      const result = await this.databaseService.query(sql, [shopId]);
      return result;
    } catch (error) {
      throw new Error('Failed to fetch branches by shop ID');
    }
  }

  async getShopOrderPolicy(shop_id: string): Promise<any[]> {
    const sql = `
    SELECT order_policy FROM public."Shops"
    WHERE shop_id = $1
  `;

    try {
      const result = await this.databaseService.query(sql, [shop_id]);
      return result[0];
    } catch (error) {
      throw new Error('Failed to get order policy');
    }
  }

  async getBranchNames(shop_id: string): Promise<any[]> {
    const sql = `
    SELECT branch_id, branch_name, branch_display_name FROM public."ShopBranches"
    WHERE fk_shop_id = $1
  `;

    try {
      const result = await this.databaseService.query(sql, [shop_id]);
      return result;
    } catch (error) {
      throw new Error('Failed to branch names');
    }
  }

  async getShopMenuItems(
    shopId: string,
    branchId: string | null,
  ): Promise<any[]> {
    let params: string[] = [];
    let sql = `
      SELECT
        c.category_id,
        c.category_name,
        c.status,
        json_agg(
          json_build_object(
            'item_id', i.item_id,
            'item_name', i.item_name,
            'item_type', i.item_type,
            'item_price', i.item_price,
            'item_image_links', i.item_image_links,
            'item_status', i.item_status,
            'item_quantity', i.item_quantity,
            'out_of_stock_until', to_char(i.out_of_stock_until AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS"Z"'),
            'add_ons_count',
              COALESCE(jsonb_array_length(i.item_add_ons_group), 0)
          )
        ) AS items,
        b.branch_id,
        b.branch_name
      FROM
        public."ShopCategories" c
      LEFT JOIN
        public."ShopItems" i ON i.fk_category_id = c.category_id
      LEFT JOIN
        public."ShopBranches" b ON i.fk_branch_id = b.branch_id
      WHERE
        c.category_id IN (
          SELECT DISTINCT i.fk_category_id
          FROM public."ShopItems" i
          WHERE i.fk_branch_id
    `;

    if (branchId) {
      sql += `= $1 `;
      params = [branchId];
    } else {
      sql += `IN (SELECT branch_id FROM public."ShopBranches" WHERE fk_shop_id = $1) `;
      params = [shopId];
    }

    sql += `
        )
      GROUP BY
        c.category_id, c.category_name, c.status, b.branch_id, b.branch_name
      ORDER BY c.category_name
    `;

    try {
      const result = await this.databaseService.query(sql, params);
      return result || [];
    } catch (error) {
      throw new Error('Failed to fetch menu items');
    }
  }

  async getShopPastOrders(
    shopId: string,
    branchId: string | null,
  ): Promise<any[]> {
    let params: string[] = [];
    let sql = `
      WITH preferred_customer AS (
        SELECT
          c.customer_id,
          COALESCE(cr.customer_name, c.customer_name) AS customer_name,
          c.phone_number,
          ROW_NUMBER() OVER (PARTITION BY c.customer_id ORDER BY cr.customer_name IS NULL) AS rn
        FROM
          public."Customers" c
        LEFT JOIN
          public."Customer_Shops" cr ON cr.fk_customer_id = c.customer_id
        AND
          $1 = ANY(cr.branch_ids)
      )
      SELECT
        o.order_id,
        o.order_name,
        o.items,
        pc.customer_name,
        pc.phone_number,
        CASE
          WHEN jsonb_typeof(o.customer_address) = 'object' THEN
            CONCAT_WS(', ',
              o.customer_address->>'sector',
              o.customer_address->>'building',
              o.customer_address->>'landmark',
              o.customer_address->>'google_address'
            )
          ELSE NULL
        END AS customer_address,
        o.customer_address->>'recipient_name' AS receiver_name,
        o.customer_address->>'recipient_contact' AS receiver_number,
        o.created_at AS ordered_on,
        o.bill,
        o.status,
        o.promo_code,
        o.delivery_driver,
        o.payment_method,
        o.rejected_reason,
        o.is_refunded,
        o.order_type,
        b.branch_name
      FROM public."ShopOrders" o
      LEFT JOIN public."ShopBranches" b ON o.fk_branch_id = b.branch_id
      LEFT JOIN preferred_customer pc ON o.fk_customer_id = pc.customer_id AND pc.rn = 1
      WHERE
        o.status != 'processing' AND
        o.fk_branch_id
    `;

    if (branchId) {
      sql += ` = $1 `;
      params = [branchId];
    } else {
      sql += ` IN (SELECT branch_id FROM public."ShopBranches" WHERE fk_shop_id = $1) `;
      params = [shopId];
    }

    sql += `
      ORDER BY o.created_at DESC
    `;

    try {
      const result = await this.databaseService.query(sql, params);
      return result || [];
    } catch (error) {
      throw new Error('Failed to fetch past orders');
    }
  }

  async getShopSales(
    shopId: string,
    branchId: string | null,
    orderType: string = 'all',
    paymentMode: string | null,
    dateOption: string,
    dateRange?: { from: string; to: string },
    timeZone: string = 'Asia/Dubai',
  ): Promise<any[]> {
    try {
      const params: any[] = [];
      let shopIdIndex: number | null = null;
      let branchIdIndex: number | null = null;

      if (shopId) {
        params.push(shopId);
        shopIdIndex = params.length;
      }

      if (branchId) {
        params.push(branchId);
        branchIdIndex = params.length;
      }

      let sql = `
        WITH preferred_customer AS (
          SELECT
            c.customer_id,
            COALESCE(cr.customer_name, c.customer_name) AS customer_name,
            c.phone_number,
            ROW_NUMBER() OVER (PARTITION BY c.customer_id ORDER BY cr.customer_name IS NULL) AS rn
          FROM
            public."Customers" c
          LEFT JOIN
            public."Customer_Shops" cr ON cr.fk_customer_id = c.customer_id
            ${branchIdIndex !== null ? `AND $${branchIdIndex} = ANY(cr.branch_ids)` : ''}
            AND cr.fk_shop_id = $${shopIdIndex}
        )
        SELECT
          o.order_id,
          o.order_name,
          o.created_at AS ordered_on,
          o.items,
          o.payment_method,
          o.promo_code,
          b.branch_name,
          pc.customer_name,
          pc.phone_number,
          o.bill,
          o.status,
          o.delivery_driver,
          o.rejected_reason,
          o.is_refunded,
          CASE
            WHEN jsonb_typeof(o.customer_address) = 'object'
            THEN CONCAT_WS(', ', o.customer_address->>'sector', o.customer_address->>'building', o.customer_address->>'landmark', o.customer_address->>'google_address')
            ELSE NULL
          END AS customer_address,
          o.customer_address->>'recipient_name' AS receiver_name,
          o.customer_address->>'recipient_contact' AS receiver_number,
          o.bill,
          o.status,
          o.delivery_driver,
          o.rejected_reason,
          o.is_refunded,
          o.order_type
        FROM
          public."ShopOrders" o
          LEFT JOIN preferred_customer pc ON o.fk_customer_id = pc.customer_id AND pc.rn = 1
          LEFT JOIN public."ShopBranches" b ON o.fk_branch_id = b.branch_id`;

      sql += `
        WHERE
          o.status IN ('delivered', 'cancelled', 'picked_up')`;

      const dateOptionFilter = this.getDateOptionFilter(
        dateOption,
        dateRange,
        timeZone,
      );
      sql += dateOptionFilter.currentCondition;

      if (paymentMode) {
        sql += `
          AND o.payment_method = $${params.length + 1}`;
        params.push(paymentMode);
      }

      if (orderType !== 'all') {
        sql += `
          AND o.order_type = $${params.length + 1}`;
        params.push(orderType);
      }

      sql += `
        AND o.fk_branch_id`;

      if (branchId) {
        sql += `
          = $${branchIdIndex}`;
      } else {
        sql += `
          IN (
            SELECT
              b.branch_id
            FROM
              public."ShopBranches" b
            WHERE
              b.fk_shop_id = $${shopIdIndex})`;
      }

      sql += `
        ORDER BY
          o.created_at DESC;`;

      const result = await this.databaseService.query(sql, params);
      return result || [];
    } catch (error) {
      throw new Error('Failed to fetch sales data');
    }
  }

  async getCustomerOrderReviews(
    shopId: string,
    branchId?: string,
  ): Promise<any> {
    let sql = `
      SELECT
        sr.review_id,
        sr.fk_order_id AS order_id,
        sr.fk_customer_id AS customer_id,
        COALESCE(cs.customer_name, c.customer_name) AS customer_name,
        CASE
          WHEN c.customer_address IS NOT NULL THEN
            (
              SELECT jsonb_agg(
                jsonb_build_object(
                  'address', 
                    CONCAT_WS(', ', 
                      NULLIF(addr.value->>'sector', ''),
                      NULLIF(addr.value->>'building', ''),
                      NULLIF(addr.value->>'google_address', '')
                    ),
                  'landmark', addr.value->>'landmark',
                  'latitude', (addr.value->>'latitude')::numeric,
                  'longitude', (addr.value->>'longitude')::numeric,
                  'recipient_name', addr.value->>'recipient_name',
                  'recipient_contact', addr.value->>'recipient_contact',
                  'type', addr.key,
                  'is_customer_favourite', (addr.key = c.customer_favourite_address_tag)
                )
              )
              FROM jsonb_each(c.customer_address) AS addr
            )
          ELSE NULL
        END AS customer_address,
        c.phone_number,
        c.email_address,
        c.gender,
        c.nationality,
        c.created_at AS customer_joined_at,
        sr.fk_shop_id AS shop_id,
        sr.fk_branch_id AS branch_id,
        b.branch_name,
        sr.order_name,
        sr.order_rating,
        sr.item_ratings,
        sr.review_text,
        sr.created_at AS review_created_at
      FROM
        public."ShopReviews" sr
      LEFT JOIN
        public."Customer_Shops" cs ON cs.fk_customer_id = sr.fk_customer_id
    `;

    if (branchId) {
      sql += `AND (cs.fk_shop_id = $1 AND $2 = ANY(cs.branch_ids))`;
    } else {
      sql += `AND cs.fk_shop_id = $1`;
    }

    sql += `
      LEFT JOIN
        public."Customers" c ON c.customer_id = sr.fk_customer_id
      LEFT JOIN
        public."ShopBranches" b ON b.branch_id = sr.fk_branch_id
      WHERE
        sr.fk_shop_id = $1
    `;

    if (branchId) {
      sql += `AND sr.fk_branch_id = $2`;
    }

    sql += `
      ORDER BY
        sr.created_at DESC
    `;

    const params = branchId ? [shopId, branchId] : [shopId];

    try {
      const result = await this.databaseService.query(sql, params);
      return result;
    } catch (error) {
      throw new Error('Failed to fetch order reviews by customer');
    }
  }

  getOffset(timeZone: string): number {
    const date = new Date();
    const timeZoneOffset = date.toLocaleString('en', {
      timeZone,
      timeZoneName: 'shortOffset',
    });
    const [, sign, hour, minute] =
      timeZoneOffset.match(/([+-])(\d+):?(\d+)?/) || [];
    const offsetHours = parseInt(hour) || 0;
    const offsetMinutes = parseInt(minute) || 0;
    const totalOffsetSeconds = (offsetHours * 60 + offsetMinutes) * 60;
    return sign === '+' ? -totalOffsetSeconds : totalOffsetSeconds;
  }

  private generateDateSeries(
    startDate: Date,
    endDate: Date,
    interval: number,
    type: string,
  ): Date[] {
    const series: Date[] = [];
    let currentDate = startDate;

    while (currentDate <= endDate) {
      series.push(currentDate);

      if (type === 'hour') {
        currentDate = addHours(currentDate, interval);
      } else if (type === 'day') {
        currentDate = addDays(currentDate, interval);
      } else if (type === 'month') {
        currentDate = addMonths(currentDate, interval);
      }

      if (type === 'month') {
        const endOfMonthDate = endOfMonth(currentDate);
        if (currentDate.getDate() !== endOfMonthDate.getDate()) {
          currentDate = new Date(
            endOfMonthDate.getFullYear(),
            endOfMonthDate.getMonth(),
            endOfMonthDate.getDate(),
            currentDate.getHours(),
            currentDate.getMinutes(),
            currentDate.getSeconds(),
            currentDate.getMilliseconds(),
          );
        }
      }
    }

    return series;
  }

  private adjustForTimezone(date: Date, timeZone: string): Date {
    const offset = this.getOffset(timeZone);
    const adjustedDate = new Date(date.getTime() + offset * 1000);
    return adjustedDate;
  }

  private getDateOptionFilter(
    dateOption: string,
    dateRange: { from: string; to: string },
    timeZone: string = 'Asia/Dubai',
    getPast?: boolean,
  ): { currentCondition: string; previousCondition: string } {
    const currentTimeWithOffset = toZonedTime(new Date(), timeZone);

    let currentCondition = '';
    let previousCondition = '';

    const formatDateForSQL = (date: Date): string => {
      return format(date, 'yyyy-MM-dd HH:mm:ss');
    };

    switch (dateOption) {
      case 'today':
        const dayStart = this.adjustForTimezone(
          startOfDay(currentTimeWithOffset),
          timeZone,
        );
        const dayEnd = this.adjustForTimezone(
          endOfDay(currentTimeWithOffset),
          timeZone,
        );
        currentCondition = ` AND o.created_at BETWEEN '${formatDateForSQL(dayStart)}' AND '${formatDateForSQL(dayEnd)}'`;
        break;
      case 'week':
        const weekStart = this.adjustForTimezone(
          startOfWeek(currentTimeWithOffset, { weekStartsOn: 1 }),
          timeZone,
        );
        const weekEnd = this.adjustForTimezone(
          endOfWeek(currentTimeWithOffset, { weekStartsOn: 1 }),
          timeZone,
        );
        currentCondition = ` AND o.created_at BETWEEN '${formatDateForSQL(weekStart)}' AND '${formatDateForSQL(weekEnd)}'`;
        break;
      case 'month':
        const monthStart = this.adjustForTimezone(
          startOfMonth(currentTimeWithOffset),
          timeZone,
        );
        const monthEnd = this.adjustForTimezone(
          endOfMonth(currentTimeWithOffset),
          timeZone,
        );
        currentCondition = ` AND o.created_at BETWEEN '${formatDateForSQL(monthStart)}' AND '${formatDateForSQL(monthEnd)}'`;
        break;
      case 'year':
        const yearStart = this.adjustForTimezone(
          startOfYear(currentTimeWithOffset),
          timeZone,
        );
        const yearEnd = this.adjustForTimezone(
          endOfYear(currentTimeWithOffset),
          timeZone,
        );
        currentCondition = ` AND o.created_at BETWEEN '${formatDateForSQL(yearStart)}' AND '${formatDateForSQL(yearEnd)}'`;
        break;
      case 'last_seven_days':
      case 'last_thirty_days':
      case 'last_ninety_days':
      case 'last_24_hours':
        let noOfDays = 90;
        if (dateOption === 'last_thirty_days') {
          noOfDays = 30;
        } else if (dateOption === 'last_seven_days') {
          noOfDays = 7;
        } else if (dateOption === 'last_24_hours') {
          noOfDays = 1;
        }

        const newDate = sub(new Date(), {
          days: noOfDays,
        });

        const fromDate = this.adjustForTimezone(newDate, timeZone);
        const newDateVal = new Date();
        newDateVal.setDate(newDateVal.getDate() + 1);

        const toDate = this.adjustForTimezone(newDateVal, timeZone);
        currentCondition = ` AND o.created_at BETWEEN '${formatDateForSQL(fromDate)}' AND '${formatDateForSQL(toDate)}'`;
        break;
      case 'date_range':
        if (dateRange) {
          const fromDate = this.adjustForTimezone(
            startOfDay(parse(dateRange.from, 'dd-MM-yyyy', new Date())),
            timeZone,
          );
          const toDate = this.adjustForTimezone(
            endOfDay(parse(dateRange.to, 'dd-MM-yyyy', new Date())),
            timeZone,
          );
          currentCondition = ` AND o.created_at BETWEEN '${formatDateForSQL(fromDate)}' AND '${formatDateForSQL(toDate)}'`;
        }
        break;
      default:
        break;
    }

    if (getPast) {
      switch (dateOption) {
        case 'today':
          const yesterdayStart = this.adjustForTimezone(
            startOfDay(subDays(currentTimeWithOffset, 1)),
            timeZone,
          );
          const yesterdayEnd = this.adjustForTimezone(
            endOfDay(subDays(currentTimeWithOffset, 1)),
            timeZone,
          );
          previousCondition = ` AND o.created_at BETWEEN '${formatDateForSQL(yesterdayStart)}' AND '${formatDateForSQL(yesterdayEnd)}'`;
          break;
        case 'week':
          const previousWeekStart = this.adjustForTimezone(
            startOfWeek(subWeeks(currentTimeWithOffset, 1), {
              weekStartsOn: 0,
            }),
            timeZone,
          );
          const previousWeekEnd = this.adjustForTimezone(
            endOfWeek(subWeeks(currentTimeWithOffset, 1), { weekStartsOn: 1 }),
            timeZone,
          );
          previousCondition = ` AND o.created_at BETWEEN '${formatDateForSQL(previousWeekStart)}' AND '${formatDateForSQL(previousWeekEnd)}'`;
          break;
        case 'month':
          const previousMonthStart = this.adjustForTimezone(
            startOfMonth(subMonths(currentTimeWithOffset, 1)),
            timeZone,
          );
          const previousMonthEnd = this.adjustForTimezone(
            endOfMonth(subMonths(currentTimeWithOffset, 1)),
            timeZone,
          );
          previousCondition = ` AND o.created_at BETWEEN '${formatDateForSQL(previousMonthStart)}' AND '${formatDateForSQL(previousMonthEnd)}'`;
          break;
        case 'year':
          const previousYearStart = this.adjustForTimezone(
            startOfYear(subYears(currentTimeWithOffset, 1)),
            timeZone,
          );
          const previousYearEnd = this.adjustForTimezone(
            endOfYear(subYears(currentTimeWithOffset, 1)),
            timeZone,
          );
          previousCondition = ` AND o.created_at BETWEEN '${formatDateForSQL(previousYearStart)}' AND '${formatDateForSQL(previousYearEnd)}'`;
          break;
        case 'last_seven_days':
        case 'last_thirty_days':
        case 'last_ninety_days':
        case 'last_24_hours':
          let noOfDays = 90;
          if (dateOption === 'last_thirty_days') {
            noOfDays = 30;
          } else if (dateOption === 'last_seven_days') {
            noOfDays = 7;
          } else if (dateOption === 'last_24_hours') {
            noOfDays = 1;
          }

          const newDate = sub(new Date(), {
            days: noOfDays * 2,
          });

          const fromDate = this.adjustForTimezone(newDate, timeZone);
          const newDateVal = new Date();
          newDateVal.setDate(newDateVal.getDate() - noOfDays);

          const toDate = this.adjustForTimezone(newDateVal, timeZone);
          previousCondition = ` AND o.created_at BETWEEN '${formatDateForSQL(fromDate)}' AND '${formatDateForSQL(toDate)}'`;
          break;
        default:
          previousCondition = '';
          break;
      }
    }

    return {
      currentCondition,
      previousCondition: getPast ? previousCondition : null,
    };
  }

  private calculatePercentageChange(
    currentValue: number,
    previousValue: number,
  ): number {
    if (previousValue === 0) {
      return 0;
    }

    const change =
      ((currentValue - previousValue) / Math.abs(previousValue)) * 100;
    const absoluteChange = Math.abs(change);

    return parseFloat(absoluteChange.toFixed(2));
  }

  private calculateSign(currentValue: number, previousValue: number): string {
    if (+currentValue > +previousValue) {
      return '+';
    } else if (+currentValue < +previousValue) {
      return '-';
    } else {
      return '=';
    }
  }

  async getShopAnalyticsCount(
    shopId: string,
    branchId: string | null,
    type:
      | 'statistics'
      | 'top_customers'
      | 'top_selling_items'
      | 'top_locations'
      | 'payment_mode'
      | 'peak_hours'
      | 'top_added_items'
      | 'total_users_landed'
      | 'total_users_texted'
      | 'top_viewed_items'
      | 'order_count',
    dateOption:
      | 'today'
      | 'week'
      | 'month'
      | 'year'
      | 'date_range'
      | 'last_seven_days'
      | 'last_thirty_days'
      | 'last_ninety_days'
      | 'last_24_hours',
    timeZone: string = 'Asia/Dubai',
    dateRange?: { from: string; to: string },
    orderBy?: 'order_based' | 'sales_based',
  ) {
    const { currentCondition, previousCondition } = this.getDateOptionFilter(
      dateOption,
      dateRange,
      timeZone,
      type === 'statistics' ||
        type === 'top_added_items' ||
        type === 'total_users_texted' ||
        type === 'total_users_landed' ||
        type === 'top_viewed_items',
    );
    const params: string[] = [];
    let currentSql = '';
    let previousSql = '';

    const addBranchCondition = () => {
      if (branchId) {
        currentSql += ` AND o.fk_branch_id = $${params.length + 1} `;
        previousSql += ` AND o.fk_branch_id = $${params.length + 1} `;
        params.push(branchId);
      } else {
        currentSql += ` AND o.fk_shop_id = $${params.length + 1} `;
        previousSql += ` AND o.fk_shop_id = $${params.length + 1} `;
        params.push(shopId);
      }
    };

    if (type === 'statistics') {
      currentSql = `
        SELECT
          COUNT(o.order_id)::int AS total_orders,
          ROUND(SUM((o.bill ->> 'total_bill')::numeric), 2) AS total_revenue,
          COUNT(DISTINCT fk_customer_id)::int AS total_customers
      `;

      if (!branchId) {
        currentSql += `, b.branch_name `;
      }

      currentSql += `
        FROM public."ShopOrders" o
      `;

      if (!branchId) {
        currentSql += ` LEFT JOIN public."ShopBranches" b ON o.fk_branch_id = b.branch_id `;
      }

      currentSql += ` WHERE o.status IN ('accepted', 'delivered', 'out_for_delivery') `;
      addBranchCondition();
      currentSql += ` ${currentCondition} GROUP BY o.fk_shop_id `;

      if (!branchId) {
        currentSql += `, b.branch_name`;
      }

      previousSql = currentSql.replace(currentCondition, previousCondition);

      try {
        const currentResult = await this.databaseService.query(
          currentSql,
          params,
        );

        const previousResult = await this.databaseService.query(
          previousSql,
          params,
        );

        let currentData = {
          total_orders: 0,
          total_revenue: 0,
          total_customers: 0,
        };
        if (currentResult.length > 0) {
          currentData = currentResult.reduce((acc, row) => {
            return {
              total_orders: acc.total_orders + +row.total_orders,
              total_revenue:
                parseFloat(acc.total_revenue) + parseFloat(row.total_revenue),
              total_customers: acc.total_customers + +row.total_customers,
            };
          });
        }

        let previousData = {
          total_orders: 0,
          total_revenue: 0,
          total_customers: 0,
        };

        if (previousResult.length > 0) {
          previousData = previousResult.reduce((acc, row) => {
            return {
              total_orders: acc.total_orders + +row.total_orders,
              total_revenue:
                parseFloat(acc.total_revenue) + parseFloat(row.total_revenue),
              total_customers: acc.total_customers + +row.total_customers,
            };
          });
        }

        const statistics = {
          orders: {
            totalOrders: currentData.total_orders,
            sign:
              dateOption !== 'date_range'
                ? this.calculateSign(
                    currentData.total_orders,
                    previousData.total_orders,
                  )
                : undefined,
            percentage:
              dateOption !== 'date_range'
                ? this.calculatePercentageChange(
                    currentData.total_orders,
                    previousData.total_orders,
                  )
                : undefined,
            isDate: dateOption === 'date_range',
          },
          revenues: {
            totalRevenue: Math.round(currentData.total_revenue * 100) / 100,
            sign:
              dateOption !== 'date_range'
                ? this.calculateSign(
                    currentData.total_revenue,
                    previousData.total_revenue,
                  )
                : undefined,
            percentage:
              dateOption !== 'date_range'
                ? this.calculatePercentageChange(
                    currentData.total_revenue,
                    previousData.total_revenue,
                  )
                : undefined,
            isDate: dateOption === 'date_range',
          },
          customers: {
            totalCustomers: currentData.total_customers,
            sign:
              dateOption !== 'date_range'
                ? this.calculateSign(
                    currentData.total_customers,
                    previousData.total_customers,
                  )
                : undefined,
            percentage:
              dateOption !== 'date_range'
                ? this.calculatePercentageChange(
                    currentData.total_customers,
                    previousData.total_customers,
                  )
                : undefined,
            isDate: dateOption === 'date_range',
          },
        };

        return { statistics };
      } catch (error) {
        throw new Error('Failed to fetch statistics');
      }
    } else if (type === 'top_customers') {
      currentSql = `
        WITH preferred_customer AS (
        SELECT
          c.customer_id,
          COALESCE(cr.customer_name, c.customer_name) AS customer_name,
          c.phone_number,
          ROW_NUMBER() OVER (PARTITION BY c.customer_id ORDER BY cr.customer_name IS NULL) AS rn
        FROM
          public."Customers" c
        LEFT JOIN
          public."Customer_Shops" cr ON cr.fk_customer_id = c.customer_id
          AND $2 = ANY(cr.branch_ids)
          AND cr.fk_shop_id = $1
        )
        SELECT
          pc.customer_name,
          pc.phone_number,
          COUNT(o.order_id) AS order_count,
          SUM((o.bill ->> 'total_bill')::numeric) AS total_revenue
        FROM
          public."ShopOrders" o
          LEFT JOIN public."Customers" c ON c.customer_id = o.fk_customer_id
          LEFT JOIN preferred_customer pc ON c.customer_id = pc.customer_id AND pc.rn = 1
        WHERE
          o.fk_shop_id = $${params.length + 1}
          AND o.status IN ('accepted', 'delivered', 'out_for_delivery', 'ready_for_pickup', 'picked_up')
      `;

      params.push(shopId);
      addBranchCondition();
      currentSql += ` ${currentCondition} GROUP BY o.fk_customer_id, o.fk_shop_id, pc.customer_name, pc.phone_number, c.phone_number `;

      if (orderBy) {
        const orderByColumn =
          orderBy === 'order_based' ? 'order_count' : 'total_revenue';
        currentSql += ` ORDER BY ${orderByColumn} DESC `;
      }

      currentSql += ` LIMIT 10 `;

      try {
        const currentResult = await this.databaseService.query(
          currentSql,
          params,
        );
        return currentResult;
      } catch (error) {
        throw new Error('Failed to fetch top customers');
      }
    } else if (type === 'top_selling_items') {
      currentSql = `
        SELECT
          i.item_name,
          COALESCE(i.item_image_links[1], di.item_image_link) AS item_image_link,
          oi.item->>'id' AS item_id,
          SUM((oi.item->>'quantity')::integer) AS total_quantity,
          b.branch_name
        FROM
          public."ShopOrders" o
        CROSS JOIN jsonb_array_elements(o.items::jsonb) AS oi(item)
        LEFT JOIN public."ShopItems" i ON i.item_id = (oi.item->>'id')::uuid
        LEFT JOIN public."DeletedImagesLogs" di ON di.item_id = (oi.item->>'id')::uuid
        LEFT JOIN public."ShopBranches" b ON o.fk_branch_id = b.branch_id
        WHERE
          o.fk_shop_id = $${params.length + 1}
          AND o.status IN ('accepted', 'delivered', 'out_for_delivery', 'ready_for_pickup', 'picked_up')
      `;

      params.push(shopId);
      addBranchCondition();
      currentSql += ` ${currentCondition} GROUP BY i.item_name, oi.item->>'id', i.item_image_links[1], di.item_image_link, b.branch_name `;
      currentSql += ` ORDER BY total_quantity DESC `;
      currentSql += ` LIMIT 10 `;

      try {
        const currentResult = await this.databaseService.query(
          currentSql,
          params,
        );
        return currentResult;
      } catch (error) {
        throw new Error('Failed to fetch top selling items');
      }
    } else if (type === 'top_locations') {
      if (branchId) {
        currentSql = `
          SELECT
            dz.zone_name AS branch_name,
            COUNT(o.order_id) AS order_count
          FROM
            public."ShopOrders" o
            JOIN public."ShopDeliveryZones" dz ON o.fk_zone_id = dz.zone_id
          WHERE
            o.fk_shop_id = $${params.length + 1}
            AND o.status IN ('accepted', 'delivered', 'out_for_delivery', 'ready_for_pickup', 'picked_up')
          ${currentCondition}
        `;
      } else {
        currentSql = `
          SELECT
            b.branch_name,
            COUNT(o.order_id) AS order_count
          FROM
            public."ShopOrders" o
            JOIN public."ShopBranches" b ON o.fk_branch_id = b.branch_id
          WHERE
            o.fk_shop_id = $${params.length + 1}
            AND o.status IN ('accepted', 'delivered', 'out_for_delivery', 'ready_for_pickup', 'picked_up')
          ${currentCondition}
        `;
      }

      params.push(shopId);
      addBranchCondition();

      if (branchId) {
        currentSql += ' GROUP BY dz.zone_name';
      } else {
        currentSql += ' GROUP BY b.branch_name';
      }

      currentSql += `
        ORDER BY order_count DESC
        LIMIT 5;
      `;

      try {
        const currentResult = await this.databaseService.query(
          currentSql,
          params,
        );
        return currentResult;
      } catch (error) {
        throw new Error('Failed to fetch top locations');
      }
    } else if (type === 'payment_mode') {
      currentSql = `
        SELECT
          p.payment_method AS mode,
          COUNT(o.order_id) AS count
        FROM
          (VALUES ('cash'), ('card'), ('online')) AS p(payment_method)
          LEFT JOIN public."ShopOrders" o ON o.payment_method = p.payment_method
          WHERE
            o.fk_shop_id = $${params.length + 1}
            AND o.status IN ('accepted', 'delivered', 'out_for_delivery', 'ready_for_pickup', 'picked_up')
      `;

      params.push(shopId);
      addBranchCondition();
      currentSql += ` ${currentCondition} GROUP BY p.payment_method `;

      try {
        const currentResult = await this.databaseService.query(
          currentSql,
          params,
        );

        const paymentModes = [
          { mode: 'cash', count: 0 },
          { mode: 'card', count: 0 },
          { mode: 'online', count: 0 },
        ];

        currentResult.forEach((row) => {
          const modeIndex = paymentModes.findIndex(
            (mode) => mode.mode === row.mode,
          );
          if (modeIndex !== -1) {
            paymentModes[modeIndex].count = +row.count;
          }
        });

        return paymentModes;
      } catch (error) {
        throw new Error('Failed to fetch payment modes');
      }
    } else if (type === 'peak_hours') {
      const currentTimeWithOffset = toZonedTime(new Date(), timeZone);
      const calculateStartEndDate = (days: number) => {
        const endDate = this.adjustForTimezone(currentTimeWithOffset, timeZone);
        const startDate = this.adjustForTimezone(
          subDays(currentTimeWithOffset, days),
          timeZone,
        );
        return { startDate, endDate };
      };
      if (dateOption === 'today') {
        const dayStart = this.adjustForTimezone(
          startOfDay(currentTimeWithOffset),
          timeZone,
        );
        const dayEnd = this.adjustForTimezone(
          endOfDay(currentTimeWithOffset),
          timeZone,
        );

        try {
          const { query, params } = this.buildOrdersQuery(
            dayStart,
            dayEnd,
            shopId,
            branchId,
          );

          const orders = await this.databaseService.query(query, params);

          const ordersByHour: { hour: string; order_count: number }[] = [];

          const dateSeries = this.generateDateSeries(
            dayStart,
            dayEnd,
            1,
            'hour',
          );

          dateSeries.forEach((hour) => {
            const hourEnd = addHours(hour, 1);

            const ordersInHour = orders.filter((order) => {
              return order.created_at >= hour && order.created_at < hourEnd;
            });

            const adjustedHour = addHours(
              hour,
              -this.getOffset(timeZone) / 3600,
            );

            const formattedHour = format(adjustedHour, 'h:mm a');
            ordersByHour.push({
              hour: formattedHour,
              order_count: ordersInHour.length,
            });
          });

          return { date_option: dateOption, data: ordersByHour };
        } catch (error) {
          throw new Error(`Failed to fetch orders: ${error.message}`);
        }
      } else if (dateOption === 'week') {
        const weekStart = this.adjustForTimezone(
          startOfWeek(currentTimeWithOffset, { weekStartsOn: 1 }),
          timeZone,
        );
        const weekEnd = this.adjustForTimezone(
          endOfWeek(currentTimeWithOffset, { weekStartsOn: 1 }),
          timeZone,
        );

        try {
          const { query, params } = this.buildOrdersQuery(
            weekStart,
            weekEnd,
            shopId,
            branchId,
          );

          const orders = await this.databaseService.query(query, params);

          const ordersByDay: { day: string; order_count: number }[] = [];

          const dateSeries = this.generateDateSeries(
            weekStart,
            weekEnd,
            1,
            'day',
          );

          dateSeries.forEach((day) => {
            const dayStart = this.adjustForTimezone(startOfDay(day), timeZone);
            const dayEnd = this.adjustForTimezone(endOfDay(day), timeZone);

            const ordersInDay = orders.filter((order) => {
              return order.created_at >= dayStart && order.created_at <= dayEnd;
            });

            ordersByDay.push({
              day: format(day, 'EEE'),
              order_count: ordersInDay.length,
            });
          });

          return { date_option: dateOption, data: ordersByDay };
        } catch (error) {
          throw new Error(`Failed to fetch orders: ${error.message}`);
        }
      } else if (dateOption === 'month') {
        const monthStart = this.adjustForTimezone(
          startOfMonth(currentTimeWithOffset),
          timeZone,
        );
        const monthEnd = this.adjustForTimezone(
          endOfMonth(currentTimeWithOffset),
          timeZone,
        );

        try {
          const { query, params } = this.buildOrdersQuery(
            monthStart,
            monthEnd,
            shopId,
            branchId,
          );

          const orders = await this.databaseService.query(query, params);

          const ordersByDay: { day: string; order_count: number }[] = [];

          const dateSeries = this.generateDateSeries(
            monthStart,
            monthEnd,
            1,
            'day',
          );

          dateSeries.forEach((day) => {
            const dayStart = this.adjustForTimezone(startOfDay(day), timeZone);
            const dayEnd = this.adjustForTimezone(endOfDay(day), timeZone);

            const ordersInDay = orders.filter((order) => {
              return order.created_at >= dayStart && order.created_at <= dayEnd;
            });

            ordersByDay.push({
              day: format(day, 'MMM d'),
              order_count: ordersInDay.length,
            });
          });

          return { date_option: dateOption, data: ordersByDay };
        } catch (error) {
          throw new Error(`Failed to fetch orders: ${error.message}`);
        }
      } else if (dateOption === 'date_range' && dateRange) {
        const fromDate = this.adjustForTimezone(
          parse(dateRange.from, 'dd-MM-yyyy', new Date()),
          timeZone,
        );
        const toDate = this.adjustForTimezone(
          parse(dateRange.to, 'dd-MM-yyyy', new Date()),
          timeZone,
        );

        try {
          const { query, params } = this.buildOrdersQuery(
            fromDate,
            toDate,
            shopId,
            branchId,
          );

          const orders = await this.databaseService.query(query, params);

          const ordersByDay: { day: string; order_count: number }[] = [];

          const dateSeries = this.generateDateSeries(
            fromDate,
            toDate,
            1,
            'day',
          );

          dateSeries.forEach((day) => {
            const dayStart = this.adjustForTimezone(startOfDay(day), timeZone);
            const dayEnd = this.adjustForTimezone(endOfDay(day), timeZone);

            const ordersInDay = orders.filter((order) => {
              return order.created_at >= dayStart && order.created_at <= dayEnd;
            });

            ordersByDay.push({
              day: format(day, 'MMM d'),
              order_count: ordersInDay.length,
            });
          });

          return { date_option: dateOption, data: ordersByDay };
        } catch (error) {
          throw new Error(`Failed to fetch orders: ${error.message}`);
        }
      } else if (dateOption === 'last_seven_days') {
        const { startDate, endDate } = calculateStartEndDate(7);

        const { query, params } = this.buildOrdersQuery(
          startDate,
          endDate,
          shopId,
          branchId,
        );

        const orders = await this.databaseService.query(query, params);

        return this.aggregateOrdersByDay(orders, startDate, endDate, timeZone);
      } else if (dateOption === 'last_thirty_days') {
        const { startDate, endDate } = calculateStartEndDate(30);

        const { query, params } = this.buildOrdersQuery(
          startDate,
          endDate,
          shopId,
          branchId,
        );

        const orders = await this.databaseService.query(query, params);

        return this.aggregateOrdersByDay(orders, startDate, endDate, timeZone);
      } else if (dateOption === 'last_ninety_days') {
        const { startDate, endDate } = calculateStartEndDate(90);

        const { query, params } = this.buildOrdersQuery(
          startDate,
          endDate,
          shopId,
          branchId,
        );

        const orders = await this.databaseService.query(query, params);

        return this.aggregateOrdersByDay(orders, startDate, endDate, timeZone);
      } else if (dateOption === 'last_24_hours') {
        const { startDate, endDate } = calculateStartEndDate(1);

        const { query, params } = this.buildOrdersQuery(
          startDate,
          endDate,
          shopId,
          branchId,
        );

        const orders = await this.databaseService.query(query, params);

        return this.aggregateOrdersByHour(orders, startDate, endDate, timeZone);
      } else {
        throw new Error(`Unsupported date option: ${dateOption}`);
      }
    } else if (type === 'top_added_items' || type === 'top_viewed_items') {
      const condition = type === 'top_added_items' ? 'ADD_ITEM' : 'VIEW_ITEM';
      currentSql = `
        WITH current_data AS (
            SELECT
                (additional_info->>'itemId')::uuid AS item_id,
                COUNT(*) AS item_count
            FROM
                public."CustomerAnalytics" o
            WHERE
                type = '${condition}'
                AND fk_shop_id IS NOT NULL
                  ${currentCondition}
                    ${branchId ? ' AND o.fk_branch_id = $2 ' : ''}
                  AND o.fk_shop_id = $1
            GROUP BY
                (additional_info->>'itemId')::uuid
        ),
        previous_data AS (
            SELECT
                (additional_info->>'itemId')::uuid AS item_id,
                COUNT(*) AS item_count
            FROM
                public."CustomerAnalytics" o
            WHERE
                type = '${condition}'
                AND fk_shop_id IS NOT NULL
                  ${previousCondition}
                    ${branchId ? ' AND o.fk_branch_id = $2 ' : ''}
                  AND o.fk_shop_id = $1
            GROUP BY
                (additional_info->>'itemId')::uuid
        ),
        combined_data AS (
            SELECT
                COALESCE(curr.item_id, prev.item_id) AS item_id,
                COALESCE(curr.item_count, 0) AS current_count,
                COALESCE(prev.item_count, 0) AS previous_count,
                RANK() OVER (ORDER BY COALESCE(curr.item_count, 0) DESC) AS current_rank
            FROM
                current_data curr
            FULL OUTER JOIN
                previous_data prev
            ON
                curr.item_id = prev.item_id
        )
        SELECT
            item_name as "itemName",
            current_count AS "totalItems",
            COALESCE(item_image_links[1], '') AS "ItemImageLink",
            CASE
                WHEN previous_count = 0 AND current_count>0 THEN 100
                WHEN previous_count = 0 THEN NULL
                ELSE ROUND(ABS((current_count - previous_count) * 100.0 / previous_count), 2)
            END AS percentage,
            CASE
                WHEN previous_count = 0 AND current_count>0 THEN '+'
                WHEN previous_count = 0 THEN NULL
                WHEN (current_count - previous_count) > 0 THEN '+'
                WHEN (current_count - previous_count) < 0 THEN '-'
                ELSE '='
            END AS sign
        FROM
            combined_data data
            JOIN public."ShopItems" item ON item.item_id = data.item_id
            WHERE current_count >0
        ORDER BY
            current_rank
        LIMIT 10;
      `;
      params.push(shopId);
      if (branchId) {
        params.push(branchId);
      }

      try {
        const currentResult = await this.databaseService.query(
          currentSql,
          params,
        );
        return currentResult;
      } catch (error) {
        throw new Error('Failed to fetch top selling items');
      }
    } else if (type === 'total_users_texted' || type === 'total_users_landed') {
      const typeCond =
        type === 'total_users_texted'
          ? 'NEW_MESSAGE_FROM_WHATSAPP'
          : 'MENU_PAGE';
      currentSql = `
          WITH current_data AS (
              SELECT
                  COUNT(*) AS current_count
              FROM
                  public."CustomerAnalytics" o
              WHERE
                  type = ${branchId ? '$3' : '$2'}
                  AND fk_shop_id IS NOT NULL
                   ${currentCondition}
                   ${branchId ? ' AND o.fk_branch_id = $2 ' : ''}
                   AND o.fk_shop_id = $1
          ),
          previous_data AS (
              SELECT
                  ${previousCondition ? `COUNT(*)` : 0} AS previous_count
              FROM
                  public."CustomerAnalytics" o
              WHERE
                  type = ${branchId ? '$3' : '$2'}
                  AND fk_shop_id IS NOT NULL
                    ${previousCondition}
                   ${branchId ? ' AND o.fk_branch_id = $2 ' : ''}
                   AND o.fk_shop_id = $1
          )
          SELECT
              COALESCE(curr.current_count, 0) AS current_count,
              COALESCE(prev.previous_count, 0) AS previous_count,
              COALESCE(CASE
                  WHEN COALESCE(prev.previous_count, 0) = 0 AND COALESCE(curr.current_count, 0)>0 THEN 100
                  WHEN COALESCE(prev.previous_count, 0) = 0 THEN NULL
                  ELSE ROUND(ABS((COALESCE(curr.current_count, 0) - COALESCE(prev.previous_count, 0)) * 100.0 / COALESCE(prev.previous_count, 0)), 2)
              END,0) AS percentage_change,
              COALESCE(CASE
                  WHEN COALESCE(prev.previous_count, 0) = 0 AND  COALESCE(curr.current_count, 0) > 0 THEN '+'
                  WHEN COALESCE(prev.previous_count, 0) = 0 THEN NULL
                  WHEN (COALESCE(curr.current_count, 0) - COALESCE(prev.previous_count, 0)) > 0 THEN '+'
                  WHEN (COALESCE(curr.current_count, 0) - COALESCE(prev.previous_count, 0)) < 0 THEN '-'
                  ELSE '='
              END,'') AS change_direction
          FROM
              current_data curr
          CROSS JOIN
              previous_data prev;
      `;
      params.push(shopId);
      if (branchId) {
        params.push(branchId);
      }
      params.push(typeCond);

      try {
        const currentResult = await this.databaseService.query(
          currentSql,
          params,
        );
        return currentResult.length > 0
          ? currentResult[0]
          : {
              current_count: 0,
              previous_count: 0,
              percentage_change: 0,
              change_direction: '',
            };
      } catch (error) {
        throw new Error('Failed to fetch top selling items');
      }
    } else if (type === 'order_count') {
      currentSql = `
      SELECT
        o.order_type AS type,
        COUNT(o.order_id) AS count
      FROM
        (VALUES ('delivery'), ('pickup')) AS t(order_type)
        LEFT JOIN public."ShopOrders" o ON o.order_type = t.order_type
        WHERE
          o.fk_shop_id = $${params.length + 1}
          AND o.status IN ('accepted', 'delivered', 'out_for_delivery', 'ready_for_pickup', 'picked_up')
    `;

      params.push(shopId);
      addBranchCondition();
      currentSql += ` ${currentCondition} GROUP BY o.order_type `;

      try {
        const currentResult = await this.databaseService.query(
          currentSql,
          params,
        );

        const serviceTypes = [
          { type: 'delivery', count: 0 },
          { type: 'pickup', count: 0 },
        ];

        currentResult.forEach((row) => {
          const typeIndex = serviceTypes.findIndex(
            (type) => type.type === row.type,
          );
          if (typeIndex !== -1) {
            serviceTypes[typeIndex].count = +row.count;
          }
        });

        return serviceTypes;
      } catch (error) {
        throw new Error('Failed to fetch service types');
      }
    }
  }

  async getCustomerUrlData(shopId: string): Promise<any> {
    const sql = `
      SELECT
        s.shop_name,
        order_link
      FROM
        public."Shops" s
      WHERE
        s.shop_id = $1
    `;
    const params = [shopId];

    try {
      const result = await this.databaseService.query(sql, params);
      return result[0];
    } catch (error) {
      throw new Error('Failed to fetch shop');
    }
  }

  async getOrderDetails(orderId: string): Promise<any> {
    const sql = `
      SELECT
        s.shop_name,
        b.trn_number,
        b.branch_name,
        b.branch_address,
        b.phone_number AS branch_phone,
        o.order_name AS order_id,
        o.customer_address,
        c.customer_name,
        c.phone_number AS customer_phone,
        o.items,
        o.bill,
        o.status,
        o.promo_code,
        o.delivery_driver,
        o.additional_notes,
        o.payment_method,
        o.order_type,
        o.created_at
      FROM public."ShopOrders" o
      LEFT JOIN public."ShopBranches" b ON o.fk_branch_id = b.branch_id
      LEFT JOIN public."Shops" s ON b.fk_shop_id = s.shop_id
      LEFT JOIN public."Customers" c ON o.fk_customer_id = c.customer_id
      WHERE o.order_id = $1
    `;

    const params = [orderId];

    try {
      const result = await this.databaseService.query(sql, params);
      return result[0];
    } catch (error) {
      throw new Error('Failed to get order details');
    }
  }

  async getShopEmailDetails(shopId: string): Promise<{
    shopEmails: string[];
    shopEmailPassword: string;
    shopSenderEmail: string;
  }> {
    const query = `
      SELECT
        c."shop_emails" AS "shopEmails",
        c."shop_email_password" AS "shopEmailPassword",
        c."shop_sender_email" AS "shopSenderEmail"
      FROM "Shops" c
      WHERE c.shop_id = $1
    `;

    const result = await this.databaseService.query(query, [shopId]);

    if (!result || result.length === 0) {
      throw new NotFoundException(`Shop with id ${shopId} not found`);
    }

    return result[0];
  }

  async getCustomerDetailsById(
    customerId: string,
    shopId: string,
    branchId: string,
  ): Promise<Customer> {
    const query = `
      SELECT
        c.phone_number,
        COALESCE(cc.customer_name, c.customer_name) AS customer_name
      FROM
        public."Customers" c
        LEFT JOIN public."Customer_Shops" cc
          ON cc.fk_customer_id = c.customer_id
          AND cc.fk_shop_id = $2
          AND $3 = ANY(cc.branch_ids)
      WHERE
        c.customer_id = $1
    `;

    const result = await this.databaseService.query(query, [
      customerId,
      shopId,
      branchId,
    ]);

    return result[0];
  }

  async getShopCancellations(
    shopId: string,
    branchId: string | null,
    paymentMode: string | null,
    dateOption: string,
    dateRange?: { from: string; to: string },
    timeZone: string = 'Asia/Dubai',
  ): Promise<any[]> {
    try {
      const params: any[] = [];
      let shopIdIndex: number | null = null;
      let branchIdIndex: number | null = null;

      if (shopId) {
        params.push(shopId);
        shopIdIndex = params.length;
      }

      if (branchId) {
        params.push(branchId);
        branchIdIndex = params.length;
      }

      let sql = `
        WITH preferred_customer AS (
          SELECT
            c.customer_id,
            COALESCE(cr.customer_name, c.customer_name) AS customer_name,
            c.phone_number,
            ROW_NUMBER() OVER (PARTITION BY c.customer_id ORDER BY cr.customer_name IS NULL) AS rn
          FROM
            public."Customers" c
          LEFT JOIN
            public."Customer_Shops" cr ON cr.fk_customer_id = c.customer_id
            ${branchIdIndex !== null ? `AND $${branchIdIndex} = ANY(cr.branch_ids)` : ''}
            AND cr.fk_shop_id = $${shopIdIndex}
        )
        SELECT
          o.order_id,
          o.order_name,
          o.created_at AS ordered_on,
          o.items,
          o.payment_method,
          o.promo_code,
          o.is_refunded`;

      if (!branchId) {
        sql += `,
          b.branch_name`;
      }

      sql += `,
          pc.customer_name,
          pc.phone_number,
          o.bill,
          o.status,
          o.delivery_driver,
          o.rejected_reason,
          CASE
            WHEN jsonb_typeof(o.customer_address) = 'object'
            THEN CONCAT_WS(', ', o.customer_address->>'sector', o.customer_address->>'building', o.customer_address->>'landmark', o.customer_address->>'google_address')
            ELSE NULL
          END AS customer_address,
          o.customer_address->>'recipient_name' AS receiver_name,
          o.customer_address->>'recipient_contact' AS receiver_number,
          o.bill,
          o.status,
          o.order_type,
          o.delivery_driver,
          o.rejected_reason
        FROM
          public."ShopOrders" o
          LEFT JOIN public."Customers" c ON c.customer_id = o.fk_customer_id
          LEFT JOIN preferred_customer pc ON c.customer_id = pc.customer_id AND pc.rn = 1`;

      if (!branchId) {
        sql += `
          LEFT JOIN public."ShopBranches" b ON o.fk_branch_id = b.branch_id`;
      }

      sql += `
        WHERE
          o.status IN ('delivered', 'accepted', 'out_for_delivery', 'ready_for_pickup', 'picked_up', 'cancelled')`;

      const dateOptionFilter = this.getDateOptionFilter(
        dateOption,
        dateRange,
        timeZone,
      );
      sql += dateOptionFilter.currentCondition;

      if (paymentMode) {
        sql += `
          AND o.payment_method = $${params.length + 1}`;
        params.push(paymentMode);
      }

      sql += `
        AND o.fk_branch_id`;

      if (branchId) {
        sql += `
          = $${branchIdIndex}`;
      } else {
        sql += `
          IN (
            SELECT
              b.branch_id
            FROM
              public."ShopBranches" b
            WHERE
              b.fk_shop_id = $${shopIdIndex})`;
      }

      sql += `
        ORDER BY
          o.created_at DESC;`;

      const result = await this.databaseService.query(sql, params);
      return result || [];
    } catch (error) {
      throw new Error('Failed to fetch sales data');
    }
  }

  async getShopSubscriptionStatus(
    shopId: string,
  ): Promise<{ subscription_status: boolean }> {
    const sql = `
      SELECT
        product_level_status,
        subscription_status,
        stripe_subscription_id,
        stripe_customer_id
      FROM public."Shops"
      WHERE shop_id = $1
    `;
    const result = await this.databaseService.query(sql, [shopId]);

    if (result.length === 0) {
      throw new NotFoundException('Shop not found');
    }

    const {
      product_level_status,
      subscription_status,
      stripe_subscription_id,
      stripe_customer_id,
    } = result[0];

    if (!product_level_status) {
      throw new ForbiddenException(
        'Can not toggle status as product status is disable',
      );
    }

    const isActiveSubscription =
      (!stripe_subscription_id || stripe_subscription_id.trim() === '') &&
      (!stripe_customer_id || stripe_customer_id.trim() === '');

    return {
      subscription_status: subscription_status || isActiveSubscription,
    };
  }

  async getShopStatus(shopId: string, serviceType: string): Promise<any[]> {
    let sql = '';

    if (serviceType === 'delivery') {
      sql += `
        SELECT take_orders AS delivery_module,
        EXISTS (
          SELECT 1
          FROM public."ShopBranches" b2
          WHERE b2.fk_shop_id = s.shop_id
          AND b2.branch_delivery = true
        ) AS module_enabled
      `;
    }

    if (serviceType === 'pickup') {
      sql += `
        SELECT pickup_module,
        EXISTS (
          SELECT 1
          FROM public."ShopBranches" b2
          WHERE b2.fk_shop_id = s.shop_id
          AND b2.branch_pickup = true
        ) AS module_enabled
      `;
    }

    sql += `
      FROM public."Shops" s
      WHERE shop_id = $1
    `;

    const params = [shopId];

    try {
      const shop = await this.databaseService.query(sql, params);
      return shop[0];
    } catch (error) {
      throw new Error('Failed to get shop status');
    }
  }

  async updateShopStatus(
    shopId: string,
    status: boolean,
    serviceType: string,
  ): Promise<void> {
    let sql = `
      UPDATE public."Shops" `;

    if (serviceType === 'delivery') {
      sql += `SET take_orders = $2 `;
    }

    if (serviceType === 'pickup') {
      sql += `SET pickup_module = $2 `;
    }

    sql += `
      WHERE shop_id = $1`;

    try {
      await this.databaseService.query(sql, [shopId, status]);
    } catch (error) {
      throw new Error('Failed to update shop status');
    }
  }

  async getBranchInfoStatus(
    shopId: string,
    serviceType: string,
    branchId?: string,
  ): Promise<any[]> {
    let sql = '';
    let params = [shopId];

    if (serviceType === 'delivery') {
      sql += `
        SELECT
          b.branch_id,
          b.branch_name,
          s.take_orders AS delivery_module,
          CASE
            WHEN b.branch_delivery_status = true
            AND b.status = true
            THEN true ELSE false
          END AS branch_delivery_status,
          b.branch_delivery,
          EXISTS (
            SELECT 1
            FROM public."ShopBranches" b2
            WHERE b2.fk_shop_id = s.shop_id
            AND b2.branch_delivery = true
          ) AS module_enabled `;
    }

    if (serviceType === 'pickup') {
      sql += `
        SELECT
          b.branch_id,
          b.branch_name,
          s.pickup_module,
          CASE
            WHEN b.branch_pickup_status = true
            AND b.pickup_status = true
            THEN true ELSE false
          END AS branch_pickup_status,
          b.branch_pickup,
          EXISTS (
            SELECT 1
            FROM public."ShopBranches" b2
            WHERE b2.fk_shop_id = s.shop_id
            AND b2.branch_pickup = true
          ) AS module_enabled `;
    }

    sql += `
      FROM
        public."ShopBranches" b
      LEFT JOIN
        public."Shops" s ON s.shop_id = b.fk_shop_id
      WHERE
        s.shop_id = $1
    `;

    if (branchId) {
      sql += ` AND b.branch_id = $2 `;
      params.push(branchId);
    } else {
      sql += ` AND b.fk_shop_id = $1 `;
    }

    try {
      const data = await this.databaseService.query(sql, params);
      return data;
    } catch (error) {
      throw new Error('Failed to get branch info status');
    }
  }

  async updateBranchInfoStatus(
    branchId: string,
    serviceType: string,
    status: boolean,
  ): Promise<void> {
    let sql = `
      UPDATE public."ShopBranches" `;

    if (serviceType === 'delivery') {
      sql += `SET branch_delivery_status = $2, status = $2 `;
    }

    if (serviceType === 'pickup') {
      sql += `SET branch_pickup_status = $2, pickup_status = $2 `;
    }

    sql += `
      WHERE branch_id = $1`;

    try {
      await this.databaseService.query(sql, [branchId, status]);
    } catch (error) {
      throw new Error('Failed to update branch info status');
    }
  }

  async getShopListingPageDetails(
    pageNumber: number = 1,
    pageSize: number = 12,
    userLatitude?: number,
    userLongitude?: number,
    radiusInKm = 10,
    branchEmirate?: string,
    searchInput?: string,
  ): Promise<any> {
    const queryParams = [];
    let branchesQuery = `
      SELECT DISTINCT
        s.shop_id AS "shopId",
        s.shop_name AS "shopName",
        b.branch_id AS "branchId",
        b.branch_name AS "branchName",
        fl.listing_image AS "listingImage",
        b.branch_address AS "address",
        s.phone_number AS "shopWhatsappNumber",
        branch_timings AS "branchTiming",
        COALESCE(fl.branch_emirate, '') AS "branchEmirate",
        fl.average_spend AS "averageSpend",
        COALESCE(fl.branch_tags, '{}'::varchar[]) AS "branchTags",
        branch_display_name "branchDisplayName",
        show_in_landing_page "showInLandingPage",
        b.branch_location AS "branchLocation",
        s.is_only_for_listing AS "onlyForListing"
    `;

    if (searchInput && searchInput.trim() !== '') {
      queryParams.push(searchInput.trim());
      branchesQuery += `,
        GREATEST(
          similarity(s.shop_name, $${queryParams.length}::text),
          similarity(b.branch_name, $${queryParams.length}::text),
          (SELECT COALESCE(MAX(similarity(tag, $${queryParams.length}::text)), 0)
           FROM unnest(fl.branch_tags) AS tag)
        ) AS search_rank`;
    }

    branchesQuery += `
      FROM
        public."Shops" s
      JOIN
        public."ShopBranches" b ON s.shop_id = b.fk_shop_id
      JOIN
        public."ShopLandingPage" fl ON s.shop_id = fl.fk_shop_id
          AND fl.branch_id = b.branch_id AND show_in_landing_page = true
      WHERE
        1=1
    `;

    if (branchEmirate) {
      queryParams.push(branchEmirate);
      branchesQuery += ` AND LOWER(fl.branch_emirate) = LOWER($${queryParams.length}::text)`;
    }

    if (searchInput && searchInput.trim() !== '') {
      const searchPattern =
        '%' +
        searchInput
          .trim()
          .split(/\s+/)
          .filter((term) => term.length > 0)
          .join('%') +
        '%';

      queryParams.push(searchPattern);

      branchesQuery += `
        AND (
          s.shop_name % $1::text
          OR b.branch_name % $1::text
          OR EXISTS (
            SELECT 1 FROM unnest(fl.branch_tags) AS tag
            WHERE tag % $1::text
          )
          OR s.shop_name ILIKE $${queryParams.length}
          OR b.branch_name ILIKE $${queryParams.length}
          OR EXISTS (
            SELECT 1 FROM unnest(fl.branch_tags) AS tag
            WHERE tag ILIKE $${queryParams.length}
          )
        )
      `;

      branchesQuery += ` ORDER BY search_rank DESC`;
    } else {
      branchesQuery += ` ORDER BY s.shop_name`;
    }

    const branches = await this.databaseService.query(
      branchesQuery,
      queryParams,
    );

    if (branches.length === 0) {
      return {
        currentPage: pageNumber,
        totalPages: 0,
        lastPage: true,
        listingData: [],
      };
    }

    if (
      userLatitude === undefined ||
      userLongitude === undefined ||
      userLatitude === 0 ||
      userLongitude === 0
    ) {
      const branchesWithDeliverableFlag = branches.map((branch) => ({
        ...branch,
        deliverable: false,
      }));

      const totalItems = branchesWithDeliverableFlag.length;
      const totalPages = Math.ceil(totalItems / pageSize);
      const validPageNumber = Math.max(1, Math.min(pageNumber, totalPages));
      const startIndex = (validPageNumber - 1) * pageSize;
      const endIndex = Math.min(startIndex + pageSize, totalItems);

      const paginatedData = branchesWithDeliverableFlag.slice(
        startIndex,
        endIndex,
      );
      const isLastPage = validPageNumber >= totalPages;

      return {
        currentPage: validPageNumber,
        totalPages,
        lastPage: isLastPage,
        listingData: paginatedData,
      };
    }

    const userPoint = point([userLongitude, userLatitude]);

    const zonesQuery = `
      SELECT
        fk_branch_id AS "branchId",
        coordinates
      FROM
        public."ShopDeliveryZones"
      WHERE
        status = true;
    `;

    const zones = await this.databaseService.query(zonesQuery, []);

    const zonesByBranchId = new Map();
    zones.forEach((zone) => {
      if (
        zone.coordinates &&
        Array.isArray(zone.coordinates) &&
        zone.coordinates.length >= 3
      ) {
        if (!zonesByBranchId.has(zone.branchId)) {
          zonesByBranchId.set(zone.branchId, []);
        }

        const polygonCoords = zone.coordinates.map((vertex) => [
          vertex.longitude,
          vertex.latitude,
        ]);
        polygonCoords.push(polygonCoords[0]);

        if (polygonCoords.length >= 4) {
          try {
            const zonePolygon = polygon([polygonCoords]);
            zonesByBranchId.get(zone.branchId).push({
              polygonCoords,
              zonePolygon,
              originalCoordinates: zone.coordinates,
            });
          } catch (polygonError) {
            console.warn(
              `Skipping invalid polygon for branch ${zone.branchId}:`,
              polygonError.message,
            );
          }
        }
      }
    });

    const processedBranches = await Promise.all(
      branches.map(async (branch) => {
        try {
          let distanceFromUser = null;
          let inDeliveryZone = false;

          if (
            branch.branchLocation &&
            branch.branchLocation.latitude &&
            branch.branchLocation.longitude
          ) {
            const branchPoint = point([
              branch.branchLocation.longitude,
              branch.branchLocation.latitude,
            ]);

            distanceFromUser = distance(userPoint, branchPoint, {
              units: 'kilometres',
            });
          }

          if (zonesByBranchId.has(branch.branchId)) {
            const branchZones = zonesByBranchId.get(branch.branchId);

            for (const zone of branchZones) {
              if (booleanPointInPolygon(userPoint, zone.zonePolygon)) {
                inDeliveryZone = true;
                break;
              }
            }
          }

          const isDeliverable =
            inDeliveryZone ||
            (distanceFromUser !== null && distanceFromUser <= radiusInKm);

          return {
            ...branch,
            distanceFromUser,
            inDeliveryZone,
            deliverable: isDeliverable,
          };
        } catch (error) {
          console.error(`Error processing branch ${branch.branchId}:`, error);
          return {
            ...branch,
            distanceFromUser: null,
            inDeliveryZone: false,
            deliverable: false,
            processingError: true,
          };
        }
      }),
    );

    const branchesWithDeliverableFlag = processedBranches.map((branch) => {
      const isDeliverable =
        branch.inDeliveryZone ||
        (branch.distanceFromUser !== null &&
          branch.distanceFromUser <= radiusInKm);

      return {
        ...branch,
        deliverable: isDeliverable,
      };
    });

    const finalResults = branchesWithDeliverableFlag.sort((a, b) => {
      if (!a.onlyForListing && b.onlyForListing) return -1;
      if (a.onlyForListing && !b.onlyForListing) return 1;

      const getCategoryScore = (branch) => {
        if (branch.inDeliveryZone && branch.deliverable) return 1;
        if (branch.inDeliveryZone && !branch.deliverable) return 2;
        if (!branch.inDeliveryZone && branch.deliverable) return 3;
        return 4;
      };

      const scoreA = getCategoryScore(a);
      const scoreB = getCategoryScore(b);

      if (scoreA !== scoreB) {
        return scoreA - scoreB;
      }

      if (a.distanceFromUser === null && b.distanceFromUser === null) return 0;
      if (a.distanceFromUser === null) return 1;
      if (b.distanceFromUser === null) return -1;

      return a.distanceFromUser - b.distanceFromUser;
    });

    const cleanedResults = finalResults.map((branch) => {
      const { inDeliveryZone, ...rest } = branch;
      return rest;
    });

    const totalItems = cleanedResults.length;
    const totalPages = Math.ceil(totalItems / pageSize);
    const validPageNumber = Math.max(1, Math.min(pageNumber, totalPages));
    const startIndex = (validPageNumber - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, totalItems);

    const paginatedData = cleanedResults.slice(startIndex, endIndex);
    const isLastPage = validPageNumber >= totalPages;

    return {
      currentPage: validPageNumber,
      totalPages,
      lastPage: isLastPage,
      listingData: paginatedData,
    };
  }

  private buildOrdersQuery(
    startDate: Date,
    endDate: Date,
    shopId: string,
    branchId?: string,
  ) {
    let query = `
      SELECT * FROM public."ShopOrders"
      WHERE created_at >= $1
      AND created_at <= $2
      AND fk_shop_id = $3
      AND status IN ('accepted', 'delivered', 'out_for_delivery', 'ready_for_pickup', 'picked_up')
    `;
    const params = [startDate, endDate, shopId];

    if (branchId) {
      query += ` AND fk_branch_id = $4`;
      params.push(branchId);
    }

    return { query, params };
  }

  private aggregateOrdersByDay(
    orders: any[],
    startDate: Date,
    endDate: Date,
    timeZone: string,
  ) {
    const ordersByDay: { day: string; order_count: number }[] = [];
    const dateSeries = this.generateDateSeries(startDate, endDate, 1, 'day');

    dateSeries.forEach((day) => {
      const dayStart = this.adjustForTimezone(startOfDay(day), timeZone);
      const dayEnd = this.adjustForTimezone(endOfDay(day), timeZone);

      const ordersInDay = orders.filter((order) => {
        return order.created_at >= dayStart && order.created_at <= dayEnd;
      });

      ordersByDay.push({
        day: format(day, 'MMM d'),
        order_count: ordersInDay.length,
      });
    });

    return { date_option: 'day', data: ordersByDay };
  }

  private aggregateOrdersByHour(
    orders: any[],
    startDate: Date,
    endDate: Date,
    timeZone: string,
  ) {
    const ordersByHour: { hour: string; order_count: number }[] = [];
    const dateSeries = this.generateDateSeries(startDate, endDate, 1, 'hour');

    dateSeries.forEach((hour) => {
      const hourEnd = addHours(hour, 1);

      const ordersInHour = orders.filter((order) => {
        return order.created_at >= hour && order.created_at < hourEnd;
      });

      const adjustedHour = addHours(hour, -this.getOffset(timeZone) / 3600);

      const formattedHour = format(adjustedHour, 'h:mm a');
      ordersByHour.push({
        hour: formattedHour,
        order_count: ordersInHour.length,
      });
    });

    return { date_option: 'hour', data: ordersByHour };
  }
  async getShopOnboardingDetailsById(shopId: string) {
    const query = `
       SELECT
        s.shop_id,
        s.shop_name,
        s.phone_number,
        s.is_only_for_listing,
        s.inbox_access_enabled,
        s.enable_support,
        s.shop_auto_accept,
        s.pickup_module,
        json_agg(
          json_build_object(
            'branch_id', b.branch_id,
            'branch_name', b.branch_name,
            'google_maps_link', b.branch_maps_url,
            'branch_min_cart_amount', b.branch_min_cart_amount,
            'branch_payment_modes', b.branch_payment_modes,
            'payment_methods', b.payment_methods->0,
            'payment_config', b.payment_methods->0,
            'branch_timings', b.branch_timings,
            'branch_address', b.branch_address,
            'branch_location', b.branch_location,
            'branch_delivery', b.branch_delivery,
            'branch_pickup', b.branch_pickup,
            'driver_module', b.delivery_module,
            'total_orders', branch_orders.order_count,
            'payment_webhook_secret',b.payment_webhook_secret
          ) ORDER by b.created_at
        ) FILTER (WHERE b.branch_id IS NOT NULL)  as branches
      FROM "Shops" s
      LEFT JOIN "ShopBranches" b ON b.fk_shop_id = s.shop_id
      LEFT JOIN (
        SELECT fk_branch_id, COUNT(*) as order_count ,fk_shop_id
        FROM "ShopOrders"
        GROUP BY fk_branch_id , fk_shop_id
      ) branch_orders ON branch_orders.fk_branch_id = b.branch_id AND branch_orders.fk_shop_id = s.shop_id
      WHERE s.shop_id = $1
      GROUP BY
        s.shop_id,
        s.shop_name,
        s.phone_number,
        s.is_only_for_listing,
        s.inbox_access_enabled,
        s.enable_support,
        s.shop_auto_accept,
        s.pickup_module
    `;
    const result = await this.databaseService.query(query, [shopId]);
    if (!result[0]) {
      return null;
    }    if (!result[0].branches) {
      return {
        shop: {
          shop_name: result[0].shop_name,
          shop_id: result[0].shop_id,
          phone_number: result[0].phone_number,
          is_only_for_listing: result[0].is_only_for_listing,
          inbox_access_enabled: result[0].inbox_access_enabled || false,
          enable_support: result[0].enable_support || false,
          shop_auto_accept: result[0].shop_auto_accept || false,
          pickup_module: result[0].pickup_module || false,
        },
        branches: [],
      };
    }

    // Handle case where shop has no branches
    const rawBranches =
      result[0].branches.length === 0 ? [] : result[0].branches;

    // Convert branch_payment_modes array to payment_methods object
    const formatPaymentMethods = (paymentModes: string[] = ['cash']) => {
      return {
        cash: paymentModes.includes('cash'),
        card: paymentModes.includes('card'),
        online: paymentModes.includes('online'),
      };
    };    return {
      shop: {
        shop_name: result[0].shop_name,
        shop_id: result[0].shop_id,
        phone_number: result[0].phone_number,
        is_only_for_listing: result[0].is_only_for_listing,
        inbox_access_enabled: result[0].inbox_access_enabled || false,
        enable_support: result[0].enable_support || false,
        shop_auto_accept: result[0].shop_auto_accept || false,
        pickup_module: result[0].pickup_module || false,
      },
      branches: rawBranches.map((branch) => ({
        branch_id: branch.branch_id,
        branch_name: branch.branch_name,
        google_maps_link: branch.google_maps_link || '',
        timings: branch.branch_timings,
        longitude: branch.branch_location?.longitude || 0,
        latitude: branch.branch_location?.latitude || 0,
        branch_address: branch.branch_address,
        branch_delivery: branch.branch_delivery ?? true,
        branch_pickup: branch.branch_pickup ?? false,
        driver_module: branch.driver_module ?? false,
        payment_methods: formatPaymentMethods(branch.branch_payment_modes),
        branch_min_cart_amount: branch.branch_min_cart_amount || 0,
        payment_config: branch.payment_methods
          ? {
              ...branch.payment_methods,
              payment_webhook_secret: branch.payment_webhook_secret,
            }
          : { payment_method: '', payment_api_key: '' },
        no_of_orders: branch.total_orders,
      })),
    };
  }
  async updateShopBranches(updateBranchesDto: UpdateShopBranchesDto) {
    try {
      const { shop, branches } = updateBranchesDto;

      // Check if shop exists
      const checkQuery = `
        SELECT shop_id
        FROM "Shops"
        WHERE shop_id = $1`;

      const existingShop = await this.databaseService.query(checkQuery, [
        shop.shop_id,
      ]);
      if (existingShop.length === 0) {
        throw new Error(`Shop with ID ${shop.shop_id} not found`);
      }      // Update shop details
      const updateShopQuery = `
        UPDATE "Shops"
        SET
          shop_name = $1,
          phone_number = $2,
          is_only_for_listing = $3,
          inbox_access_enabled = $4,
          enable_support = $5,
          shop_auto_accept = $6,
          pickup_module = $7
        WHERE shop_id = $8`;

      await this.databaseService.query(updateShopQuery, [
        shop.shop_name,
        shop.phone_number,
        shop.is_only_for_listing,
        shop.inbox_access_enabled ?? false,
        shop.enable_support ?? false,
        shop.shop_auto_accept ?? false,
        shop.pickup_module ?? false,
        shop.shop_id,
      ]);

      // Get existing branches for this shop
      const getExistingBranchesQuery = `
        SELECT branch_id
        FROM "ShopBranches"
        WHERE fk_shop_id = $1`;
      const existingBranches = await this.databaseService.query(
        getExistingBranchesQuery,
        [shop.shop_id],
      );
      const existingBranchIds = new Set(
        existingBranches.map((b) => b.branch_id),
      );
      const givenBranchIds = new Set(branches.map((b) => b.branch_id));

      // Delete branches that are not in the new payload
      for (const branch of existingBranches) {
        if (!givenBranchIds.has(branch.branch_id)) {
          const deleteBranchQuery = `
            DELETE FROM "ShopBranches"
            WHERE branch_id = $1 AND fk_shop_id = $2`;
          await this.databaseService.query(deleteBranchQuery, [
            branch.branch_id,
            shop.shop_id,
          ]);
        }
      }

      // Track new branch IDs for AuthMasterMerchant updates
      const newBranchIds: string[] = [];

      // Process each branch - update if exists, create if new
      for (const branch of branches) {
        const branchTimings = JSON.stringify(branch.timings);
        const paymentModes = Object.entries(branch.payment_methods)
          .filter(([, enabled]) => enabled)
          .map(([mode]) => mode);

        if (existingBranchIds.has(branch.branch_id)) {          // Update existing branch
          const updateQuery = `
            UPDATE "ShopBranches"
            SET
              branch_name = $1,
              branch_maps_url = $2,
              branch_timings = $3::jsonb,
              branch_min_cart_amount = $4,
              branch_payment_modes = $5,
              phone_number = $6,
              branch_location = $7::jsonb,
              branch_address = $8,
              branch_delivery = $9,
              branch_pickup = $10,
              payment_methods = $11::jsonb,
              delivery_module = $14,
              payment_webhook_secret = $15
            WHERE branch_id = $12 AND fk_shop_id = $13`;

          await this.databaseService.query(updateQuery, [
            branch.branch_name,
            branch.google_maps_link || '',
            branchTimings,
            branch.branch_min_cart_amount,
            paymentModes,
            shop.phone_number,
            JSON.stringify({
              latitude: branch.latitude,
              longitude: branch.longitude,
            }),
            branch.branch_address,
            branch.branch_delivery ?? true,
            branch.branch_pickup ?? false,
            JSON.stringify([branch.payment_config]),
            branch.branch_id,
            shop.shop_id,
            branch.driver_module ?? false,
            branch.payment_config?.payment_webhook_secret || '',
          ]);
        } else {          // Insert new branch
          newBranchIds.push(branch.branch_id);
          
          const insertQuery = `
            INSERT INTO "ShopBranches" (
              branch_id,
              fk_shop_id,
              branch_name,
              branch_maps_url,
              branch_timings,
              branch_min_cart_amount,
              branch_payment_modes,
              phone_number,
              branch_location,
              branch_address,
              branch_delivery,
              branch_pickup,
              payment_methods,
              trn_number,
              delivery_module,
              payment_webhook_secret 
            ) VALUES ($1, $2, $3, $4, $5::jsonb, $6, $7, $8, $9::jsonb, $10, $11, $12, $13::jsonb, $14, $15, $16)`;

          await this.databaseService.query(insertQuery, [
            branch.branch_id,
            shop.shop_id,
            branch.branch_name,
            branch.google_maps_link || '',
            branchTimings,
            branch.branch_min_cart_amount,
            paymentModes,
            shop.phone_number,
            JSON.stringify({
              latitude: branch.latitude,
              longitude: branch.longitude,
            }),
            branch.branch_address,
            branch.branch_delivery ?? true,
            branch.branch_pickup ?? false,
            JSON.stringify([branch.payment_config]),
            '1234567890',
            branch.driver_module ?? false,
            branch.payment_config?.payment_webhook_secret || '',
          ]);

          // Create a default delivery zone for the new branch
          const createDefaultZoneQuery = `
            INSERT INTO "ShopDeliveryZones" (
              fk_branch_id,
              coordinates,
              zone_name,
              delivery_fee,
              status,
              fk_shop_id,
              min_cart_amount
            ) VALUES ($1, $2::jsonb, $3, $4, $5, $6, $7)`;

          // Default zone parameters
          const defaultZone = {
            coordinates: JSON.stringify([
              {
                latitude: 25.30074929867603,
                longitude: 55.326288350516165,
              },
              {
                latitude: 24.528556248984625,
                longitude: 54.62041721978427,
              },
              {
                latitude: 24.32599806981801,
                longitude: 55.73552913847765,
              },
              {
                latitude: 24.947634972459205,
                longitude: 55.79595470252205,
              },
            ]),
            zoneName: 'Default Zone',
            deliveryFee: 0,
            minCartAmount: branch.branch_min_cart_amount || 0,
          };

          await this.databaseService.query(createDefaultZoneQuery, [
            branch.branch_id,
            defaultZone.coordinates,
            defaultZone.zoneName,
            defaultZone.deliveryFee,
            true,
            shop.shop_id,
            defaultZone.minCartAmount,
          ]);
        }
      }

      // Update AuthMasterMerchant branch_ids for owner users - only for new branches
      if (newBranchIds.length > 0) {
        await this.updateAuthMasterMerchantBranchIds(shop.shop_id, newBranchIds);
      }

      // this.storeShopDetailsInTheExcel(updateBranchesDto);

      return {
        success: true,
        message: 'Branches updated successfully',
      };
    } catch (error) {
      if (error.message.includes('not found')) {
        throw error;
      }
      throw new Error(`Failed to update shop branches: ${error.message}`);
    }
  }

  async createShopOnboarding(createShopOnboardingDto: CreateShopOnboardingDto) {
    const { shop } = createShopOnboardingDto;

    try {
      // Check if shop ID already exists
      const checkQuery = `
        SELECT shop_id
        FROM "Shops"
        WHERE shop_id = $1`;

      const existingShop = await this.databaseService.query(checkQuery, [
        shop.shop_id,
      ]);
      if (existingShop.length > 0) {
        throw new Error(`Shop with ID ${shop.shop_id} already exists`);
      }

      // Insert shop
      const shopQuery = `
        INSERT INTO "Shops" (
          shop_id,
          shop_name,
          phone_number,
          order_link,
          is_only_for_listing
        ) VALUES ($1, $2, $3, $4, $5)
        RETURNING shop_id`;

      const shopId = await this.databaseService.query(shopQuery, [
        shop.shop_id,
        shop.shop_name,
        shop.phone_number,
        `https://customer.justcravin.com/commerce/${shop.shop_id}/auth`,
        shop.is_only_for_listing,
      ]);
      return shopId;
    } catch (error) {
      if (error.message.includes('already exists')) {
        throw error;
      }
      throw new Error(`Failed to create shop: ${error.message}`);
    }
  }

  private async storeShopDetailsInTheExcel(data: UpdateShopBranchesDto) {
    const auth = new google.auth.GoogleAuth({
      keyFile: 'src/google.json',
      scopes: 'https://www.googleapis.com/auth/spreadsheets',
    });

    // Create client instance for auth
    const client = await auth.getClient();

    // Instance of Google Sheets API
    const googleSheets = google.sheets({
      version: 'v4',
      auth: client as any,
    });

    const spreadsheetId = '1MvmLZ7aQNHJ5A3efgt9pyl4rR8MqOx7bNACwxT0Le3k';

    try {
      const getRows = await googleSheets.spreadsheets.values.get({
        spreadsheetId,
        range: 'Shops',
      });

      const rows = getRows.data.values || [];
      const headerRow = rows[0] || [];
      const shopIdIndex = headerRow.indexOf('Shop_ID');

      // Prepare the new row data
      const newRowData = [
        data.shop.shop_id,
        data.shop.shop_name,
        data.shop.phone_number,
        data.shop.is_only_for_listing.toString(),
        data.branches.length.toString(),
        JSON.stringify(
          data.branches
            .map((branch) => {
              return `
${branch.branch_name},${branch.branch_address}
          `;
            })
            .join(` `),
        ),
      ];

      // Find if shop already exists
      const existingRowIndex = rows.findIndex(
        (row, index) => index > 0 && row[shopIdIndex] === data.shop.shop_id,
      );

      if (existingRowIndex === -1) {
        // Add new row if shop doesn't exist
        await googleSheets.spreadsheets.values.append({
          spreadsheetId,
          range: 'Shops',
          valueInputOption: 'RAW',
          requestBody: {
            values: [newRowData],
          },
        });
      } else {
        // Update existing row if shop exists
        const range = `Shops!A${existingRowIndex + 1}:${String.fromCharCode(65 + newRowData.length - 1)}${existingRowIndex + 1}`;
        await googleSheets.spreadsheets.values.update({
          spreadsheetId,
          range,
          valueInputOption: 'RAW',
          requestBody: {
            values: [newRowData],
          },
        });
      }

      return {
        success: true,
        message:
          existingRowIndex === -1
            ? 'Shop details added successfully'
            : 'Shop details updated successfully',
      };
    } catch (error) {}
  }

  async getOnboardingListingDetails(shopId: string) {
    try {
      const sql = `
        SELECT
          fl.branch_emirate,
          fl.branch_tags,
          fl.more_info,
          fl.popular_items,
          fl.known_for,
          fl.show_in_landing_page,
          fl.image_gallery,
          fl.listing_image,
          fl.shop_landline_numbers,
          fl.social_links,
          fl.branch_menus,
          fl.average_spend,
          b.branch_id,
          b.branch_name,
          s.shop_name,
          b.branch_display_name
        FROM public."ShopLandingPage" fl
        RIGHT JOIN public."ShopBranches" b
          ON fl.branch_id = b.branch_id
          AND fl.fk_shop_id = b.fk_shop_id
        JOIN public."Shops" s ON s.shop_id = b.fk_shop_id
        WHERE b.fk_shop_id = $1 AND b.branch_id IS NOT NULL
        ORDER BY b.branch_name`;

      const result = await this.databaseService.query(sql, [shopId]);

      if (result.length === 0) {
        throw new NotFoundException(`No branches found for shop ${shopId}`);
      }

      // Transform the results to match the schema format
      return result.map((branch) => ({
        branchId: branch.branch_id,
        branchName: branch.branch_name,
        branch_emirate: branch.branch_emirate || '',
        branch_tags: Array.isArray(branch.branch_tags)
          ? branch.branch_tags
          : [],
        more_info: Array.isArray(branch.more_info) ? branch.more_info : [],
        popular_items: Array.isArray(branch.popular_items)
          ? branch.popular_items
          : [],
        known_for: branch.known_for || '',
        show_in_landing_page: branch.show_in_landing_page || false,
        image_gallery: Array.isArray(branch.image_gallery)
          ? branch.image_gallery
          : [],
        listing_image: branch.listing_image || '',
        average_spend: branch.average_spend || null,
        shopName: branch.shop_name,
        branch_display_name: branch.branch_display_name || branch.branch_name,
        shop_landline_numbers: Array.isArray(branch.shop_landline_numbers)
          ? branch.shop_landline_numbers
          : [],
        social_links: branch.social_links || {},
        branch_menus: Array.isArray(branch.branch_menus)
          ? branch.branch_menus
          : [],
      }));
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(
        `Failed to fetch onboarding listing details: ${error.message}`,
      );
    }
  }

  async saveLandingPageDetails(shopId: string, data: LandingPageDto) {
    try {
      const sql = `
        INSERT INTO public."ShopLandingPage" (
          fk_shop_id,
          branch_id,
          branch_emirate,
          branch_tags,
          more_info,
          popular_items,
          known_for,
          show_in_landing_page,
          image_gallery,
          listing_image,
          shop_landline_numbers,
          social_links,
          branch_menus,
          average_spend
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
        ON CONFLICT (fk_shop_id, branch_id)
        DO UPDATE SET
          branch_emirate = EXCLUDED.branch_emirate,
          branch_tags = EXCLUDED.branch_tags,
          more_info = EXCLUDED.more_info,
          popular_items = EXCLUDED.popular_items,
          known_for = EXCLUDED.known_for,
          show_in_landing_page = EXCLUDED.show_in_landing_page,
          image_gallery = EXCLUDED.image_gallery,
          listing_image = EXCLUDED.listing_image,
          shop_landline_numbers = EXCLUDED.shop_landline_numbers,
          social_links = EXCLUDED.social_links,
          branch_menus = EXCLUDED.branch_menus,
          average_spend = EXCLUDED.average_spend
        RETURNING *`;

      const result = await this.databaseService.query(sql, [
        shopId,
        data.branchId,
        data.branch_emirate,
        data.branch_tags,
        data.more_info,
        data.popular_items,
        data.known_for,
        data.show_in_landing_page,
        data.image_gallery,
        data.listing_image,
        data.shop_landline_numbers || [],
        data.social_links || {},
        data.branch_menus || [],
        data.average_spend || null,
      ]);

      // update branch_display_name in branches table
      const sqlQuery = `
        UPDATE public."ShopBranches"
        SET branch_display_name= $1
        WHERE branch_id = $2
        `;
      await this.databaseService.query(sqlQuery, [
        data.branch_display_name,
        data.branchId,
      ]);

      return result[0];
    } catch (error) {
      throw new Error(`Failed to save landing page details: ${error.message}`);
    }
  }

  async getShopsList(): Promise<any[]> {
    try {
      const sql = `
    SELECT
        r.shop_id as id,
        r.shop_id as "shopId",
        r.shop_name as name,
        r.phone_number as phone,
        CASE
          WHEN r.shop_whatsapp_token IS NOT NULL THEN true
          ELSE false
        END as "isWhatsappOnboardingCompleted",
        r.created_at as "addedOn",
        r.is_only_for_listing,
        r.product_level_status as "isVisible"
      FROM
        public."Shops" r
      ORDER BY
        r.created_at DESC
    `;

      const result = await this.databaseService.query(sql, []);
      return result || [];
    } catch (error) {
      throw new Error('Failed to fetch shops list: ' + error.message);
    }
  }

  /**
   * Delete a shop and all related data
   * @param shopId The ID of the shop to delete
   */
  async deleteShop(shopId: string): Promise<void> {
    // First check if the shop exists
    const checkShopQuery = `SELECT shop_id FROM "Shops" WHERE shop_id = $1`;
    const shopExists = await this.databaseService.query(checkShopQuery, [
      shopId,
    ]);

    if (!shopExists || shopExists.length === 0) {
      throw new NotFoundException(`Shop with ID ${shopId} not found`);
    }

    // Get all branch IDs for this shop
    const branchQuery = `SELECT branch_id FROM "ShopBranches" WHERE fk_shop_id = $1`;
    const branches = await this.databaseService.query(branchQuery, [shopId]);
    const branchIds = branches.map(
      (branch: { branch_id: string }) => branch.branch_id,
    );

    try {
      // Delete tracking codes
      await this.databaseService.query(
        `DELETE FROM "ShopTrackingCodes" WHERE fk_shop_id = $1`,
        [shopId],
      );

      // Delete landing page data
      await this.databaseService.query(
        `DELETE FROM "ShopLandingPage" WHERE fk_shop_id = $1`,
        [shopId],
      );

      // Delete customer shop links
      await this.databaseService.query(
        `DELETE FROM "Customer_Shops" WHERE fk_shop_id = $1`,
        [shopId],
      );

      // Delete delivery zones
      if (branchIds.length > 0) {
        await this.databaseService.query(
          `DELETE FROM "ShopDeliveryZones" WHERE fk_shop_id = $1`,
          [shopId],
        );
      }

      // Delete shop orders
      await this.databaseService.query(
        `DELETE FROM "ShopOrders" WHERE fk_shop_id = $1`,
        [shopId],
      );

      // Delete shop items
      if (branchIds.length > 0) {
        const branchIdsPlaceholder = branchIds
          .map((_: any, idx: number) => `$${idx + 2}`)
          .join(',');
        await this.databaseService.query(
          `DELETE FROM "ShopItems" WHERE fk_branch_id IN (${branchIdsPlaceholder})`,
          [shopId, ...branchIds],
        );
      }

      // Delete shop categories
      await this.databaseService.query(
        `DELETE FROM "ShopCategories" WHERE fk_shop_id = $1`,
        [shopId],
      );

      // Delete shop branches
      await this.databaseService.query(
        `DELETE FROM "ShopBranches" WHERE fk_shop_id = $1`,
        [shopId],
      );

      // Delete auth records for this shop
      await this.databaseService.query(
        `DELETE FROM "AuthMasterMerchant" WHERE shop_id = $1`,
        [shopId],
      );

      // Finally, delete the shop itself
      await this.databaseService.query(
        `DELETE FROM "Shops" WHERE shop_id = $1`,
        [shopId],
      );
    } catch (error) {
      throw new Error(`Failed to delete shop: ${error.message}`);
    }
  }

  /**
   * Updates the branch_ids array in AuthMasterMerchant for owner users of a shop
   * @param shopId - The shop ID
   * @param newBranchIds - Array of new branch IDs to add
   */
  private async updateAuthMasterMerchantBranchIds(
    shopId: string,
    newBranchIds: string[],
  ): Promise<void> {
    try {
      // Find owner users for this shop
      const findOwnerQuery = `
        SELECT user_id, branch_ids
        FROM public."AuthMasterMerchant"
        WHERE shop_id = $1 
        AND user_type = 'owner'
      `;
      
      const ownerUsers = await this.databaseService.query(findOwnerQuery, [
        shopId,
      ]);

      // Update each owner user's branch_ids
      for (const user of ownerUsers) {
        const currentBranchIds = user.branch_ids || [];
        
        // Add new branch IDs to existing ones (avoid duplicates)
        const updatedBranchIds = [
          ...new Set([...currentBranchIds, ...newBranchIds]),
        ];

        const updateQuery = `
          UPDATE public."AuthMasterMerchant"
          SET branch_ids = $1
          WHERE user_id = $2
        `;

        await this.databaseService.query(updateQuery, [
          updatedBranchIds,
          user.user_id,
        ]);
      }
    } catch (error) {
      throw new Error(
        `Failed to update AuthMasterMerchant branch IDs: ${error.message}`,
      );
    }
  }

  /**
   * Toggle visibility of a shop and all its branches
   * @param shopId The ID of the shop to toggle visibility
   * @param status The status to set (true for visible, false for hidden)
   */
  async toggleShopVisibility(shopId: string, status: boolean): Promise<void> {
    // First check if the shop exists
    const checkShopQuery = `SELECT shop_id FROM "Shops" WHERE shop_id = $1`;
    const shopExists = await this.databaseService.query(checkShopQuery, [
      shopId,
    ]);

    if (!shopExists || shopExists.length === 0) {
      throw new NotFoundException(`Shop with ID ${shopId} not found`);
    }

    try {
      // Update shop level statuses
      await this.databaseService.query(
        `UPDATE "Shops"
         SET product_level_status = $2
         WHERE shop_id = $1`,
        [shopId, status],
      );

      // // Get all branch IDs for this shop
      // const branchQuery = `SELECT branch_id FROM "ShopBranches" WHERE fk_shop_id = $1`;
      // const branches = await this.databaseService.query(branchQuery, [shopId]);

      // // Update all branches for this shop
      // for (const branch of branches) {
      //   await this.databaseService.query(
      //     `UPDATE "ShopBranches"
      //      SET branch_delivery_status = $2,
      //          branch_pickup_status = $2,
      //          status = $2,
      //          pickup_status = $2
      //      WHERE branch_id = $1`,
      //     [branch.branch_id, status],
      //   );
      // }
    } catch (error) {
      throw new Error(`Failed to toggle shop visibility: ${error.message}`);
    }
  }
}

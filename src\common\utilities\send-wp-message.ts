import axios, { AxiosResponse } from 'axios';

export const sendWhatsAppMessage = async (
  from: string,
  to: string,
  shopWhatsAppToken: string,
  otp: number,
) => {
  const apiUrl = `https://graph.facebook.com/v20.0/${from}/messages`;

  const payload = {
    messaging_product: 'whatsapp',
    recipient_type: 'individual',
    to,
    type: 'text',
    text: {
      body: `*${otp}* is your verification code. For your security, do not share this code.`,
    },
  };

  const headers = {
    Authorization: `Bearer ${shopWhatsAppToken}`,
    'Content-Type': 'application/json',
  };

  try {
    const response: AxiosResponse = await axios.post(apiUrl, payload, {
      headers,
    });
    if (response.status !== 200) {
      throw new Error(
        `Failed to send WhatsApp message. Status: ${response.status}: ${response.data}`,
      );
    }
    return response.data;
  } catch (error) {
    throw new Error(`Failed to send WhatsApp message. ${error.message}`);
  }
};

import { Module, ValidationPipe } from '@nestjs/common';
import { APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { ScheduleModule } from '@nestjs/schedule';
import { CommerceWhatsappController } from 'src/modules/commerce-wa/whatsapp.controller';
import { CommerceWhatsappService } from 'src/modules/commerce-wa/whatsapp.service';
import { CommonController } from 'src/modules/common/common.controller';
import { CommonService } from 'src/modules/common/common.service';
import { TrackingController } from 'src/modules/tracking/tracking.controller';
import { TrackingService } from 'src/modules/tracking/tracking.service';
import { BranchesController } from '../../modules/branches/branches.controller';
import { BranchesService } from '../../modules/branches/branches.service';
import { CustomerQueryService } from '../../modules/customers/customer-queries';
import { CustomersController } from '../../modules/customers/customers.controller';
import { CustomersService } from '../../modules/customers/customers.service';
import { ShopsController } from '../../modules/shops/shops.controller';
import { ShopsService } from '../../modules/shops/shops.service';
import { AuthModule } from '../auth/auth.module';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ConfigAppModule } from '../config/config.module';
import { CronService } from '../cron/cron.service';
import { UserTrackingCronService } from '../cron/user-tracking.cron.service';
import { LoggingInterceptor } from '../interceptors/logging.interceptor';
import { ResponseInterceptor } from '../interceptors/response.interceptor';
import { RedisModule } from '../redis/redis.module';
import { SubscriptionService } from '../utilities/subscriptions';
import { WebSocketModule } from '../websocket/websocket.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    ConfigAppModule,
    AuthModule,
    ScheduleModule.forRoot(),
    RedisModule,
    WebSocketModule,
  ],
  controllers: [
    AppController,
    ShopsController,
    BranchesController,
    CustomersController,
    CommerceWhatsappController,
    CommonController,
    TrackingController,
  ],
  providers: [
    AppService,
    CronService,
    UserTrackingCronService,
    ShopsService,
    BranchesService,
    CustomersService,
    CustomerQueryService,
    CommerceWhatsappService,
    CommonService,
    TrackingService,
    JwtService,
    SubscriptionService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },

    {
      provide: APP_PIPE,
      useClass: ValidationPipe,
    },
  ],
  exports: [ConfigAppModule],
})
export class AppModule {}

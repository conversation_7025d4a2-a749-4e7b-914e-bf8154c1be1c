import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsBoolean } from 'class-validator';

export class UpdatePasswordDto {
  @ApiProperty({
    description: 'Shop ID',
    example: 'shop-123',
  })
  @IsNotEmpty()
  @IsString()
  shopId: string;


  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  password: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  branch_id?: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  newBranch?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsString()
  ownerUsername?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  managerUsername?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  staffUsername?: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  newStaff?: boolean;
}

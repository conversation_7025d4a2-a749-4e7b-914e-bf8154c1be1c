import {
  CopyObjectCommand,
  DeleteObjectCommand,
  PutObjectCommand,
} from '@aws-sdk/client-s3';
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import axios from 'axios';
import {
  addDays,
  format,
  getDay,
  isAfter,
  isBefore,
  parse,
  subDays,
} from 'date-fns';
import Strip<PERSON> from 'stripe';
import { v4 as uuidv4 } from 'uuid';
import { DatabaseService } from '../../common/config/database.services';
import { s3Client } from '../../common/utilities/s3';
import { CommonService } from '../common/common.service';
import { CustomerQueryService } from '../customers/customer-queries';
import { ShopsService } from '../shops/shops.service';
import {
  AddOnGroup,
  BreakTimings,
  CancelOrders,
  CreateAddOnDto,
  CreateBranchDto,
  CreateCategoryDto,
  CreateDeliveryZoneDto,
  CreateDiscountDto,
  CreateItemDto,
  CreateOrderDto,
  CreateQuotationOrderDto,
  UpdateBranchDetailsDto,
  UpdateDeliveryZoneDto,
  UpdatePromotionalBannersDto,
} from './dto/branches.dto';

@Injectable()
export class BranchesService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly commonService: CommonService,
    private readonly shopService: ShopsService,
    @Inject(forwardRef(() => CustomerQueryService))
    private readonly customerQueryService: CustomerQueryService,
  ) {}

  async addBranch(branchData: CreateBranchDto): Promise<void> {
    const {
      shop_id,
      branch_id,
      branch_name,
      branch_address,
      phone_number,
      branch_timings,
      branch_logo,
      branch_description,
    } = branchData;

    const sql = `
      INSERT INTO public."ShopBranches" (
        fk_shop_id,
        branch_id,
        branch_name,
        branch_address,
        phone_number,
        branch_timings,
        branch_logo,
        branch_description
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `;

    const params = [
      shop_id,
      branch_id,
      branch_name,
      branch_address,
      phone_number,
      branch_timings,
      branch_logo,
      branch_description,
    ];

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to add branch');
    }
  }

  async getAllBranches(): Promise<any[]> {
    const sql = `
      SELECT
        branch_id,
        branch_timezone,
        break_timings,
        break_status_switch,
        status,
        pickup_status,
        break_override_until
      FROM public."ShopBranches"
      WHERE
        break_timings IS NOT NULL
        AND break_timings != '[]'::jsonb
        AND (break_status_switch = true OR status = true)
    `;
    const branchesData = await this.databaseService.query(sql);
    return branchesData;
  }

  async updateBranchOrderStatus(
    branch_id: string,
    status: boolean,
  ): Promise<void> {
    const sql = `
      UPDATE public."ShopBranches"
      SET status = $2, pickup_status = $2
      WHERE branch_id = $1
    `;
    await this.databaseService.query(sql, [branch_id, status]);
  }

  async updateBranchBreakOverride(branch_id: string): Promise<void> {
    const sql = `
      UPDATE public."ShopBranches"
      SET break_override_until = NULL
      WHERE branch_id = $1
    `;
    await this.databaseService.query(sql, [branch_id]);
  }

  async parseTime(timeStr: string): Promise<[number, number]> {
    const match = timeStr.match(/(\d{1,2}):(\d{2})\s*(AM|PM)/i);

    let hours = parseInt(match[1]);
    const minutes = parseInt(match[2]);
    const period = match[3].toUpperCase();

    if (period === 'PM' && hours !== 12) {
      hours += 12;
    } else if (period === 'AM' && hours === 12) {
      hours = 0;
    }

    return [hours, minutes];
  }

  normalizeToNull(value: any): any {
    if (
      value === undefined ||
      value === null ||
      value === '' ||
      value === 'null' ||
      value === 'undefined'
    ) {
      return null;
    }
    return value;
  }

  async addItem(itemData: CreateItemDto): Promise<void> {
    const {
      branch_id,
      item_name,
      item_description,
      item_type,
      category_id,
      item_price,
      item_images,
      item_status,
      item_variants,
      item_add_ons_group,
      item_combos,
      item_quantity,
    } = itemData;

    let transformedShopAddOnsGroup: any;
    if (item_add_ons_group && item_add_ons_group.length > 0) {
      transformedShopAddOnsGroup = item_add_ons_group.map(
        (group: AddOnGroup) => ({
          id: group.id,
          selection: group.selection,
          group_name: group.group_name,
          add_ons_items: group.add_ons_items.map((item) => item.add_on_id),
        }),
      );
    }

    const sql = `
      INSERT INTO public."ShopItems" (
        fk_branch_id,
        item_name,
        item_description,
        item_type,
        fk_category_id,
        item_price,
        item_status,
        item_variants,
        item_add_ons_group,
        item_combos,
        item_quantity
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING item_id
    `;

    const params = [
      branch_id,
      item_name,
      item_description,
      item_type,
      category_id,
      item_price,
      item_status,
      JSON.stringify(item_variants),
      JSON.stringify(transformedShopAddOnsGroup),
      JSON.stringify(item_combos),
      item_quantity,
    ];

    try {
      const item = await this.databaseService.query(sql, params);
      const itemId = item[0].item_id;

      if (item_images && item_images.length > 0) {
        const imageUrls = [];

        for (let i = 0; i < item_images.length; i++) {
          const image = item_images[i];
          const extension = image.mimetype.split('/').pop();
          const timestamp = Date.now();
          const fileName = `${process.env.STORAGE_FOLDER}/branches/${branch_id}/items/${itemId}/image_${i + 1}_${timestamp}.${extension}`;

          const uploadParams = {
            Bucket: 'cravin',
            Key: fileName,
            Body: image.buffer,
            ContentType: image.mimetype,
            PutObjectAcl: 'public-read',
          };

          const command = new PutObjectCommand(uploadParams);
          await s3Client.send(command);

          const imageUrl = `https://cravin.s3.me-central-1.amazonaws.com/${fileName}`;
          imageUrls.push(imageUrl);
        }

        const updateSql = `
          UPDATE public."ShopItems"
          SET item_image_links = $2
          WHERE item_id = $1
        `;

        const updateParams = [itemId, imageUrls];
        await this.databaseService.query(updateSql, updateParams);
      }
    } catch (error) {
      throw new Error('Failed to add item');
    }
  }

  async editItem(item_id: string, itemData: CreateItemDto): Promise<void> {
    const {
      branch_id,
      item_name,
      item_description,
      item_type,
      category_id,
      item_price,
      item_images,
      item_image_links,
      item_status,
      item_variants,
      item_add_ons_group,
      item_combos,
      item_quantity,
      image_delete_index,
    } = itemData;

    const itemStatus = item_quantity === 0 ? 'out-of-stock' : item_status;

    let new_item_image_links: string[] = [];

    if (typeof item_image_links === 'string') {
      new_item_image_links = JSON.parse(item_image_links);
    } else if (Array.isArray(item_image_links)) {
      new_item_image_links = item_image_links;
    }

    let deleteIndices: number[] = [];
    if (image_delete_index && image_delete_index.length > 0) {
      if (typeof image_delete_index === 'string') {
        const parsedArray = JSON.parse(image_delete_index);
        if (Array.isArray(parsedArray)) {
          deleteIndices = parsedArray.map((item) => +item);
        }
      } else if (Array.isArray(image_delete_index)) {
        deleteIndices = image_delete_index.map((item) => +item);
      }
    }

    if (deleteIndices.length > 0 || (item_images && item_images.length > 0)) {
      for (const index of deleteIndices) {
        const urlToDelete = new_item_image_links[index - 1];
        if (
          urlToDelete &&
          urlToDelete.startsWith(
            'https://cravin.s3.me-central-1.amazonaws.com/',
          )
        ) {
          try {
            const key = urlToDelete.split(
              'https://cravin.s3.me-central-1.amazonaws.com/',
            )[1];
            const deleteParams = {
              Bucket: 'cravin',
              Key: key,
            };
            const deleteCommand = new DeleteObjectCommand(deleteParams);
            await s3Client.send(deleteCommand);
          } catch (error) {
            throw new Error('Failed to delete old image');
          }
        }
      }

      new_item_image_links = new_item_image_links.filter(
        (_, idx) => !deleteIndices.includes(idx + 1),
      );

      for (let i = 0; i < new_item_image_links.length; i++) {
        const oldUrl = new_item_image_links[i];
        if (
          oldUrl.startsWith('https://cravin.s3.me-central-1.amazonaws.com/')
        ) {
          const oldKey = oldUrl.split(
            'https://cravin.s3.me-central-1.amazonaws.com/',
          )[1];
          const extension = oldKey.split('.').pop();
          const timestamp = Date.now();
          const newKey = `${process.env.STORAGE_FOLDER}/branches/${branch_id}/items/${item_id}/image_${i + 1}_${timestamp}.${extension}`;
          const newUrl = `https://cravin.s3.me-central-1.amazonaws.com/${newKey}`;

          if (oldKey !== newKey) {
            try {
              const copyParams = {
                Bucket: 'cravin',
                CopySource: `cravin/${oldKey}`,
                Key: newKey,
              };
              const copyCommand = new CopyObjectCommand(copyParams);
              await s3Client.send(copyCommand);

              const deleteParams = {
                Bucket: 'cravin',
                Key: oldKey,
              };
              const deleteCommand = new DeleteObjectCommand(deleteParams);
              await s3Client.send(deleteCommand);

              new_item_image_links[i] = newUrl;
            } catch (error) {
              throw new Error('Failed to rename files');
            }
          }
        }
      }

      if (Array.isArray(item_images) && item_images.length > 0) {
        let nextAvailableIndex = new_item_image_links.length + 1;
        for (const image of item_images) {
          const image_extension = image.mimetype.split('/').pop();
          const timestamp = Date.now();
          const newFileName = `${process.env.STORAGE_FOLDER}/branches/${branch_id}/items/${item_id}/image_${nextAvailableIndex}_${timestamp}.${image_extension}`;
          const imageUploadParams = {
            Bucket: 'cravin',
            Key: newFileName,
            Body: image.buffer,
            ContentType: image.mimetype,
          };
          try {
            const imageCommand = new PutObjectCommand(imageUploadParams);
            await s3Client.send(imageCommand);
            const newUrl = `https://cravin.s3.me-central-1.amazonaws.com/${newFileName}`;
            new_item_image_links.push(newUrl);
          } catch (error) {
            throw new Error('Failed to upload new image');
          }
          nextAvailableIndex++;
        }
      }
    }

    let transformedShopAddOnsGroup: any;
    if (item_add_ons_group && item_add_ons_group.length > 0) {
      try {
        transformedShopAddOnsGroup = item_add_ons_group.map((group) => ({
          id: group.id,
          selection: group.selection,
          group_name: group.group_name,
          add_ons_items: group.add_ons_items.map((item) => item.add_on_id),
        }));
      } catch (error) {
        throw new Error('Failed to parse add_ons_items');
      }
    }
    // get the max item_order of category in new item category and increment by 1
    const itemOrderSql = `
      SELECT MAX(item_order) AS max_item_order,COUNT(*) AS item_count
      FROM public."ShopItems"
      WHERE fk_category_id = $1
      AND fk_branch_id = $2
    `;

    const itemOrderParams = [category_id, branch_id];
    let maxItemOrder: number | null;
    let itemCount: number;

    try {
      const result = await this.databaseService.query(
        itemOrderSql,
        itemOrderParams,
      );
      maxItemOrder = result[0]?.max_item_order || 0;
      itemCount = result[0]?.item_count || 0;
    } catch (error) {
      throw new Error('Failed to fetch max item order');
    }

    // get the old category id of item
    const oldCategoryIdSql = `
      SELECT fk_category_id , item_order
      FROM public."ShopItems"
      WHERE item_id = $1
      AND fk_branch_id = $2
    `;
    const oldCategoryIdParams = [item_id, branch_id];

    let oldCategoryId: string;
    let oldItemOrder: number | null;
    try {
      const result = await this.databaseService.query(
        oldCategoryIdSql,
        oldCategoryIdParams,
      );
      oldCategoryId = result[0]?.fk_category_id;
      oldItemOrder = result[0]?.item_order || null;
    } catch (error) {
      throw new Error('Failed to fetch old category id');
    }

    const sql = `
      UPDATE public."ShopItems"
      SET
        item_name = $3,
        item_description = $4,
        item_type = $5,
        fk_category_id = $6,
        item_price = $7,
        item_image_links = $8,
        item_status = $9,
        item_variants = $10,
        item_add_ons_group = $11,
        item_combos = $12,
        item_quantity = $13,
        item_order =  $14
      WHERE item_id = $1
      AND fk_branch_id = $2
    `;

    const params = [
      item_id,
      branch_id,
      item_name,
      item_description,
      item_type,
      category_id,
      item_price,
      new_item_image_links,
      itemStatus,
      JSON.stringify(item_variants),
      JSON.stringify(transformedShopAddOnsGroup),
      JSON.stringify(item_combos),
      item_quantity,
    ];

    if (oldCategoryId !== category_id) {
      params.push(maxItemOrder < itemCount ? null : maxItemOrder + 1);
    } else {
      params.push(oldItemOrder);
    }

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to update item');
    }
  }

  async getCategoryById(categoryId: string): Promise<any> {
    const sql = `
      SELECT
        fk_branch_id AS branch_id,
        category_name,
        category_description,
        category_availability,
        category_availability_timings
      FROM public."ShopCategories"
      WHERE category_id = $1
    `;

    const params = [categoryId];

    try {
      const category = await this.databaseService.query(sql, params);
      return category[0];
    } catch (error) {
      throw new Error('Failed to fetch category');
    }
  }

  async addCategory(categoryData: CreateCategoryDto): Promise<void> {
    const {
      branch_id,
      category_name,
      category_description,
      category_availability,
      category_availability_timings,
    } = categoryData;

    const sql = `
      INSERT INTO public."ShopCategories" (
        fk_branch_id,
        category_name,
        category_description,
        category_availability,
        category_availability_timings
      ) VALUES ($1, $2, $3, $4, $5)
      RETURNING category_id
    `;

    const params = [
      branch_id,
      category_name,
      category_description,
      category_availability,
      JSON.stringify(category_availability_timings),
    ];

    try {
      const result = await this.databaseService.query(sql, params);
      const newCategoryId = result[0].category_id;
      await this.updateBranchCategoryOrder(branch_id, [newCategoryId]);
    } catch (error) {
      throw new Error(
        'Failed to add category and update branch category order',
      );
    }
  }

  async editCategory(
    categoryId: string,
    categoryData: CreateCategoryDto,
  ): Promise<void> {
    const {
      branch_id,
      category_name,
      category_description,
      category_availability,
      category_availability_timings,
    } = categoryData;

    const sql = `
      UPDATE public."ShopCategories"
      SET
        fk_branch_id = $2,
        category_name = $3,
        category_description = $4,
        category_availability = $5,
        category_availability_timings = $6
      WHERE category_id = $1
    `;

    const params = [
      categoryId,
      branch_id,
      category_name,
      category_description,
      category_availability,
      JSON.stringify(category_availability_timings),
    ];

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to update category');
    }
  }

  async deleteCategory(categoryId: string): Promise<void> {
    const itemCheckSql = `
      SELECT COUNT(*) AS item_count
      FROM public."ShopItems"
      WHERE fk_category_id = $1
    `;
    const itemCheckParams = [categoryId];
    const itemCheckResult = await this.databaseService.query(
      itemCheckSql,
      itemCheckParams,
    );

    if (+itemCheckResult[0].item_count > 0) {
      throw new BadRequestException(
        'Cannot delete category with items linked. Please delete the items first.',
      );
    }

    const sql = `
      DELETE FROM public."ShopCategories"
      WHERE category_id = $1
      RETURNING fk_branch_id
    `;
    const params = [categoryId];

    try {
      const result = await this.databaseService.query(sql, params);
      const branchId = result[0].fk_branch_id;
      await this.removeFromBranchCategoryOrder(branchId, categoryId);
    } catch (error) {
      throw new Error('Failed to remove category');
    }
  }

  async addAddOn(addOnData: CreateAddOnDto): Promise<void> {
    const { branch_id, add_on_name, add_on_type, add_on_price } = addOnData;

    const sql = `
      INSERT INTO public."ShopAddOns" (
        fk_branch_id,
        add_on_name,
        add_on_type,
        add_on_price
      ) VALUES ($1, $2, $3, $4)
    `;

    const params = [branch_id, add_on_name, add_on_type, add_on_price];

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to add addon');
    }
  }

  async addDiscount(discountData: CreateDiscountDto): Promise<void> {
    const {
      branch_id,
      shop_id,
      discount_name,
      discount_code,
      uses_per_customer,
      applicable_customer,
      applicable_category,
      applicable_items,
      discount_type,
      discount_value,
      min_amount,
      max_amount,
      usage_method,
      total_uses,
      discount_date,
      discount_duration_day,
      applicable_order_type,
      created_by,
    } = discountData;

    const sql = `
      INSERT INTO public."ShopDiscounts" (
        discount_name,
        fk_shop_id,
        branch_ids,
        discount_code,
        uses_per_customer,
        applicable_customer,
        applicable_category,
        applicable_items,
        discount_type,
        discount_value,
        min_amount,
        max_amount,
        usage_method,
        total_uses,
        discount_date,
        discount_duration_day,
        applicable_order_type,
        created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
    `;

    const params = [
      discount_name,
      shop_id,
      [branch_id],
      discount_code,
      uses_per_customer,
      applicable_customer,
      applicable_category,
      applicable_items,
      discount_type,
      discount_value,
      min_amount,
      max_amount,
      usage_method,
      total_uses,
      discount_date,
      discount_duration_day,
      applicable_order_type,
      created_by,
    ];

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to add discount');
    }
  }

  async getAllDiscounts(shopId: string, branchIds?: string[]): Promise<any[]> {
    let sql = `
      SELECT
        d.discount_id,
        d.discount_code,
        d.discount_name,
        d.discount_date,
        d.discount_duration_day,
        d.usage_method,
        d.discount_value,
        b.branch_name,
        CASE
          WHEN d.usage_method IN ('menu', 'promo', 'auto', 'manual') THEN
            CONCAT(
              CASE d.usage_method
                WHEN 'menu' THEN 'menu - '
                WHEN 'promo' THEN 'promo - '
                WHEN 'auto' THEN 'promo - ' || 'auto - '
                WHEN 'manual' THEN 'promo - ' || 'manual - '
              END,
              CASE d.discount_type
                WHEN 'flat' THEN 'flat'
                WHEN 'percentage' THEN 'percentage'
                ELSE d.discount_type
              END
            )
          ELSE d.discount_type
        END AS discount_type,
        COALESCE(SUM(CAST(o.bill->>'total_bill' AS numeric)), 0)::real AS total_sales,
        d.status,
        d.applicable_customer,
        d.min_amount,
        d.max_amount,
        d.uses_per_customer,
        d.total_uses,
        d.applicable_order_type,
        CASE
          WHEN d.usage_method = 'menu' THEN
            JSON_AGG(
              DISTINCT JSONB_BUILD_OBJECT(
                'category_id', ci.category_id,
                'category_name', ci.category_name,
                'items', COALESCE((
                  SELECT JSONB_AGG(
                    JSONB_BUILD_OBJECT(
                      'item_name', i.item_name,
                      'discount_applicable', (
                        CASE
                          WHEN d.discount_type = 'flat' THEN
                            i.item_price - d.discount_value > 0
                          WHEN d.discount_type = 'percentage' THEN
                            i.item_price * (1 - d.discount_value / 100) > 0
                            AND (i.item_price * (1 - d.discount_value / 100) < d.max_amount)
                          WHEN d.discount_type = 'specific' THEN
                            i.item_price > d.discount_value
                          ELSE
                            TRUE
                        END
                      )
                    )
                  )
                  FROM public."ShopItems" i
                  WHERE i.fk_category_id = ci.category_id
                ), '[]'::jsonb)
              )
            )
          ELSE NULL
        END AS category_items,
        d.created_at
      FROM
        public."ShopDiscounts" d
      LEFT JOIN
        public."ShopOrders" o ON d.discount_id = o.fk_discount_id
      LEFT JOIN
        public."ShopCategories" ci
        ON ci.category_id = ANY(d.applicable_category::uuid[])
      LEFT JOIN
        public."ShopBranches" b ON b.branch_id = ANY(d.branch_ids)
      WHERE
        d.fk_shop_id = $1
    `;

    const params = [shopId];

    if (branchIds && branchIds.length > 0) {
      const placeholders = branchIds.map(
        (_, index) => `$${params.length + index + 1}`,
      );
      sql += `AND d.branch_ids && ARRAY[${placeholders.join(', ')}]::varchar[]`;
      params.push(...branchIds);
    }

    sql += `GROUP BY d.discount_id, b.branch_name, d.created_at ORDER BY d.created_at DESC`;

    try {
      const result = await this.databaseService.query(sql, params);
      return result;
    } catch (error) {
      throw new Error('Failed to fetch discounts');
    }
  }

  async updateDiscountStatus(
    discountId: string,
    status: boolean,
  ): Promise<void> {
    try {
      const currentResult = await this.databaseService.query(
        `SELECT discount_name AS "currentDiscountName",
          applicable_category AS "currentCategories",
          usage_method AS "usageMethod"
          FROM public."ShopDiscounts" WHERE discount_id = $1`,
        [discountId],
      );

      const { currentDiscountName, currentCategories, usageMethod } =
        currentResult[0];

      if (status === true && usageMethod === 'menu') {
        if (currentResult.length === 0) {
          throw new BadRequestException(
            'Discount has no categories associated',
          );
        }

        const conflictResult = await this.databaseService.query(
          `
          SELECT discount_name AS "conflictingDiscountName"
          FROM public."ShopDiscounts"
          WHERE status = true
            AND discount_id != $1
            AND usage_method = 'menu'
            AND EXISTS (
              SELECT 1
              FROM unnest($2::varchar[]) AS category
              WHERE category = ANY(applicable_category)
            )
            LIMIT 1
          `,
          [discountId, currentCategories],
        );

        if (conflictResult.length > 0) {
          const { conflictingDiscountName } = conflictResult[0];
          throw new BadRequestException(
            `Active discount ${conflictingDiscountName} is conflicting with ${currentDiscountName}. Please turn off ${conflictingDiscountName} to make ${currentDiscountName} active.`,
          );
        }
      }

      await this.databaseService.query(
        `UPDATE public."ShopDiscounts" SET status = $1 WHERE discount_id = $2`,
        [status, discountId],
      );
    } catch (error) {
      throw error;
    }
  }

  async processDiscountUsage(
    customerId: string,
    discountId: string,
    rejected: boolean = false,
  ): Promise<void> {
    try {
      const associationExists = await this.databaseService.query(
        'SELECT 1 FROM "Customer_ShopDiscounts" WHERE "fk_customer_id" = $1 AND "fk_discount_id" = $2',
        [customerId, discountId],
      );

      if (associationExists.length > 0) {
        await this.databaseService.query(
          `UPDATE "Customer_ShopDiscounts" SET "no_of_usage" = "no_of_usage" ${rejected ? '- 1' : '+ 1'} WHERE "fk_customer_id" = $1 AND "fk_discount_id" = $2`,
          [customerId, discountId],
        );
      } else {
        await this.databaseService.query(
          'INSERT INTO "Customer_ShopDiscounts" ("fk_customer_id", "fk_discount_id") VALUES ($1, $2)',
          [customerId, discountId],
        );
      }

      await this.databaseService.query(
        `UPDATE "ShopDiscounts" SET "actual_usage" = COALESCE("actual_usage", 0) ${rejected ? '- 1' : '+ 1'} WHERE "discount_id" = $1`,
        [discountId],
      );
    } catch (error) {
      throw new Error('Error processing discount usage');
    }
  }

  async addOrder(orderData: CreateOrderDto): Promise<any> {
    const {
      customer_id,
      branch_id,
      items,
      promo_code,
      discount_id,
      additional_notes,
      bill_amount,
      address,
      payment_method,
      shop_id,
      zone_id,
      order_type = 'delivery',
    } = orderData;

    const lastOrderQuery = `
      WITH branch_check AS (
        SELECT
          branch_delivery_status, branch_delivery, status,
          branch_pickup_status, branch_pickup, pickup_status
        FROM public."ShopBranches" b
        WHERE b.branch_id = $2
      ),
      latest_order AS (
        SELECT o.order_name, o.fk_shop_id
        FROM public."ShopOrders" o
        WHERE o.fk_shop_id = $1
        ORDER BY o.created_at DESC
        LIMIT 1
      )
      SELECT
        o.order_name,
        s.shop_auto_accept,
        s.shop_whatsapp_id,
        CASE
          WHEN s.product_level_status = true
          AND s.take_orders = true
          AND b.branch_delivery_status = true
          AND b.branch_delivery = true
          AND b.status = true
          THEN true
          ELSE false
        END as branch_status,
        CASE
          WHEN s.product_level_status = true
          AND s.pickup_module = true
          AND b.branch_pickup_status = true
          AND b.branch_pickup = true
          AND b.pickup_status = true
          THEN true
          ELSE false
        END as pickup_status
      FROM public."Shops" s
      LEFT JOIN latest_order o ON s.shop_id = o.fk_shop_id
      JOIN branch_check b ON true
      WHERE s.shop_id = $1;
    `;
    let lastOrderResult = null;
    let quantityCheckResult = null;

    let newOrderName = '';
    try {
      lastOrderResult = await this.databaseService.query(lastOrderQuery, [
        shop_id,
        branch_id,
      ]);

      const { branch_status, pickup_status } = lastOrderResult[0];

      if (!branch_status) {
        throw new Error(
          'The store is not accepting delivery orders, please try again later',
        );
      }

      if (order_type === 'pickup' && !pickup_status) {
        throw new Error(
          'The store is not accepting pickup orders, please try again later',
        );
      }

      const lastOrderName = +lastOrderResult[0]?.order_name?.split('-')[1] || 0;
      newOrderName = `ORD-${lastOrderName + 1}`;
    } catch (error) {
      throw new BadRequestException(
        error.message || 'Failed to fetch last order',
      );
    }

    if (promo_code) {
      try {
        await this.processDiscountUsage(customer_id, discount_id);
      } catch (error) {
        throw new Error('Failed to process discount usage');
      }
    }

    const itemQuantitiesMap = items.reduce(
      (acc, item) => {
        if (acc[item.id]) {
          acc[item.id] += item.quantity;
        } else {
          acc[item.id] = item.quantity;
        }
        return acc;
      },
      {} as { [key: string]: number },
    );

    const itemIds = Object.keys(itemQuantitiesMap);

    const quantityCheckQuery = `
      SELECT
        i.item_id,
        i.item_quantity::int4
      FROM
        public."ShopItems" i
      WHERE
        i.fk_branch_id = $1
      AND
        i.item_id = ANY($2::uuid[])
    `;

    const quantityCheckParams = [branch_id, itemIds];
    try {
      quantityCheckResult = await this.databaseService.query(
        quantityCheckQuery,
        quantityCheckParams,
      );
    } catch (error) {
      throw new Error('Failed to fetch item quantity');
    }

    const stockQuantities = quantityCheckResult.reduce(
      (
        acc: { [x: string]: any },
        row: { item_id: string | number; item_quantity: any },
      ) => {
        acc[row.item_id] = row.item_quantity;
        return acc;
      },
      {} as { [key: string]: number },
    );

    let quantityValidationError = false;
    for (const [itemId, requestedQuantity] of Object.entries(
      itemQuantitiesMap,
    )) {
      const availableQuantity = stockQuantities[itemId];
      if (requestedQuantity > availableQuantity) {
        quantityValidationError = true;
        break;
      }
    }

    if (quantityValidationError) {
      throw new BadRequestException(
        'Quantity validation failed for items in the checkout',
      );
    }

    const autoAccept = lastOrderResult[0].shop_auto_accept;

    const orderStatus =
      payment_method === 'online'
        ? 'processing'
        : autoAccept
          ? 'accepted'
          : 'initiated';

    const sql = `
      INSERT INTO public."ShopOrders" (
        order_name,
        fk_customer_id,
        fk_shop_id,
        fk_branch_id,
        items,
        promo_code,
        fk_discount_id,
        additional_notes,
        bill,
        customer_address,
        status,
        payment_method,
        fk_zone_id,
        order_type
      ) VALUES ($1, $2, $3, $4, $5::jsonb, $6, $7, $8, $9::jsonb, $10::jsonb, $11, $12, $13, $14)
      RETURNING *
    `;
    const params = [
      newOrderName,
      customer_id,
      shop_id,
      branch_id,
      JSON.stringify(items),
      promo_code,
      discount_id,
      additional_notes,
      JSON.stringify(bill_amount),
      JSON.stringify(address),
      orderStatus,
      payment_method,
      zone_id,
      order_type,
    ];
    try {
      this.commonService.clearCartFromAnalytics(
        shop_id,
        customer_id,
        branch_id,
        newOrderName,
      );
    } catch (error) {}
    let order: any[];
    try {
      order = await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to add order');
    }

    const batchSize = 10;
    const itemEntries = Object.entries(itemQuantitiesMap);

    for (let i = 0; i < itemEntries.length; i += batchSize) {
      const batch = itemEntries.slice(i, i + batchSize);

      const itemIdsBatch = batch.map(([id]) => id);
      const requestedQuantitiesBatch = batch.map(([, quantity]) => quantity);

      const updateQuantitiesQuery = `
        UPDATE
          public."ShopItems"
        SET
          item_quantity = item_quantity - subquery.requested_quantity
        FROM (
          SELECT
            unnest($2::uuid[]) AS item_id,
            unnest($3::int[]) AS requested_quantity
        ) AS subquery
        WHERE
          public."ShopItems".item_id = subquery.item_id
        AND
          public."ShopItems".fk_branch_id = $1;
      `;
      const updateQuantitiesParams = [
        branch_id,
        itemIdsBatch,
        requestedQuantitiesBatch,
      ];

      try {
        await this.databaseService.query(
          updateQuantitiesQuery,
          updateQuantitiesParams,
        );
      } catch (error) {
        throw new Error('Failed to update item quantities');
      }
    }

    const updateStatusQuery = `
      UPDATE public."ShopItems"
      SET item_status = 'out-of-stock'
      WHERE item_quantity <= 0
      AND item_status = 'in-stock';
    `;
    try {
      await this.databaseService.query(updateStatusQuery);
    } catch (error) {
      throw new Error('Failed to update item status');
    }

    try {
      await this.customerShopBranchLinking(customer_id, shop_id, branch_id);
    } catch (error) {
      throw new Error('Customer linking failed');
    }

    if (payment_method === 'online') {
      return await this.processOnlinePayment(
        order[0],
        customer_id,
        shop_id,
        order_type,
      );
    } else {
      try {
        await this.commonService.sendNotification({
          shopId: shop_id,
          branchId: branch_id,
          title: 'New Order',
          message: `New Order: ${newOrderName}`,
          url: `${process.env.MERCHANT_URL}/admin/orders`,
          orderType: autoAccept ? 'autoaccept' : '',
        });

        const { order_id, created_at, items, additional_notes, bill, status } =
          order[0];

        const customerQuery = `
          SELECT
            b.branch_name AS "branchName",
            s.shop_name AS "shopName",
            s.shop_emails AS "shopEmails",
            s.shop_sender_email AS "shopSenderEmail",
            s.shop_email_password AS "shopEmailPassword",
            b.cancellation_number AS "branchCancellationNumber",
            c.customer_name as "customerName",
            c.phone_number AS "customerPhone",
            generate_quotation
          FROM
            public."ShopOrders" o
          LEFT JOIN
            public."ShopBranches" b ON o.fk_branch_id = b.branch_id
          LEFT JOIN
            public."Customers" c ON o.fk_customer_id = c.customer_id
          LEFT JOIN
            public."Shops" s ON o.fk_shop_id = s.shop_id
          WHERE
            o.order_id = $1
        `;

        let customerDetails = await this.databaseService.query(customerQuery, [
          order_id,
        ]);

        const {
          branchName,
          customerName,
          customerPhone,
          shopName,
          shopEmails,
          shopSenderEmail,
          shopEmailPassword,
          branchCancellationNumber,
          generate_quotation,
        } = customerDetails[0];

        await fetch(process.env.WHATSAPP_URL + '/send-telegram-message', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            product: 'Commerce',
            orderName: newOrderName,
            orderId: order_id,
            shopId: shop_id,
            branchId: branch_id,
            customerId: customer_id,
            customerName: customerName,
            type: 'ORDER_SUCCESSFUL',
            isOrderSuccessful: true,
          }),
        });

        const formatPrice = (price: number) => `AED ${price.toFixed(2)}`;
        const formatPriceAddition = (price: number) =>
          price > 0 ? ` (+${formatPrice(price)})` : '';
        const paymentMethod =
          payment_method[0].toUpperCase() + payment_method.slice(1);
        const orderStatus = status[0].toUpperCase() + status.slice(1);
        const trackingLink = `${process.env.FRONTEND_URL}/${shop_id}/${order_type === 'pickup' ? 'pickup-' : ''}tracking/${order_id}`;
        const baseMessage = autoAccept
          ? '*Your order has been confirmed* ✅.'
          : '*Your order has been sent. Please wait for approval.*';
        const orderDetailsMessage = `Your Order *${newOrderName}* has been confirmed. ✅`;
        const cancellationMessage = `If you wish to cancel or amend your order, please call *+${branchCancellationNumber}*.`;
        const thankYouMessage = `\n\nThank you for ordering from *${shopName}* with Cravin.`;
        const whatsAppAcceptedMessage = `${orderDetailsMessage}\n${cancellationMessage}\n${thankYouMessage}`;

        const formatOrderDetails = () => {
          let message = '';
          const formattedDate = new Date(created_at).toLocaleDateString(
            'en-US',
            {
              weekday: 'short',
              month: 'short',
              day: 'numeric',
              year: 'numeric',
            },
          );

          message += `*Order Details:*\n\nOrder ID: ${newOrderName}\n`;
          message += `Branch Name: ${branchName}\n`;
          message += `Order Status: ${orderStatus}\n`;
          message += `Ordered on: ${formattedDate}\n`;
          message += `Payment Method: ${paymentMethod}\n\n*Items:*\n`;

          items.forEach((order: any) => {
            message += `\n${order.name}\n`;
            message += `Quantity: ${order.quantity}\n`;
            message += `Price: ${formatPrice(order.price * order.quantity)}\n\n`;

            if (order.notes) {
              message += `*Notes:* ${order.notes}\n`;
            }

            if (order.variants && order.variants.length > 0) {
              order.variants.forEach((variant: any) => {
                message += `${variant.name}:\n - ${variant.option}${formatPriceAddition(variant.price)}\n`;
              });
            }

            if (order.combos && order.combos.length > 0) {
              order.combos.forEach((combo: any) => {
                if (combo.items.length > 0) {
                  message += `${combo.name}:\n`;
                  combo.items.forEach((item: any) => {
                    message += ` - ${item.quantity} x ${item.option}${formatPriceAddition(item.price * item.quantity)}\n`;
                  });
                }
              });
            }

            if (order.add_ons && order.add_ons.length > 0) {
              order.add_ons.forEach((addOn: any) => {
                if (addOn.items.length > 0) {
                  message += `${addOn.name}:\n`;
                  addOn.items.forEach((item: any) => {
                    message += ` - ${item.option}${formatPriceAddition(item.price)}\n`;
                  });
                }
              });
            }
          });

          if (additional_notes) {
            message += `\n*Additional Notes:* ${additional_notes}\n`;
          }
          message += `\n*Total Bill:* AED ${bill.total_bill}`;
          message += `\n\n*Click this button to track your order* 👇`;

          return message;
        };

        let whatsAppMessage = `${baseMessage}\n\n${formatOrderDetails()}`;
        let emailMessage = formatOrderDetails();

        await axios.post(
          `${process.env.WHATSAPP_URL}/commerce/send-whatsapp-message`,
          {
            shopId: shop_id,
            customerPhone,
            message: whatsAppMessage,
            trackingLink,
            isCTA: true,
          },
        );

        if (autoAccept) {
          await axios.post(
            `${process.env.WHATSAPP_URL}/commerce/send-whatsapp-message`,
            {
              shopId: shop_id,
              customerPhone,
              message: whatsAppAcceptedMessage,
            },
          );
        }

        if (generate_quotation) {
          await axios.post(
            `${process.env.WHATSAPP_URL}/commerce/send-quotation-message`,
            {
              shopId: shop_id?.toLowerCase(),
              orderId: order_id,
            },
          );
        }

        await this.commonService.sendMail({
          from: shopSenderEmail,
          to: shopEmails,
          password: shopEmailPassword,
          secret: process.env.EMAIL_SECRET,
          subject: `New Order: ${newOrderName}`,
          text: `Customer Name: ${customerName}\nPhone: ${customerPhone}\n${emailMessage} `,
        });
      } catch (error) {
        throw new Error(
          'Failed to send email, OneSignal notification and WhatsApp message',
        );
      }
    }

    try {
      const sqlQuery = `
       UPDATE public."UserDataCache"
       SET current_step='NEW_ORDER'
       where shop_phone_number=$1
       AND phone_number=$2
       `;
      const params = [lastOrderResult[0].shop_whatsapp_id, customer_id];
      await this.databaseService.query(sqlQuery, params);
    } catch (error) {
      throw new Error('Failed to update user data cache');
    }

    return { order_id: order[0].order_id };
  }

  private async processOnlinePayment(
    order: any,
    customer_id: string,
    shop_id: string,
    order_type: string,
  ): Promise<any> {
    const customerQuery = `
      SELECT customer_name, phone_number
      FROM public."Customers"
      WHERE customer_id = $1
    `;

    let customer: any[];
    try {
      customer = await this.databaseService.query(customerQuery, [customer_id]);
    } catch (error) {
      throw new Error('Failed to fetch customer details');
    }

    const { stripeKey, whatsAppId, tapkey, gatewayName } =
      await this.findStripeAPIKey(shop_id, order?.fk_branch_id);
    const { order_id, order_name, bill, fk_shop_id } = order;
    const { phone_number, customer_name } = customer[0];

    const metadata = {
      orderId: order_id,
      shopId: shop_id,
      shopWhatsAppId: whatsAppId,
      customerPhone: phone_number,
      customerName: customer_name,
      branchId: order?.fk_branch_id,
    };
    let paymentResponse;
    if (gatewayName === 'stripe') {
      paymentResponse = await this.processStripePayment(
        `Order ${order_name}`,
        bill.total_bill,
        metadata,
        stripeKey,
        order_id,
        fk_shop_id,
        order_type,
      );

      await this.addOrderAndPaymentLink(
        phone_number,
        'stripe',
        order_id,
        paymentResponse,
      );
    } else if (gatewayName === 'tap') {
      paymentResponse = await this.processTapPayment(
        `Order ${order_name}`,
        bill.total_bill,
        metadata,
        tapkey,
        order_id,
        fk_shop_id,
      );
    }

    return { payment_link: paymentResponse[0] };
  }

  async findStripeAPIKey(
    shop_id: string,
    branchId: string,
  ): Promise<{
    stripeKey: string | null;
    whatsAppId: string;
    tapkey: string;
    gatewayName: string;
  }> {
    const paymentMethodQuery = `
      SELECT COALESCE(b.payment_methods,s.payment_methods) payment_methods, shop_whatsapp_id
      FROM public."Shops" s
      JOIN public."ShopBranches" b on b.fk_shop_id = s.shop_id
      WHERE shop_id = $1 AND branch_id = $2
    `;
    let gatewayName = null;
    let paymentResult: any[];
    try {
      paymentResult = await this.databaseService.query(paymentMethodQuery, [
        shop_id,
        branchId,
      ]);
    } catch (error) {
      throw new Error('Failed to fetch payment methods');
    }

    const paymentMethods = paymentResult[0].payment_methods;
    const whatsAppId = paymentResult[0].shop_whatsapp_id;
    let stripeKey = null;
    let tapkey = null;
    for (const method of paymentMethods) {
      gatewayName = method.payment_method;
      if (method.payment_method === 'stripe') {
        stripeKey = method.payment_api_key;
        break;
      } else if (method.payment_method === 'tap') {
        tapkey = method.payment_api_key;
        break;
      }
    }

    return { stripeKey, tapkey, whatsAppId, gatewayName };
  }

  async processTapPayment(
    name: string,
    total_bill: number,
    metadata: Record<string, string> = {},
    tap_api_key: string,
    order_id: string,
    shop_id: string,
  ): Promise<string[]> {
    try {
      if (!metadata.customerPhone || !metadata.customerName) {
        throw new Error('Customer phone and name are required for Tap payment');
      }

      const nameParts = metadata.customerName.split(' ');
      const firstName = nameParts[0];
      const middleName =
        nameParts.length > 2 ? nameParts.slice(1, -1).join(' ') : '';
      const lastName =
        nameParts.length > 1 ? nameParts[nameParts.length - 1] : '';

      const tapUrl = 'https://api.tap.company/v2/charges';
      const redirectUrl = `${process.env.FRONTEND_URL}/${shop_id}/tracking/${order_id}`;
      const webhookUrl = `${process.env.WHATSAPP_URL}/tap-pay-webhook/commerce/${shop_id}`;

      const tapPayload = {
        amount: parseFloat(total_bill.toString()).toFixed(2),
        currency: 'AED',
        customer_initiated: true,
        threeDSecure: true,
        save_card: false,
        description: `${name} - ${shop_id}`,
        metadata: {
          orderId: order_id,
          shopId: shop_id,
          shopWhatsAppId: metadata.shopWhatsAppId,
          customerPhone: metadata.customerPhone,
          customerName: metadata.customerName,
          branchId: metadata.branchId,
          orderName: name,
          totalAmount: parseFloat(total_bill.toString()).toFixed(2),
        },
        receipt: {
          email: false,
          sms: false,
        },
        reference: {
          transaction: `trx_${Date.now()}`,
          order: order_id,
        },
        customer: {
          first_name: firstName,
          middle_name: middleName || undefined,
          last_name: lastName,
          email: '<EMAIL>',
        },
        merchant: {
          id: shop_id,
        },
        source: {
          id: 'src_all',
        },
        post: {
          url: webhookUrl,
        },
        redirect: {
          url: redirectUrl,
        },
      };

      const tapResponse = await axios.post(tapUrl, tapPayload, {
        headers: {
          Authorization: `Bearer ${tap_api_key}`,
          'Content-Type': 'application/json',
        },
        timeout: 10000,
      });

      if (!tapResponse.data?.transaction?.url || !tapResponse.data?.id) {
        throw new Error('Invalid response from Tap payment gateway');
      }

      return [
        tapResponse.data.transaction.url,
        'tap_product',
        'tap_price',
        tapResponse.data.id,
      ];
    } catch (error) {
      console.error(
        'Tap payment error:',
        error.response?.data || error.message,
      );
      if (axios.isAxiosError(error)) {
        throw new Error(
          `Tap payment failed: ${error.response?.data?.message || error.message}`,
        );
      }
      throw new Error('Error processing Tap payment');
    }
  }

  async stripeStepProcess(
    shop_id: string,
    order_id: string,
    reject: boolean,
    fk_branch_id: string,
  ) {
    const stripe_api_key = (await this.findStripeAPIKey(shop_id, fk_branch_id))
      .stripeKey;
    const stripe = new Stripe(stripe_api_key, {
      apiVersion: '2025-06-30.basil',
    });

    const sql = `
        SELECT stripe_payment_intent AS payment_intent_id,
        stripe_payment_link_id AS payment_link_id
        FROM public."ShopOrders"
        WHERE order_id = $1
    `;

    let results: any[];
    try {
      results = await this.databaseService.query(sql, [order_id]);
    } catch (error) {
      throw new Error('Failed to fetch stripe order details');
    }

    const { payment_intent_id, payment_link_id } = results[0];

    try {
      const paymentIntent =
        await stripe.paymentIntents.retrieve(payment_intent_id);

      if (!reject) {
        if (paymentIntent.status !== 'succeeded') {
          await stripe.paymentIntents.capture(payment_intent_id);
        } else {
          return 'Payment intent has already been captured.';
        }
      } else {
        if (paymentIntent.status !== 'canceled') {
          await stripe.paymentIntents.cancel(payment_intent_id);
        } else {
          return 'Payment intent has already been canceled.';
        }
      }

      await stripe.paymentLinks.update(payment_link_id, { active: false });
    } catch (error) {
      throw new Error('Stripe payment intent error');
    }
  }

  async customerShopBranchLinking(
    customerId: string,
    shopId: string,
    branchId: string,
  ): Promise<void> {
    const sql = `
      INSERT INTO public."Customer_Shops" (
        fk_customer_id,
        fk_shop_id,
        branch_ids
      ) VALUES ($1, $2, ARRAY[$3])
      ON CONFLICT (fk_customer_id, fk_shop_id)
      DO UPDATE SET
        branch_ids = CASE
          WHEN NOT EXISTS (
            SELECT 1
            FROM unnest("Customer_Shops".branch_ids) AS f
            WHERE f = $3
          ) THEN "Customer_Shops".branch_ids || ARRAY[$3]
          ELSE "Customer_Shops".branch_ids
        END
    `;

    const params = [customerId, shopId, branchId];

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Error adding or updating customer links');
    }
  }

  async findCategoryOrderByBranchId(
    branchId: string,
    hideInvalidCategory: boolean = false,
  ): Promise<any[]> {
    let query = `
      SELECT
        c.category_id,
        c.category_name
      FROM public."ShopCategories" c
      JOIN public."ShopBranches" b ON c.fk_branch_id = b.branch_id
      WHERE b.branch_id = $1`;

    if (hideInvalidCategory) {
      query += `
        AND c.status != false
        AND EXISTS (
          SELECT 1 
          FROM public."ShopItems" i 
          WHERE i.fk_category_id = c.category_id 
            AND NOT (i.item_status = 'out-of-stock' AND i.out_of_stock_until IS NULL)
            AND i.fk_branch_id = $1
        )`;
    }

    query += ` ORDER BY array_position(b.branch_category_order::uuid[], c.category_id::uuid)`;

    try {
      const result = await this.databaseService.query(query, [branchId]);
      return result;
    } catch (error) {
      throw error;
    }
  }

  async updateCategoryStatus(
    categoryId: string,
    status: boolean,
  ): Promise<void> {
    const sql = `
      UPDATE public."ShopCategories"
      SET status = $1
      WHERE category_id = $2
    `;

    const params = [status, categoryId];

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to update category status');
    }
  }

  async updateCategoryOrderByBranchId(
    branchId: string,
    newCategoryOrder: string[],
  ): Promise<void> {
    const query = `
      UPDATE public."ShopBranches"
      SET branch_category_order = $2
      WHERE branch_id = $1
    `;
    try {
      await this.databaseService.query(query, [branchId, newCategoryOrder]);
    } catch (error) {
      throw error;
    }
  }

  async getBranchDetails(branchId: string): Promise<any[]> {
    const sql = `
      SELECT
        b.branch_logo AS "branch_logo_url", b.branch_display_name,
        b.branch_name, b.branch_address, b.branch_maps_url,
        b.branch_timings, b.branch_promotional_banner, b.branch_location,
        COALESCE(b.branch_payment_modes, '{}'::character varying[]) AS branch_payment_modes,
        b.trn_number, b.auto_refundable, b.cancellation_number AS branch_cancellation_number
      FROM
        public."ShopBranches" b
      WHERE
        b.branch_id = $1
    `;

    try {
      const result = await this.databaseService.query(sql, [branchId]);
      return result || [];
    } catch (error) {
      throw new Error('Failed to fetch branch details');
    }
  }

  async updateBranchDetails(
    branchId: string,
    branchData: UpdateBranchDetailsDto,
  ): Promise<void> {
    const {
      branch_logo,
      branch_logo_url,
      branch_name,
      branch_display_name,
      branch_address,
      branch_maps_url,
      branch_payment_modes,
      auto_refundable,
      trn_number,
      branch_cancellation_number,
    } = branchData;

    let new_branch_logo_url = branch_logo_url;
    const branch_logo_extension = branch_logo?.mimetype.split('/').pop();

    if (branch_logo && branch_logo_extension) {
      const oldLogoKey = branch_logo_url?.split(
        'https://cravin.s3.me-central-1.amazonaws.com/',
      )[1];

      if (oldLogoKey) {
        const deleteParams = {
          Bucket: 'cravin',
          Key: oldLogoKey,
        };

        const deleteCommand = new DeleteObjectCommand(deleteParams);
        try {
          await s3Client.send(deleteCommand);
        } catch (error) {
          throw new Error('Failed to delete old logo');
        }
      }

      const logoFileName = `${process.env.STORAGE_FOLDER}/branches/${branchId}/logo_${Date.now()}.${branch_logo_extension}`;

      const logoUploadParams = {
        Bucket: 'cravin',
        Key: logoFileName,
        Body: branch_logo.buffer,
        ContentType: branch_logo.mimetype,
        PutObjectAcl: 'public-read',
      };

      const logoCommand = new PutObjectCommand(logoUploadParams);
      try {
        await s3Client.send(logoCommand);
        new_branch_logo_url = `https://cravin.s3.me-central-1.amazonaws.com/${logoFileName}`;
      } catch (error) {
        throw new Error('Failed to upload branch logo');
      }
    }

    let processedTrnNumber: number | null = null;
    if (
      trn_number !== null ||
      trn_number !== undefined ||
      trn_number !== '' ||
      trn_number.trim() !== ''
    ) {
      const num = Number(trn_number);
      if (!isNaN(num)) {
        processedTrnNumber = num;
      }
    }

    const sql = `
      UPDATE public."ShopBranches"
      SET
        branch_logo = $1,
        branch_name = $2,
        branch_display_name = $3,
        branch_address = $4,
        branch_maps_url = $5,
        branch_payment_modes = $6,
        auto_refundable = $7,
        trn_number = $8,
        cancellation_number = $9
      WHERE branch_id = $10
    `;

    const params = [
      new_branch_logo_url,
      branch_name,
      branch_display_name,
      branch_address,
      branch_maps_url,
      JSON.parse(branch_payment_modes as unknown as string),
      auto_refundable === 'true',
      processedTrnNumber,
      branch_cancellation_number,
      branchId,
    ];

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to update branch details');
    }
  }

  async updatePromotionalBanners(
    branchId: string,
    bannerData: UpdatePromotionalBannersDto,
  ): Promise<void> {
    const {
      branch_promotional_banner_urls,
      branch_promotional_banners,
      banner_delete_index,
    } = bannerData;

    let currentUrls: string[] = [];

    if (typeof branch_promotional_banner_urls === 'string') {
      currentUrls = JSON.parse(branch_promotional_banner_urls);
    } else if (Array.isArray(branch_promotional_banner_urls)) {
      currentUrls = branch_promotional_banner_urls;
    }

    let deleteIndices: number[] = [];
    if (banner_delete_index && banner_delete_index.length > 0) {
      if (typeof banner_delete_index === 'string') {
        const parsedArray = JSON.parse(banner_delete_index);
        if (Array.isArray(parsedArray)) {
          deleteIndices = parsedArray.map((item) => +item);
        }
      } else if (Array.isArray(banner_delete_index)) {
        deleteIndices = banner_delete_index.map((item) => +item);
      }
    }

    if (
      deleteIndices.length > 0 ||
      (branch_promotional_banners && branch_promotional_banners.length > 0)
    ) {
      for (const index of deleteIndices) {
        const urlToDelete = currentUrls[index - 1];
        if (
          urlToDelete &&
          urlToDelete.startsWith(
            'https://cravin.s3.me-central-1.amazonaws.com/',
          )
        ) {
          try {
            const key = urlToDelete.split(
              'https://cravin.s3.me-central-1.amazonaws.com/',
            )[1];

            const deleteParams = {
              Bucket: 'cravin',
              Key: key,
            };

            const deleteCommand = new DeleteObjectCommand(deleteParams);
            await s3Client.send(deleteCommand);
          } catch (error) {
            throw new Error('Failed to delete files');
          }
        }
      }

      currentUrls = currentUrls.filter(
        (_, idx) => !deleteIndices.includes(idx + 1),
      );

      for (let i = 0; i < currentUrls.length; i++) {
        const oldUrl = currentUrls[i];
        if (
          oldUrl.startsWith('https://cravin.s3.me-central-1.amazonaws.com/')
        ) {
          const oldKey = oldUrl.split(
            'https://cravin.s3.me-central-1.amazonaws.com/',
          )[1];
          const extension = oldKey.split('.').pop();
          const timestamp = Date.now();
          const newKey = `${process.env.STORAGE_FOLDER}/branches/${branchId}/promotional_banners/banner_${i + 1}_${timestamp}.${extension}`;
          const newUrl = `https://cravin.s3.me-central-1.amazonaws.com/${newKey}`;

          if (oldKey !== newKey) {
            try {
              const copyParams = {
                Bucket: 'cravin',
                CopySource: `cravin/${oldKey}`,
                Key: newKey,
              };
              const copyCommand = new CopyObjectCommand(copyParams);
              await s3Client.send(copyCommand);

              const deleteParams = {
                Bucket: 'cravin',
                Key: oldKey,
              };
              const deleteCommand = new DeleteObjectCommand(deleteParams);
              await s3Client.send(deleteCommand);

              currentUrls[i] = newUrl;
            } catch (error) {
              throw new Error('Failed to rename files');
            }
          }
        }
      }

      if (
        Array.isArray(branch_promotional_banners) &&
        branch_promotional_banners.length > 0
      ) {
        let nextAvailableIndex = currentUrls.length + 1;
        for (const banner of branch_promotional_banners) {
          const banner_extension = banner.mimetype.split('/').pop();
          const timestamp = Date.now();
          const newFileName = `${process.env.STORAGE_FOLDER}/branches/${branchId}/promotional_banners/banner_${nextAvailableIndex}_${timestamp}.${banner_extension}`;
          const bannerUploadParams = {
            Bucket: 'cravin',
            Key: newFileName,
            Body: banner.buffer,
            ContentType: banner.mimetype,
          };
          try {
            const bannerCommand = new PutObjectCommand(bannerUploadParams);
            await s3Client.send(bannerCommand);
            const newUrl = `https://cravin.s3.me-central-1.amazonaws.com/${newFileName}`;
            currentUrls.push(newUrl);
          } catch (error) {
            throw new Error('Failed to upload promotional banner');
          }
          nextAvailableIndex++;
        }
      }
    }

    const sql = `
      UPDATE public."ShopBranches"
      SET
        branch_promotional_banner = $2
      WHERE branch_id = $1
    `;
    const params = [branchId, currentUrls];
    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to update promotional banners in database');
    }
  }

  async getBranch(shopId: string, branchId: string): Promise<any> {
    const sql = `
      SELECT
        s.shop_name, b.branch_logo, b.branch_display_name, b.branch_delivery_status,
        b.branch_name, b.branch_address, b.branch_timings, b.branch_pickup_status,
        COALESCE(b.branch_promotional_banner, ARRAY[]::varchar[]) AS promotional_banner,
        b.branch_timezone, b.break_timings, b.break_status_switch,
        CASE
          WHEN s.product_level_status = true
          AND s.take_orders = true
          AND b.branch_delivery_status = true
          AND b.status = true
          THEN true ELSE false
        END AS status,
        CASE
          WHEN s.product_level_status = true
          AND s.pickup_module = true
          AND b.branch_pickup_status = true
          AND b.pickup_status = true THEN true
          ELSE false
        END AS pickup_status,
        EXISTS (
          SELECT 1
          FROM public."ShopBranches" b2
          WHERE b2.fk_shop_id = s.shop_id
          AND b2.branch_delivery = true
        ) AS delivery_module,
        EXISTS (
          SELECT 1
          FROM public."ShopBranches" b2
          WHERE b2.fk_shop_id = s.shop_id
          AND b2.branch_pickup = true
        ) AS pickup_module,
        order_link
      FROM
        public."ShopBranches" b
      LEFT JOIN
        public."Shops" s ON b.fk_shop_id = s.shop_id
      WHERE
        b.fk_shop_id = $1
      AND
        b.branch_id = $2
    `;
    const params = [shopId, branchId];

    try {
      const result = await this.databaseService.query(sql, params);
      return result[0];
    } catch (error) {
      throw new Error('Failed to fetch shop');
    }
  }

  async getBranchesForShop(shopId: string): Promise<any[]> {
    const sql = `
      SELECT branch_id, branch_name
      FROM public."ShopBranches"
      WHERE fk_shop_id = $1
    `;
    const params = [shopId];

    try {
      const result = await this.databaseService.query(sql, params);
      return result;
    } catch (error) {
      throw new Error('Failed to fetch branches');
    }
  }

  async getMenuItemsForBranch(branchId: string): Promise<any[]> {
    const sql = `
      WITH ordered_categories AS (
        SELECT category_id::uuid, order_index
        FROM (
          SELECT
            unnest(branch_category_order) AS category_id,
            generate_series(1, array_length(branch_category_order, 1)) AS order_index
          FROM public."ShopBranches"
          WHERE branch_id = $1
        )
      ),
      items_with_order AS (
        SELECT
          i.*,
          CASE
            WHEN i.item_order IS NULL THEN
              row_number() OVER (PARTITION BY i.fk_category_id ORDER BY i.created_at) +
              COALESCE((SELECT MAX(item_order) FROM public."Items" WHERE fk_category_id = i.fk_category_id AND item_order IS NOT NULL), 0)
            ELSE i.item_order
          END as calculated_order
        FROM public."ShopItems" i
        WHERE i.fk_branch_id = $1
      )
      SELECT
        c.category_id::text AS category_id,
        c.category_name,
        c.status,
        oc.order_index,
        COALESCE(
          json_agg(
            json_build_object(
              'item_id', i.item_id,
              'item_name', i.item_name,
              'item_type', i.item_type,
              'item_price', i.item_price,
              'item_image_links', i.item_image_links,
              'item_status', i.item_status,
              'item_quantity', i.item_quantity,
              'add_ons_count',
                COALESCE(jsonb_array_length(i.item_add_ons_group), 0),
              'item_order', i.calculated_order,
              'out_of_stock_until', to_char(i.out_of_stock_until AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS"Z"')
            ) ORDER BY i.calculated_order
          ) FILTER (WHERE i.item_id IS NOT NULL),
          '[]'
        ) AS items
      FROM
        ordered_categories oc
      JOIN
        public."ShopCategories" c ON c.category_id = oc.category_id
      LEFT JOIN
        items_with_order i ON c.category_id = i.fk_category_id
      GROUP BY
        c.category_id, c.category_name, c.status, oc.order_index
      ORDER BY
        order_index
    `;

    const params = [branchId];

    try {
      const result = await this.databaseService.query(sql, params);
      return result || [];
    } catch (error) {
      throw new Error('Failed to fetch menu items');
    }
  }

  async getCustomerMenuItemsForBranch(
    branchId: string,
    customerId: string,
    shopId: string,
    date: string,
  ): Promise<any[]> {
    const parts = date.split('-');
    const formattedDate = `${parts[2]}-${parts[1]}-${parts[0]}`;
    const sql = `
      WITH ordered_categories AS (
        SELECT category_id::uuid, order_index
        FROM (
          SELECT
            unnest(branch_category_order) AS category_id,
            generate_series(1, array_length(branch_category_order, 1)) AS order_index
          FROM public."ShopBranches"
          WHERE branch_id = $1
        ) AS ordered
      ),
      customer_check AS (
        SELECT
          CASE
            WHEN $2::uuid IS NULL THEN 'new'
            WHEN EXISTS (
              SELECT 1
              FROM public."Customer_Shops" cr
              WHERE cr.fk_customer_id = $2
                AND cr.fk_shop_id = $3
                AND $1 = ANY(cr.branch_ids)
            )
            THEN 'existing'
            ELSE 'new'
          END AS customer_type
      ),
      discounts AS (
        SELECT
          *,
          TO_DATE((discount_date->>'start_date'), 'DD-MM-YYYY') AS start_date,
          TO_DATE((discount_date->>'end_date'), 'DD-MM-YYYY') AS end_date
        FROM public."ShopDiscounts"
        WHERE status = true
          AND (
            applicable_customer = 'all'
            OR applicable_customer = (SELECT customer_type FROM customer_check)
          )
          AND (
            TO_DATE((discount_date->>'start_date'), 'DD-MM-YYYY') IS NULL
            OR TO_DATE((discount_date->>'start_date'), 'DD-MM-YYYY') <= $4::date
          )
          AND (
            TO_DATE((discount_date->>'end_date'), 'DD-MM-YYYY') IS NULL
            OR TO_DATE((discount_date->>'end_date'), 'DD-MM-YYYY') >= $4::date
          )
      ),
      items_with_order AS (
        SELECT
          i.*,
          CASE
            WHEN i.item_order IS NULL THEN
              row_number() OVER (PARTITION BY i.fk_category_id ORDER BY i.created_at) +
              COALESCE((SELECT MAX(item_order) FROM public."ShopItems" WHERE fk_category_id = i.fk_category_id AND item_order IS NOT NULL), 0)
            ELSE i.item_order
          END as calculated_order
        FROM public."ShopItems" i
        WHERE i.fk_branch_id = $1
        AND NOT (i.item_status = 'out-of-stock' AND i.out_of_stock_until IS NULL)
      ),
      item_discounts AS (
        SELECT
          i.item_id,
          COALESCE(
            json_agg(
              json_build_object(
                'type', d.discount_type,
                'value', d.discount_value,
                'min_amount', d.min_amount,
                'max_amount', d.max_amount,
                'start_time', (d.discount_duration_day->>'start_time'),
                'end_time', (d.discount_duration_day->>'end_time'),
                'uses_per_customer', d.uses_per_customer
              ) ORDER BY d.discount_type DESC
            ) FILTER (WHERE d.discount_id IS NOT NULL), '[]'::json
          ) AS discounts
        FROM
          public."ShopItems" i
        LEFT JOIN
          discounts d ON d.discount_id IS NOT NULL
            AND (
              i.fk_category_id = ANY(d.applicable_category::uuid[])
              OR i.item_id = ANY(d.applicable_items::uuid[])
            )
        WHERE
          i.fk_branch_id = $1
        GROUP BY
          i.item_id
      )
      SELECT
        c.category_id::text AS category_id,
        c.category_name,
        COALESCE(c.category_availability, '') AS category_availability,
        COALESCE(c.category_availability_timings, '[]'::jsonb) AS category_availability_timings,
        oc.order_index,
        json_agg(
          json_build_object(
            'item_id', i.item_id,
            'item_name', i.item_name,
            'item_description', i.item_description,
            'item_type', i.item_type,
            'item_price', i.item_price,
            'item_image_links', i.item_image_links,
            'item_status', i.item_status,
            'item_quantity', i.item_quantity,
            'out_of_stock_until', to_char(i.out_of_stock_until AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS"Z"'),
            'discounts', id.discounts
          ) ORDER BY
            CASE WHEN i.item_status = 'in-stock' THEN 0 ELSE 1 END,
            CASE
              WHEN i.item_status = 'in-stock' THEN i.calculated_order
              ELSE i.calculated_order
            END
        ) FILTER (WHERE i.item_id IS NOT NULL) AS items
      FROM
        ordered_categories oc
      JOIN
        public."ShopCategories" c ON c.category_id = oc.category_id
      LEFT JOIN
        items_with_order i ON i.fk_category_id = c.category_id
      LEFT JOIN
        item_discounts id ON id.item_id = i.item_id
      WHERE
        c.status != false
        AND c.fk_branch_id = $1
        AND EXISTS (
          SELECT 1 
          FROM public."ShopItems" check_items 
          WHERE check_items.fk_category_id = c.category_id 
            AND NOT (check_items.item_status = 'out-of-stock' AND check_items.out_of_stock_until IS NULL)
            AND check_items.fk_branch_id = $1
        )
      GROUP BY
        oc.order_index, c.category_id, c.category_name,
        c.category_availability, c.category_availability_timings
      ORDER BY
        oc.order_index;
    `;

    const params = [branchId, customerId, shopId, formattedDate];

    try {
      const result = await this.databaseService.query(sql, params);
      return result || [];
    } catch (error) {
      throw new Error('Failed to fetch menu items');
    }
  }

  async getCustomerBranchMenuData(
    shopId: string,
    branchId: string,
    customerId: string = null,
    date: string,
  ): Promise<any> {
    const branch = await this.getBranch(shopId, branchId);
    if (!branch) {
      throw new Error('Branch not found');
    }

    if (customerId && customerId.trim() === '') {
      customerId = null;
    }

    const items = await this.getCustomerMenuItemsForBranch(
      branchId,
      customerId,
      shopId,
      date,
    );
    return {
      shop_logo: branch.branch_logo,
      shop_name: branch.shop_name,
      promotional_banner: branch.promotional_banner,
      branch_name: branch.branch_name,
      branch_display_name: branch.branch_display_name,
      branch_address: branch.branch_address,
      branch_timings: branch.branch_timings,
      break_flag: branch.break_status_switch,
      break_timings: branch.break_timings,
      branch_timezone: branch.branch_timezone,
      order_link: branch.order_link,
      take_orders_delivery: branch.status,
      take_orders_pickup: branch.pickup_status,
      shop_delivery: branch.delivery_module,
      shop_pickup: branch.pickup_module,
      menu: items,
    };
  }

  async getBranchPaymentModes(branchId: string): Promise<any> {
    try {
      const sql = `
      SELECT
        branch_payment_modes AS "paymentModes"
      FROM
        public."ShopBranches"
      WHERE
        branch_id = $1
      `;
      const params = [branchId];
      const result = await this.databaseService.query(sql, params);
      return result[0];
    } catch (error) {
      throw new Error('Failed to fetch payment modes for the branch');
    }
  }

  async getItemDetails(branchId: string, itemId: string): Promise<any> {
    try {
      const sql = `
      SELECT
        fk_branch_id AS branch_id,
        item_name,
        item_description,
        item_type,
        fk_category_id AS category_id,
        item_price,
        item_image_links,
        item_status,
        item_quantity,
        item_variants AS variants,
        item_combos AS combos,
        CASE
          WHEN COALESCE(jsonb_array_length(item_add_ons_group), 0) = 0 THEN NULL
          ELSE JSON_AGG(
            JSON_BUILD_OBJECT(
              'id', item_add_ons_group.id,
              'selection', item_add_ons_group.selection,
              'group_name', item_add_ons_group.group_name,
              'add_ons_items', (
                SELECT JSON_AGG(
                  JSON_BUILD_OBJECT(
                    'add_on_id', a.add_on_id,
                    'add_on_name', a.add_on_name,
                    'add_on_type', a.add_on_type,
                    'add_on_price', a.add_on_price
                  )
                )
                FROM public."ShopAddOns" a
                WHERE a.add_on_id IN (
                  SELECT UNNEST(item_add_ons_group.add_ons_items)
                )
              )
            )
          )
        END AS add_ons
      FROM
        public."ShopItems"
      LEFT JOIN LATERAL (
        SELECT id, selection, group_name, add_ons_items
        FROM jsonb_to_recordset(item_add_ons_group)
        AS item_add_ons_group(id uuid, selection jsonb, group_name text, add_ons_items uuid[])
      ) AS item_add_ons_group ON true
      WHERE
        item_id = $1
      AND
        fk_branch_id = $2
      GROUP BY
        item_id, item_variants, item_add_ons_group, item_combos
      `;
      const params = [itemId, branchId];
      const result = await this.databaseService.query(sql, params);
      return result[0];
    } catch (error) {
      throw new Error('Failed to fetch item details');
    }
  }

  async getAllItems(branchId: string): Promise<any> {
    const sql = `
      SELECT
        item_id, item_name, item_type, item_price, item_image_links, item_quantity
      FROM
        public."ShopItems"
      WHERE
        fk_branch_id = $1
    `;
    const params = [branchId];

    try {
      const result = await this.databaseService.query(sql, params);
      return result;
    } catch (error) {
      throw new Error('Failed to fetch items');
    }
  }

  async getAllAddOns(branchId: string): Promise<any> {
    const sql = `
      SELECT
        add_on_id, add_on_name, add_on_type, add_on_price
      FROM
        public."ShopAddOns"
      WHERE
        fk_branch_id = $1
    `;
    const params = [branchId];

    try {
      const result = await this.databaseService.query(sql, params);
      return result;
    } catch (error) {
      throw new Error('Failed to fetch addons');
    }
  }

  async findItemsByAddOnId(branchId: string, addOnId: string): Promise<any[]> {
    const selectItemsSql = `
      SELECT item_id, item_add_ons_group
      FROM public."ShopItems"
      WHERE fk_branch_id = $1
        AND EXISTS (
          SELECT 1
          FROM jsonb_array_elements(item_add_ons_group) AS group_elem,
               jsonb_array_elements_text(group_elem->'add_ons_items') AS add_on_elem
          WHERE add_on_elem = $2
        )
    `;

    const items = await this.databaseService.query(selectItemsSql, [
      branchId,
      addOnId,
    ]);

    return items;
  }

  async editAddOn(
    branchId: string,
    addOnId: string,
    updatedData: {
      add_on_name: string;
      add_on_type: string;
      add_on_price: number;
    },
  ): Promise<void> {
    const { add_on_name, add_on_type, add_on_price } = updatedData;
    const updateAddOnSql = `
      UPDATE public."ShopAddOns"
      SET add_on_name = $1, add_on_type = $2, add_on_price = $3, modified_at = CURRENT_TIMESTAMP
      WHERE fk_branch_id = $4 AND add_on_id = $5
    `;
    const params = [add_on_name, add_on_type, add_on_price, branchId, addOnId];

    try {
      await this.databaseService.query(updateAddOnSql, params);
    } catch (error) {
      throw new Error('Failed to update add-on');
    }
  }

  async deleteAddOn(
    branchId: string,
    addOnId: string,
  ): Promise<{
    items: Array<{ item_id: string; name: string }>;
    isDeleted: boolean;
  }> {
    const selectItemsSql = `
      SELECT item_id, item_name
      FROM public."ShopItems"
      WHERE fk_branch_id = $1
        AND EXISTS (
          SELECT 1
          FROM jsonb_array_elements(item_add_ons_group) AS group_elem,
               jsonb_array_elements_text(group_elem->'add_ons_items') AS add_on_elem
          WHERE add_on_elem = $2
        )
    `;

    const items = await this.databaseService.query(selectItemsSql, [
      branchId,
      addOnId,
    ]);

    const isDeleted = items.length === 0;

    if (isDeleted) {
      const deleteAddOnSql = `
        DELETE FROM public."ShopAddOns"
        WHERE fk_branch_id = $1 AND add_on_id = $2
      `;
      await this.databaseService.query(deleteAddOnSql, [branchId, addOnId]);
    }

    const response = {
      items: items.map((item: { item_id: any; item_name: any }) => ({
        item_id: item.item_id,
        item_name: item.item_name,
      })),
      isDeleted,
    };

    return response;
  }

  async updateItemAvailability(
    itemId: string,
    availability: string,
  ): Promise<void> {
    const sql = `
      UPDATE public."ShopItems"
      SET item_status = $1
      WHERE item_id = $2
    `;

    const params = [availability, itemId];

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to update item availability');
    }
  }

  async getActiveOrders(
    shop_id: string,
    branch_id: string,
    orderType: string,
  ): Promise<any[]> {
    const sql = `
    WITH aggregated_items AS (
      SELECT
        o.order_id,
        jsonb_agg(
          jsonb_build_object(
            'id', item.value->>'id',
            'name', item.value->>'name',
            'type', item.value->>'type',
            'notes', item.value->>'notes',
            'price', (item.value->>'price')::real,
            'add_ons', item.value->'add_ons',
            'quantity', (item.value->>'quantity')::int,
            'variants', item.value->'variants',
            'combos', item.value->'combos',
            'image_link', COALESCE(
              i.item_image_link,
              di.item_image_link
            )
          )
        ) AS cart_items
      FROM
        public."ShopOrders" o
      LEFT JOIN LATERAL (
        SELECT
          item.value AS value
        FROM
          jsonb_array_elements(o.items) AS item(value)
      ) item ON true
      LEFT JOIN LATERAL (
        SELECT
          i.item_image_links[1] AS item_image_link
        FROM
          public."ShopItems" i
        WHERE
          i.item_id = (item.value->>'id')::uuid
      ) i ON true
      LEFT JOIN LATERAL (
        SELECT
          di.item_image_link
        FROM
          public."DeletedImagesLogs" di
        WHERE
          di.item_id = (item.value->>'id')::uuid
      ) di ON true
      GROUP BY
        o.order_id
      ),
      preferred_customer AS (
        SELECT
          c.customer_id,
          COALESCE(cr.customer_name, c.customer_name) AS customer_name,
          c.phone_number,
          ROW_NUMBER() OVER (PARTITION BY c.customer_id ORDER BY cr.customer_name IS NULL) AS rn
        FROM
          public."Customers" c
        LEFT JOIN
          public."Customer_Shops" cr ON cr.fk_customer_id = c.customer_id
        AND
          (cr.fk_shop_id = $1 AND $2 = ANY(cr.branch_ids))
      )
      SELECT
        o.order_id,
        o.order_name,
        o.created_at AS ordered_on,
        pc.customer_name,
        pc.phone_number AS customer_number,
        CASE
          WHEN jsonb_typeof(o.customer_address) = 'object' THEN
            (
              SELECT CONCAT_WS(', ',
                o.customer_address->>'sector',
                o.customer_address->>'building',
                o.customer_address->>'landmark',
                o.customer_address->>'google_address'
              )
            )
          ELSE NULL
        END AS address,
        o.customer_address->>'recipient_name' AS receiver_name,
        o.customer_address->>'recipient_contact' AS receiver_number,
        o.customer_address->>'latitude' AS latitude,
        o.customer_address->>'longitude' AS longitude,
        ai.cart_items,
        o.status,
        o.payment_method,
        o.additional_notes,
        o.bill AS bill_amount,
        o.promo_code,
        o.rejected_reason AS reason,
        o.modified_at,
        o.order_type
      FROM
        public."ShopOrders" o
      JOIN
        public."Customers" c ON o.fk_customer_id = c.customer_id
      JOIN
        preferred_customer pc ON c.customer_id = pc.customer_id AND pc.rn = 1
      LEFT JOIN
        aggregated_items ai ON o.order_id = ai.order_id
      JOIN
        public."ShopBranches" b ON o.fk_branch_id = b.branch_id
      WHERE
      o.fk_shop_id = $1
        AND o.fk_branch_id = $2
        AND (
          (o.status = 'initiated' AND $3 = 'new')
          OR (o.status = 'accepted' AND $3 = 'accepted')
          OR (o.status = 'ready_for_pickup' AND $3 = 'accepted')
        )
      ORDER BY
        GREATEST(o.modified_at, o.created_at) DESC
    `;

    const params = [shop_id, branch_id, orderType];

    try {
      const result = await this.databaseService.query(sql, params);
      return result;
    } catch (error) {
      throw new Error('Failed to retrieve orders');
    }
  }

  async processStripePayment(
    name: string,
    total_bill: number,
    metadata: Record<string, string> = {},
    stripe_api_key: string,
    order_id: string,
    shop_id: string,
    order_type: string,
  ): Promise<string[]> {
    const stripe = new Stripe(stripe_api_key, {
      apiVersion: '2025-06-30.basil',
    });

    const product = await stripe.products.create({
      name,
      type: 'good',
    });

    const price = await stripe.prices.create({
      currency: 'aed',
      unit_amount: Math.round(total_bill * 100),
      product: product.id,
    });

    const paymentLink = await stripe.paymentLinks.create({
      currency: 'aed',
      line_items: [
        {
          price: price.id,
          quantity: 1,
        },
      ],
      payment_intent_data: {
        capture_method: 'manual',
      },
      after_completion: {
        redirect: {
          url: `${process.env.FRONTEND_URL}/${shop_id}/${order_type === 'pickup' ? 'pickup-' : ''}tracking/${order_id}`,
        },
        type: 'redirect',
      },
      metadata: metadata,
    });

    return [paymentLink.url, product.id, price.id, paymentLink.id];
  }

  async addOrderAndPaymentLink(
    phone_number: string,
    paymentMethod: string,
    orderId: string,
    paymentResponse: string[],
  ): Promise<void> {
    if (paymentMethod === 'stripe') {
      const [
        stripe_payment_link,
        stripe_product_id,
        stripe_price_id,
        stripe_payment_id,
      ] = paymentResponse;

      const stripeQuery = `
            UPDATE public."UserDataCache"
            SET fk_order_id = $1, stripe_payment_link = $3,
            stripe_product_id = $4, stripe_price_id = $5, modified_at = NOW()
            WHERE phone_number = $2;
        `;

      const stripeQueryParams = [
        orderId,
        phone_number,
        stripe_payment_link,
        stripe_product_id,
        stripe_price_id,
      ];

      await this.databaseService.query(stripeQuery, stripeQueryParams);

      const stripeIdQuery = `
            UPDATE public."ShopOrders"
            SET stripe_payment_link_id = $2
            WHERE order_id = $1;
        `;

      const stripeIdQueryParams = [orderId, stripe_payment_id];

      await this.databaseService.query(stripeIdQuery, stripeIdQueryParams);
    }
  }

  async getOrderStatus(orderId: string): Promise<string> {
    const sql = `
      SELECT status
      FROM public."ShopOrders"
      WHERE order_id = $1
    `;

    const params = [orderId];

    try {
      const result = await this.databaseService.query(sql, params);
      return result[0].status;
    } catch (error) {
      throw new Error('Failed to get order status');
    }
  }

  async getOrderDetails(orderId: string): Promise<any> {
    const sql = `
      SELECT
        s.shop_name,
        b.branch_name,
        b.branch_address,
        b.branch_maps_url,
        CONCAT_WS(', ',
          o.customer_address->>'sector',
          o.customer_address->>'building',
          o.customer_address->>'landmark',
          o.customer_address->>'google_address'
        ) AS customer_address,
        o.items,
        o.bill,
        o.payment_method,
        o.order_name AS order_id,
        o.rejected_reason,
        o.created_at,
        o.order_type,
        o.delivery_driver->>'name' AS delivery_driver_name
      FROM public."ShopOrders" o
      LEFT JOIN public."ShopBranches" b ON o.fk_branch_id = b.branch_id
      LEFT JOIN public."Shops" s ON b.fk_shop_id = s.shop_id
      WHERE o.order_id = $1
    `;

    const params = [orderId];

    try {
      const result = await this.databaseService.query(sql, params);
      return result[0];
    } catch (error) {
      throw new Error('Failed to get order details');
    }
  }

  async updateOrderStatus(
    orderId: string,
    body: {
      status: string;
      rejected_reason?: string;
      id?: string;
      name?: string;
      number?: string;
    },
  ): Promise<any> {
    try {
      const { status, rejected_reason, id, name, number } = body;
      let outOfStockFlag = false;

      const branchSql = `
        SELECT delivery_module
        FROM public."ShopBranches"
        WHERE branch_id = (
          SELECT fk_branch_id FROM public."ShopOrders" WHERE order_id = $1
        )
      `;
      const branchResult = await this.databaseService.query(branchSql, [
        orderId,
      ]);
      const deliveryModuleEnabled = branchResult[0]?.delivery_module || false;

      let sql = `
        UPDATE public."ShopOrders"
        SET
          status = $1::varchar,
          modified_at = NOW()`;

      const params: string[] = [status];

      if (status === 'rejected' && rejected_reason) {
        sql += `,
          rejected_reason = $3`;
        params.push(rejected_reason);
        const reasonLowerCase = rejected_reason.toLowerCase();
        if (reasonLowerCase.includes('out of stock')) {
          outOfStockFlag = true;
        }
      }

      if (status === 'out_for_delivery') {
        if (deliveryModuleEnabled) {
          if (id && name && number) {
            sql += `,
              delivery_driver = $${params.length + 1}::jsonb,
              delivery_start = CURRENT_TIMESTAMP`;
            const deliveryDriver = { id, name, number };
            params.push(JSON.stringify(deliveryDriver));

            const deliveryCountSql = `
              UPDATE public."ShopDeliveryDrivers"
              SET delivery_count = delivery_count + 1
              WHERE driver_id = $1
            `;
            await this.databaseService.query(deliveryCountSql, [id]);
          } else {
            throw new Error(
              'Driver information is required for out_for_delivery status when delivery_module is enabled',
            );
          }
        }
      }

      sql += `
        WHERE order_id = $${params.length + 1}
        RETURNING fk_shop_id, fk_branch_id, items, promo_code, fk_customer_id, fk_discount_id`;
      params.push(orderId);

      const result = await this.databaseService.query(sql, params);
      const shopId = result[0].fk_shop_id;
      const branchId = result[0].fk_branch_id;
      const promoCode = result[0].promo_code;
      const customerId = result[0].fk_customer_id;
      const discountId = result[0].fk_discount_id;

      if (status === 'rejected' && !outOfStockFlag) {
        const { fk_branch_id, items } = result[0];
        const orderItems = JSON.parse(items);

        const itemQuantitiesMap = orderItems.reduce(
          (
            acc: { [x: string]: any },
            item: { id: string | number; quantity: any },
          ) => {
            if (acc[item.id]) {
              acc[item.id] += item.quantity;
            } else {
              acc[item.id] = item.quantity;
            }
            return acc;
          },
          {} as { [key: string]: number },
        );

        const batchSize = 10;
        const itemEntries = Object.entries(itemQuantitiesMap);

        for (let i = 0; i < itemEntries.length; i += batchSize) {
          const batch = itemEntries.slice(i, i + batchSize);

          const itemIdsBatch = batch.map(([id]) => id);
          const quantitiesBatch = batch.map(([, quantity]) => quantity);

          const updateQuantitiesQuery = `
            UPDATE
              public."ShopItems"
            SET
              item_quantity = item_quantity + subquery.requested_quantity
            FROM (
              SELECT
                unnest($2::uuid[]) AS item_id,
                unnest($3::int[]) AS requested_quantity
            ) AS subquery
            WHERE
              public."ShopItems".item_id = subquery.item_id
            AND
              public."ShopItems".fk_branch_id = $1;
          `;
          const updateQuantitiesParams = [
            fk_branch_id,
            itemIdsBatch,
            quantitiesBatch,
          ];

          await this.databaseService.query(
            updateQuantitiesQuery,
            updateQuantitiesParams,
          );
        }
      }

      const updateStatusQuery = `
        UPDATE public."ShopItems"
        SET item_status = 'out-of-stock'
        WHERE item_quantity <= 0
        AND item_status = 'in-stock';
      `;
      try {
        await this.databaseService.query(updateStatusQuery);
      } catch (error) {
        throw new Error('Failed to update item status');
      }

      const orderSql = `
        SELECT
          o.order_name,
          o.rejected_reason,
          o.payment_method,
          b.branch_name,
          c.phone_number,
          b.cancellation_number,
          s.shop_name
        FROM public."ShopOrders" o
        LEFT JOIN public."ShopBranches" b ON o.fk_branch_id = b.branch_id
        LEFT JOIN public."Customers" c ON o.fk_customer_id = c.customer_id
        LEFT JOIN public."Shops" s ON o.fk_shop_id = s.shop_id
        WHERE o.order_id = $1
      `;

      const orderParams = [orderId];

      const orderDetails = await this.databaseService.query(
        orderSql,
        orderParams,
      );

      const order = orderDetails[0];
      const {
        phone_number,
        order_name,
        shop_name,
        cancellation_number,
        payment_method,
      } = order;

      const baseMessage = `Your Order *${order_name}*`;
      const contactMessage = `lease call *+${cancellation_number}*.`;
      const thanksMessage = `\n\nThank you for ordering from *${shop_name}* with Cravin.`;
      const notifyMessage = `\n\nWe will notify you when your order is out for delivery.`;
      const rejectedMessage = ' was rejected by the shop.';
      const rejectedOnlineMessage = '\n\nYour refund has been initiated.';
      const confirmedMessage = ' has been confirmed ✅\n';
      const deliveryMessage = ' is out for delivery ✅';
      const pickupMessage = ' is ready for pickup ✅';
      const deliveryModuleMessage = ` has been picked up ${name ? 'by ' + name : ''} and`;
      const cancelMessage = `\nIf you wish to cancel or amend your order, p${contactMessage}.${notifyMessage}`;
      const apologyMessage = ` We apologize for the inconvenience caused.\n\nYour order was rejected because:\n\n${rejected_reason?.charAt(0).toUpperCase() + rejected_reason?.slice(1)}\n\nP${contactMessage} to know more.\n\n`;
      const endMessage =
        'This conversation has now ended, please send a *“Hi”* to initiate a new conversation.';

      if (status === 'accepted') {
        if (payment_method === 'online') {
          await this.stripeStepProcess(shopId, orderId, false, branchId);
        }
        await axios.post(
          `${process.env.WHATSAPP_URL}/commerce/send-whatsapp-message`,
          {
            shopId: shopId,
            customerPhone: phone_number,
            message: baseMessage + confirmedMessage + cancelMessage,
          },
        );
      } else if (status === 'rejected') {
        if (promoCode) {
          try {
            await this.processDiscountUsage(customerId, discountId, true);
          } catch (error) {
            throw new Error('Failed to update discount usage');
          }
        }

        if (payment_method === 'online') {
          await this.stripeStepProcess(shopId, orderId, true, branchId);
          await axios.post(
            `${process.env.WHATSAPP_URL}/commerce/send-whatsapp-message`,
            {
              shopId: shopId,
              customerPhone: phone_number,
              message:
                baseMessage +
                rejectedMessage +
                rejectedOnlineMessage +
                apologyMessage +
                endMessage,
            },
          );
        } else {
          await axios.post(
            `${process.env.WHATSAPP_URL}/commerce/send-whatsapp-message`,
            {
              shopId: shopId,
              customerPhone: phone_number,
              message:
                baseMessage + rejectedMessage + apologyMessage + endMessage,
            },
          );
        }
      } else if (status === 'out_for_delivery') {
        await axios.post(
          `${process.env.WHATSAPP_URL}/commerce/send-whatsapp-message`,
          {
            shopId: shopId,
            customerPhone: phone_number,
            message:
              baseMessage +
              (deliveryModuleEnabled ? deliveryModuleMessage : '') +
              deliveryMessage +
              thanksMessage,
          },
        );
      } else if (status === 'ready_for_pickup') {
        await axios.post(
          `${process.env.WHATSAPP_URL}/commerce/send-whatsapp-message`,
          {
            shopId: shopId,
            customerPhone: phone_number,
            message: baseMessage + pickupMessage + thanksMessage,
          },
        );
      }
    } catch (error) {
      throw new Error('Failed to update order status');
    }
  }

  async getZonesByBranchId(branchId: string): Promise<any[]> {
    const sql = `
      SELECT
        d.*,
        b.branch_location
      FROM public."ShopDeliveryZones" d
      LEFT JOIN public."ShopBranches" b ON b.branch_id = d.fk_branch_id
      WHERE d.fk_branch_id = $1
    `;

    try {
      const result = await this.databaseService.query(sql, [branchId]);
      return result;
    } catch (error) {
      throw new Error('Failed to get delivery zones');
    }
  }

  async addDeliveryZone(zoneData: CreateDeliveryZoneDto): Promise<void> {
    const {
      branch_id,
      shop_id,
      coordinates,
      zone_name,
      delivery_fee,
      min_cart_amount,
    } = zoneData;

    const sql = `
      INSERT INTO public."ShopDeliveryZones" (
        fk_branch_id,
        fk_shop_id,
        coordinates,
        zone_name,
        delivery_fee,
        min_cart_amount
      ) VALUES ($1, $2, $3::jsonb, $4, $5, $6)
    `;

    const params = [
      branch_id,
      shop_id,
      JSON.stringify(coordinates),
      zone_name,
      delivery_fee,
      min_cart_amount,
    ];

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to add delivery zone');
    }
  }

  async updateDeliveryZone(
    zoneId: string,
    zoneData: UpdateDeliveryZoneDto,
  ): Promise<void> {
    const { coordinates, zone_name, delivery_fee, min_cart_amount } = zoneData;

    const sql = `
      UPDATE public."ShopDeliveryZones"
      SET
        coordinates = $1::jsonb,
        zone_name = $2,
        delivery_fee = $3,
        min_cart_amount = $4
      WHERE zone_id = $5
    `;

    const params = [
      JSON.stringify(coordinates),
      zone_name,
      delivery_fee,
      min_cart_amount,
      zoneId,
    ];

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to update delivery zone');
    }
  }

  async updateZoneStatus(zoneId: string, status: boolean): Promise<void> {
    const sql = `
      UPDATE public."ShopDeliveryZones"
      SET status = $1
      WHERE zone_id = $2
    `;

    const params = [status, zoneId];

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to update delivery zone status');
    }
  }

  async deleteZoneById(zoneId: string): Promise<void> {
    const sql = `
      DELETE FROM public."ShopDeliveryZones"
      WHERE zone_id = $1
    `;

    const params = [zoneId];

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to delete delivery zone');
    }
  }

  async addItemsWithCategories(
    itemsData: any[],
    branchId: string,
  ): Promise<void> {
    const batchSize = 10;

    const uniqueCategories = [
      ...new Set(itemsData.map((item) => item.category)),
    ];
    const categoryIdsMap = await this.getOrCreateCategories(
      branchId,
      uniqueCategories,
    );

    for (let i = 0; i < itemsData.length; i += batchSize) {
      const batch = itemsData.slice(i, i + batchSize);

      const filteredBatch = batch.filter((item) => {
        const price = parseFloat(item.item_price);
        return !isNaN(price) && price > 0;
      });

      if (filteredBatch.length === 0) {
        continue;
      }

      try {
        const params = filteredBatch.map((itemData) => {
          const quantityRaw = parseFloat(itemData.item_quantity);
          const quantity = isNaN(quantityRaw)
            ? 0
            : Math.floor(Math.abs(quantityRaw));
          const status = quantity > 0 ? 'in-stock' : 'out-of-stock';

          return [
            branchId,
            itemData.item_name,
            itemData.item_description,
            'none',
            categoryIdsMap.get(itemData.category),
            parseFloat(itemData.item_price),
            itemData.item_image_link ? [itemData.item_image_link] : [],
            quantity,
            status,
          ];
        });

        const sql = `
          INSERT INTO public."ShopItems" (
            fk_branch_id,
            item_name,
            item_description,
            item_type,
            fk_category_id,
            item_price,
            item_image_links,
            item_quantity,
            item_status
          ) VALUES ${params.map((_, index) => `($${index * 9 + 1}, $${index * 9 + 2}, $${index * 9 + 3}, $${index * 9 + 4}, $${index * 9 + 5}, $${index * 9 + 6}, $${index * 9 + 7}, $${index * 9 + 8}, $${index * 9 + 9})`).join(', ')}
        `;

        await this.databaseService.query(sql, params.flat());
      } catch (error) {
        throw new Error('Failed to add items in bulk');
      }
    }
  }

  async getOrCreateCategories(
    branchId: string,
    categoryNames: string[],
  ): Promise<Map<string, string>> {
    const existingCategories = await this.getCategoriesByNames(
      branchId,
      categoryNames,
    );
    const categoryIdsMap = new Map<string, string>();

    existingCategories.forEach((category) => {
      categoryIdsMap.set(category.category_name, category.category_id);
    });

    const newCategories = categoryNames.filter(
      (category) => !categoryIdsMap.has(category),
    );

    if (newCategories.length > 0) {
      const newCategoryData = newCategories.map((categoryName) => ({
        branch_id: branchId,
        category_name: categoryName,
        category_availability: 'all',
        category_description: null,
        category_availability_timings: null,
      }));

      const newCategoryIds = await this.bulkInsertCategories(newCategoryData);

      newCategoryIds.forEach((id, index) => {
        categoryIdsMap.set(newCategories[index], id);
      });

      await this.updateBranchCategoryOrder(branchId, newCategoryIds);
    }

    return categoryIdsMap;
  }

  async getCategoriesByNames(
    branchId: string,
    categoryNames: string[],
  ): Promise<any[]> {
    const sql = `
      SELECT category_id, category_name
      FROM public."ShopCategories"
      WHERE fk_branch_id = $1
      AND category_name = ANY($2)
    `;
    const params = [branchId, categoryNames];

    try {
      const result = await this.databaseService.query(sql, params);
      return result;
    } catch (error) {
      throw new Error('Failed to fetch categories');
    }
  }

  async bulkInsertCategories(
    categoriesData: CreateCategoryDto[],
  ): Promise<string[]> {
    const sql = `
      INSERT INTO public."ShopCategories" (
        fk_branch_id,
        category_name,
        category_availability,
        category_description,
        category_availability_timings
      ) VALUES ${categoriesData.map((_, index) => `($${index * 5 + 1}, $${index * 5 + 2}, $${index * 5 + 3}, $${index * 5 + 4}, $${index * 5 + 5})`).join(', ')}
      RETURNING category_id
    `;

    const params = categoriesData.flatMap((category) => [
      category.branch_id,
      category.category_name,
      category.category_availability,
      category.category_description,
      category.category_availability_timings,
    ]);

    try {
      const result = await this.databaseService.query(sql, params);
      return result.map((row) => row.category_id);
    } catch (error) {
      throw new Error('Failed to insert categories');
    }
  }

  async updateBranchCategoryOrder(
    branchId: string,
    newCategoryIds: string[],
  ): Promise<void> {
    try {
      const sql = `
        UPDATE public."ShopBranches"
        SET branch_category_order = array_cat(branch_category_order, $2)
        WHERE branch_id = $1
      `;
      const params = [branchId, newCategoryIds];
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to update branch category order');
    }
  }

  async removeFromBranchCategoryOrder(
    branchId: string,
    categoryIdsToRemove: string,
  ): Promise<void> {
    try {
      const sql = `
        UPDATE public."ShopBranches"
        SET branch_category_order = array_remove(branch_category_order, $2)
        WHERE branch_id = $1
      `;
      const params = [branchId, categoryIdsToRemove];
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to update branch category order');
    }
  }

  async deleteItem(itemId: string): Promise<void> {
    let itemImageLinks: string[] | null = null;
    let checkItemOrder: any = null;

    const orderItemsQuery = `
      SELECT 1
      FROM public."ShopOrders"
      WHERE EXISTS (
        SELECT 1
        FROM jsonb_array_elements(items) AS item
        WHERE item ->> 'id' = $1
      );
    `;
    const orderItemParams = [itemId];

    try {
      checkItemOrder = await this.databaseService.query(
        orderItemsQuery,
        orderItemParams,
      );
    } catch (error) {
      throw new Error('Failed to check item in orders');
    }

    const itemImageSql = `
      SELECT item_image_links
      FROM public."ShopItems"
      WHERE item_id = $1;
    `;
    const itemImageParams = [itemId];

    try {
      const imageResult = await this.databaseService.query(
        itemImageSql,
        itemImageParams,
      );
      if (imageResult && imageResult.length > 0) {
        itemImageLinks = imageResult[0].item_image_links;
      }
    } catch (error) {
      throw new Error('Failed to find item image link');
    }

    if (
      itemImageLinks &&
      itemImageLinks.length > 0 &&
      checkItemOrder.length > 0
    ) {
      const logDeleteQuery = `
      INSERT INTO public."DeletedImagesLogs" (item_id, item_image_link)
      VALUES ($1, $2);
    `;
      const logParams = [itemId, itemImageLinks[0]];
      try {
        await this.databaseService.query(logDeleteQuery, logParams);
        console.log(
          `Logged skipped image deletion for item_id: ${itemId} because it exists in an order.`,
        );
      } catch (logError) {
        console.error('Failed to log skipped image deletion:', logError);
      }
    }

    let count: number = 0;
    if (checkItemOrder.length > 0) {
      count = 1;
    }

    if (itemImageLinks && itemImageLinks.length > count) {
      for (count; count < itemImageLinks.length; count++) {
        const itemImageLink = itemImageLinks[count];
        if (
          itemImageLink &&
          itemImageLink.includes(
            'https://cravin.s3.me-central-1.amazonaws.com/',
          )
        ) {
          const oldFileKey = itemImageLink.split(
            'https://cravin.s3.me-central-1.amazonaws.com/',
          )[1];

          if (oldFileKey) {
            const deleteParams = {
              Bucket: 'cravin',
              Key: oldFileKey,
            };
            const deleteCommand = new DeleteObjectCommand(deleteParams);

            try {
              await s3Client.send(deleteCommand);
              console.log(`Successfully deleted image for item_id: ${itemId}`);
            } catch (error) {
              console.error(
                `Failed to delete image for item_id ${itemId}:`,
                error,
              );
            }
          }
        }
      }
    }

    const deleteItemSql = `
      DELETE FROM public."ShopItems"
      WHERE item_id = $1;
    `;
    const deleteParams = [itemId];

    try {
      await this.databaseService.query(deleteItemSql, deleteParams);
      console.log(`Item with item_id: ${itemId} deleted.`);
    } catch (error) {
      throw new Error('Failed to delete item');
    }
  }

  async getItemQuantities(branchId: string, itemIds: string[]): Promise<any[]> {
    if (!itemIds.length) {
      return [];
    }

    const sql = `
      SELECT
        item_id,
        item_quantity
      FROM
        public."ShopItems"
      WHERE
        fk_branch_id = $1
      AND
        item_id = ANY($2::uuid[])
    `;

    try {
      const result = await this.databaseService.query(sql, [branchId, itemIds]);
      return result;
    } catch (error) {
      throw new Error('Failed to fetch item quantities');
    }
  }

  async updateItemStatus(
    itemId: string,
    body: { status: string; timestamp?: number | null },
  ): Promise<void> {
    const { status, timestamp } = body;
    const params = [itemId, status];

    let sql = `
      UPDATE public."ShopItems"
      SET item_status = $2`;

    if (timestamp === null || timestamp === undefined) {
      sql += `, out_of_stock_until = NULL `;
    } else if (timestamp === 0 || timestamp === -1) {
      sql += `, out_of_stock_until = NULL `;
    } else if (timestamp) {
      sql += `, out_of_stock_until = to_timestamp($3) `;
      params.push(`${timestamp / 1000}`);
    }

    sql += `WHERE item_id = $1`;

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to update category status');
    }
  }

  async getBranchStatus(branchId: string, serviceType: string): Promise<any[]> {
    let sql = '';

    if (serviceType === 'delivery') {
      sql += `
        SELECT b.status AS take_orders,
        CASE
          WHEN b.branch_delivery = true THEN true
          ELSE false
        END AS module_enabled,
        b.branch_delivery_status,
        s.take_orders AS delivery_module
      `;
    }

    if (serviceType === 'pickup') {
      sql += `
        SELECT b.pickup_status AS take_orders,
        CASE
          WHEN b.branch_pickup = true THEN true
          ELSE false
        END AS module_enabled,
        b.branch_pickup_status,
        s.pickup_module
      `;
    }

    sql += `
      FROM public."ShopBranches" b
      LEFT JOIN public."Shops" s ON s.shop_id = b.fk_shop_id
      WHERE b.branch_id = $1
    `;

    const params = [branchId];

    try {
      const branch = await this.databaseService.query(sql, params);
      return branch[0];
    } catch (error) {
      throw new Error('Failed to get branch status');
    }
  }

  async updateBranchStatus(
    branchId: string,
    status: boolean,
    serviceType: string,
  ): Promise<void> {
    if (!status) {
      let sql = `
        UPDATE public."ShopBranches" `;

      if (serviceType === 'delivery') {
        sql += `SET status = false, `;
      }

      if (serviceType === 'pickup') {
        sql += `SET pickup_status = false, `;
      }

      if (!serviceType) {
        sql += `SET status = false, pickup_status = false, `;
      }

      sql += `
          break_status_switch = false,
          break_override_until = NULL
        WHERE
          branch_id = $1
      `;

      try {
        await this.databaseService.query(sql, [branchId]);
      } catch (error) {
        throw new Error('Failed to update branch status');
      }
      return;
    }

    const sql = `
      SELECT
        branch_timezone,
        break_timings
      FROM public."ShopBranches"
      WHERE branch_id = $1
    `;

    const branch = await this.databaseService.query(sql, [branchId]);
    if (!branch) throw new Error('Branch not found');
    const { branch_timezone, break_timings } = branch[0];

    const { time: currentTime } =
      await this.customerQueryService.getCurrentTimeForBranch(
        null,
        branch_timezone,
      );

    const currentDate = new Date(currentTime);
    const currentDay = currentDate.getDay();
    const breakTimings: BreakTimings[] = break_timings || [];

    const dayTiming = breakTimings.find(
      (timing: BreakTimings) => timing.day === currentDay && timing.status,
    );

    let isInBreak = false;
    let breakEndTime = null;

    if (dayTiming?.breaks?.[0]) {
      const breaks = dayTiming.breaks[0];

      for (const breakKey of ['break1', 'break2', 'break3'] as const) {
        const breakTiming = breaks[breakKey];
        if (!breakTiming?.start || !breakTiming?.end) continue;

        const [startHours, startMinutes] = await this.parseTime(
          breakTiming.start,
        );
        const [endHours, endMinutes] = await this.parseTime(breakTiming.end);

        const currentHours = currentDate.getHours();
        const currentMinutes = currentDate.getMinutes();

        const isAfterStart =
          currentHours > startHours ||
          (currentHours === startHours && currentMinutes >= startMinutes);

        const isBeforeEnd =
          currentHours < endHours ||
          (currentHours === endHours && currentMinutes <= endMinutes);

        if (isAfterStart && isBeforeEnd) {
          isInBreak = true;
          breakEndTime = new Date(
            currentDate.setHours(endHours, endMinutes, 0, 0),
          );
          break;
        }
      }
    }

    let updateSql = `UPDATE public."ShopBranches" `;

    if (serviceType === 'delivery') {
      updateSql += `SET status = $1, `;
    }

    if (serviceType === 'pickup') {
      updateSql += `SET pickup_status = $1, `;
    }

    if (!serviceType) {
      updateSql += `
        SET status = $1, pickup_status = $1,
        branch_delivery_status = $1,
        branch_pickup_status = $1, `;
    }

    updateSql += `
        break_status_switch = $1,
        break_override_until = $3
      WHERE branch_id = $2
    `;

    const overrideUntil = isInBreak
      ? new Date(breakEndTime.getTime() + 5 * 60 * 1000)
      : null;

    try {
      await this.databaseService.query(updateSql, [
        status,
        branchId,
        overrideUntil,
      ]);
    } catch (error) {
      throw new Error('Failed to update branch status');
    }
  }

  async getBranchTimings(branchId: string): Promise<any[]> {
    const sql = `
      SELECT branch_timings
      FROM public."ShopBranches"
      WHERE branch_id = $1`;
    try {
      const result = await this.databaseService.query(sql, [branchId]);
      return result[0]?.branch_timings;
    } catch (error) {
      throw new Error('Failed to get branch timings');
    }
  }

  async getBreakTimings(branchId: string): Promise<any[]> {
    const sql = `
      SELECT break_timings
      FROM public."ShopBranches"
      WHERE branch_id = $1`;
    try {
      const result = await this.databaseService.query(sql, [branchId]);
      return result[0]?.break_timings;
    } catch (error) {
      throw new Error('Failed to get break timings');
    }
  }

  async updateBranchTimings(
    branchId: string,
    branchTimings: any,
  ): Promise<void> {
    const query = `
      UPDATE "ShopBranches"
      SET branch_timings = $2
      WHERE branch_id = $1`;
    try {
      await this.databaseService.query(query, [
        branchId,
        JSON.stringify(branchTimings),
      ]);
    } catch (error) {
      throw new Error('Failed to update branch timings');
    }
  }

  async updateBreakTimings(branchId: string, breakTimings: any): Promise<void> {
    const sql = `
      UPDATE "ShopBranches"
      SET break_timings = $2
      WHERE branch_id = $1
      RETURNING status`;
    try {
      const branch = await this.databaseService.query(sql, [
        branchId,
        JSON.stringify(breakTimings),
      ]);
      await this.updateBranchStatus(branchId, branch[0]?.status, null);
    } catch (error) {
      throw new Error('Failed to update break timings');
    }
  }

  async getInboxStatus(shopId: string, branchId?: string | null): Promise<any> {
    const params: any[] = [shopId];
    let sql = `
      FROM public."Shops" s
		  JOIN public."ShopBranches" b on b.fk_shop_id = s.shop_id
      WHERE s.shop_id = $1
    `;

    if (branchId) {
      sql =
        `
        SELECT
          b.branch_id,
          s.shop_id,
          s.inbox_access_enabled,
          b.inbox_enabled AS branch_inbox_access,
          b.delivery_module AS branch_delivery_access
        ` +
        sql +
        `
          AND b.branch_id = $2
      `;
      params.push(branchId);
    } else {
      sql =
        `
        SELECT
          s.shop_id,
          s.inbox_access_enabled,
          EXISTS (
            SELECT 1 FROM public."ShopBranches" b2 
            WHERE b2.fk_shop_id = s.shop_id AND b2.delivery_module = true
          ) AS delivery_access
        ` +
        sql +
        `
        GROUP BY s.shop_id, s.inbox_access_enabled
      `;
    }

    try {
      const result = await this.databaseService.query(sql, params);
      return result;
    } catch (error) {
      throw new Error('Failed to get inbox and delivery status');
    }
  }

  async updateInboxStatus(
    shopId: string,
    branchId: string,
    status: boolean,
  ): Promise<any> {
    const sql = `
      UPDATE public."ShopBranches"
        set inbox_enabled =$3
       WHERE
        fk_shop_id = $1 AND
        branch_id = $2
    `;
    const params = [shopId, branchId, status];

    try {
      const result = await this.databaseService.query(sql, params);
      return result;
    } catch (error) {
      throw new Error('Failed to fetch items');
    }
  }

  async addDeliveryDrivers(driverData: any[], branchId: string): Promise<void> {
    const batchSize = 10;
    for (let i = 0; i < driverData.length; i += batchSize) {
      const batch = driverData.slice(i, i + batchSize);

      try {
        const params = batch.map((driver) => [
          driver.name,
          driver.number,
          branchId,
        ]);

        const sql = `
          INSERT INTO public."ShopDeliveryDrivers" (
            driver_name,
            driver_contact,
            fk_branch_id
          ) VALUES ${params.map((_, index) => `($${index * 3 + 1}, $${index * 3 + 2}, $${index * 3 + 3})`).join(', ')}
        `;

        await this.databaseService.query(sql, params.flat());
      } catch (error) {
        throw new Error('Failed to add drivers');
      }
    }
  }

  async getDeliveryDrivers(
    shopId: string,
    branchId: string | null = 'all',
    dateOption: string,
    timeZone: string | null = 'Asia/Dubai',
  ): Promise<any> {
    if (!shopId) {
      throw new Error('Shop ID must be provided');
    }

    const isAllBranches = !branchId || branchId === 'all' || branchId === null;

    if (isAllBranches) {
      return await this.getAllBranchesDrivers(shopId, dateOption, timeZone);
    } else {
      return await this.getSingleBranchDrivers(branchId, dateOption, timeZone);
    }
  }

  private async getSingleBranchDrivers(
    branchId: string,
    dateOption: string,
    timeZone: string | null,
  ): Promise<any> {
    const params: any[] = [branchId];
    const dateOptionFilter = await this.calculateWorkingHours(
      branchId,
      dateOption,
      timeZone,
    );

    const sql = `
      SELECT
        s.driver_id AS id,
        s.driver_name AS name,
        s.driver_contact AS number,
        COALESCE(order_stats.count, 0) AS count,
        COALESCE(order_stats.total_amount, 0) AS total_amount,
        payments.payment_method,
        b.branch_id,
        b.branch_name
      FROM public."ShopDeliveryDrivers" s
      JOIN public."ShopBranches" b ON s.fk_branch_id = b.branch_id
      LEFT JOIN (
        SELECT
          (o.delivery_driver ->> 'id')::uuid AS driver_id,
          COUNT(*)::int4 AS count,
          SUM((o.bill ->> 'total_bill')::decimal)::float8 AS total_amount
        FROM public."ShopOrders" o
        JOIN public."ShopBranches" ob ON o.fk_branch_id = ob.branch_id
        WHERE 1=1
        AND o.fk_branch_id = $1
        ${dateOptionFilter}
        AND o.status != 'cancelled'
        GROUP BY driver_id
      ) order_stats ON order_stats.driver_id = s.driver_id
      LEFT JOIN LATERAL (
        SELECT json_agg(
          json_build_object(
            'type', p.method,
            'count', COALESCE(ps.count, 0),
            'amount', COALESCE(ps.amount, 0)
          ) ORDER BY p.method
        ) AS payment_method
        FROM (
          SELECT 'cash' AS method
          UNION ALL
          SELECT 'card'
          UNION ALL
          SELECT 'online'
        ) p
        LEFT JOIN (
          SELECT
            o.payment_method,
            COUNT(*) AS count,
            SUM((o.bill ->> 'total_bill')::decimal) AS amount
          FROM public."ShopOrders" o
          WHERE (o.delivery_driver ->> 'id')::uuid = s.driver_id
          AND o.fk_branch_id = $1
          ${dateOptionFilter}
          AND o.status != 'cancelled'
          GROUP BY o.payment_method
        ) ps ON ps.payment_method = p.method
      ) payments ON true
      WHERE s.fk_branch_id = $1
      ORDER BY count DESC, name
    `;

    try {
      return await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to fetch drivers for single branch');
    }
  }

  async getOrderDeliveredByDeliveryDriver(
    branchId: string,
    driverId: string,
    dateOption: string,
    timeZone: string | null = 'Asia/Dubai',
  ): Promise<any> {
    let sql = `
      SELECT
        o.order_name,
        o.status,
        o.bill ->> 'total_bill' AS total_bill,
        o.payment_method,
        o.customer_address ->> 'recipient_name' AS recipient_name,
        o.customer_address ->> 'recipient_contact' AS recipient_contact
      FROM
        public."ShopOrders" o
      WHERE
        o.delivery_driver ->> 'id' = $1
    `;

    const dateOptionFilter = await this.calculateWorkingHours(
      branchId,
      dateOption,
      timeZone,
    );
    sql += dateOptionFilter;

    sql += 'ORDER BY o.modified_at DESC';

    const params = [driverId];

    try {
      const result = await this.databaseService.query(sql, params);
      return result;
    } catch (error) {
      throw new Error('Failed to fetch orders by the driver');
    }
  }

  private async getAllBranchesDrivers(
    shopId: string,
    dateOption: string,
    timeZone: string | null,
  ): Promise<any> {
    const branchesQuery = `
      SELECT branch_id, branch_name 
      FROM public."ShopBranches" 
      WHERE fk_shop_id = $1
    `;

    let branches: any;
    try {
      branches = await this.databaseService.query(branchesQuery, [shopId]);
    } catch (error) {
      throw new Error('Failed to fetch branches');
    }

    if (branches.length === 0) {
      return [];
    }

    const allDrivers = [];

    for (const branch of branches) {
      try {
        const branchDrivers = await this.getSingleBranchDrivers(
          branch.branch_id,
          dateOption,
          timeZone,
        );

        allDrivers.push(...branchDrivers);
      } catch (error) {
        console.error(
          `Failed to fetch drivers for branch ${branch.branch_id}:`,
          error,
        );
        continue;
      }
    }

    allDrivers.sort((a, b) => {
      if (b.count !== a.count) {
        return b.count - a.count;
      }
      return a.name.localeCompare(b.name);
    });

    return allDrivers;
  }

  async updateDriverDetails(
    driverId: string,
    newName: string,
    newNumber: string,
  ): Promise<void> {
    const sql = `
      UPDATE public."ShopDeliveryDrivers"
      SET
        driver_name = $1,
        driver_contact = $2
      WHERE
        driver_id = $3;
    `;

    const params = [newName, newNumber, driverId];

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to update driver details');
    }
  }

  async deleteDeliveryDriver(driverId: string): Promise<void> {
    const sql = `
      DELETE FROM public."ShopDeliveryDrivers"
      WHERE driver_id = $1;
    `;

    const params = [driverId];

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      throw new Error('Failed to delete driver details');
    }
  }

  private adjustTimezoneOffset(
    date: Date,
    timeZone: string,
    reverse?: boolean,
  ): Date {
    const offset = this.shopService.getOffset(timeZone);
    const reverseCalc = reverse ? -offset : +offset;
    const adjustedDate = new Date(date.getTime() + reverseCalc * 1000);
    return adjustedDate;
  }

  private dateOptionsFilter(dateOption: string) {
    let noOfDays = 0;

    switch (dateOption) {
      case 'today':
        noOfDays = 0;
        break;
      case 'last_seven_days':
        noOfDays = 7;
        break;
      case 'last_thirty_days':
        noOfDays = 30;
        break;
      case 'last_ninety_days':
        noOfDays = 90;
        break;
    }
    const calculatedDate = subDays(new Date(), noOfDays);
    return calculatedDate;
  }

  async calculateWorkingHours(
    branchId: string,
    dateOption: string,
    timeZone: string | null = 'Asia/Dubai',
  ) {
    let result = [];
    const currentTime = new Date();
    const sql = `
      SELECT
        branch_timings
      FROM
        public."ShopBranches"
      WHERE
        branch_id = $1
    `;

    try {
      result = await this.databaseService.query(sql, [branchId]);
    } catch (error) {
      throw new Error('Failed to fetch branch details');
    }

    const branchTimings = result[0]?.branch_timings;
    const fromDate = this.dateOptionsFilter(dateOption);
    const toDate = new Date();

    const formatDateForSQL = (date: Date): string =>
      format(date, 'yyyy-MM-dd HH:mm:ss');

    const getWorkingHoursForDate = (date: Date) => {
      const dayIndex = getDay(date);
      return branchTimings.find((timings: any) => timings.day === dayIndex);
    };

    const calculateStartAndEndOfDay = (
      date: Date,
      workingHours: { start: string; end: string },
    ) => {
      let start = this.adjustTimezoneOffset(
        parse(
          `${format(date, 'dd-MM-yyyy')} ${workingHours.start}`,
          'dd-MM-yyyy h:mm a',
          new Date(),
        ),
        timeZone,
      );

      let end = this.adjustTimezoneOffset(
        parse(
          `${format(date, 'dd-MM-yyyy')} ${workingHours.end}`,
          'dd-MM-yyyy h:mm a',
          new Date(),
        ),
        timeZone,
      );

      const midnight = this.adjustTimezoneOffset(
        parse(
          `${format(date, 'dd-MM-yyyy')} 12:00 AM`,
          'dd-MM-yyyy h:mm a',
          date,
        ),
        timeZone,
      );

      if (isAfter(midnight, end) || isBefore(end, start)) {
        end = addDays(end, 1);
      }

      return { start, end };
    };

    let reportDate = fromDate;
    let workingHours = getWorkingHoursForDate(reportDate);
    let { start: startOfDay, end: endOfDay } = calculateStartAndEndOfDay(
      reportDate,
      workingHours,
    );
    if (currentTime < startOfDay) {
      reportDate = subDays(reportDate, 1);
      workingHours = getWorkingHoursForDate(reportDate);
      ({ start: startOfDay, end: endOfDay } = calculateStartAndEndOfDay(
        reportDate,
        workingHours,
      ));
    }

    const toDateWorkingHours = getWorkingHoursForDate(toDate);
    const { end: endOfToDateDay } = calculateStartAndEndOfDay(
      toDate,
      toDateWorkingHours,
    );
    endOfDay = endOfToDateDay;

    const calculatedStart = formatDateForSQL(startOfDay);
    const calculatedEnd = formatDateForSQL(endOfDay);

    const sqlCondition = ` AND o.modified_at BETWEEN '${calculatedStart}' AND '${calculatedEnd}'`;
    return sqlCondition;
  }

  async cancelOrders(order: CancelOrders): Promise<void> {
    const cancelledBy = order.cancelled_by;
    for (const orderId of order.order_ids) {
      await this.databaseService.query('BEGIN');

      try {
        const orderInfo = await this.getOrdersById(orderId);
        if (!orderInfo || orderInfo?.status === 'cancelled') {
          continue;
        }

        await this.cancelOrder(orderInfo, cancelledBy, order);

        await this.databaseService.query('COMMIT');
      } catch (error) {
        await this.databaseService.query('ROLLBACK');
        throw error;
      }
    }
  }

  private async getOrdersById(orderId: string): Promise<any> {
    const sql = `
      SELECT *
      FROM public."ShopOrders"
      WHERE order_id = $1
      AND status != 'cancelled'
    `;

    const result = await this.databaseService.query(sql, [orderId]);
    return result.length > 0 ? result[0] : null;
  }

  async cancelOrder(
    order: any,
    cancelledBy: string,
    orderPayload: CancelOrders,
  ) {
    try {
      let refunded = false;
      if (orderPayload?.refund && order?.stripe_charge_id) {
        const shopPayment = await this.findStripeAPIKey(
          order?.fk_shop_id,
          order?.fk_branch_id,
        );

        if (shopPayment.gatewayName === 'stripe') {
          try {
            const stripeRes = new Stripe(shopPayment.stripeKey, {
              apiVersion: '2025-06-30.basil',
            });
            await stripeRes.refunds.create({
              charge: order?.stripe_charge_id,
            });
            refunded = true;
          } catch (err) {
            refunded = false;
          }
        } else if (shopPayment.gatewayName === 'tap') {
          try {
            await axios.post(
              'https://api.tap.company/v2/refunds/',
              {
                charge_id: order?.stripe_charge_id,
                amount: order?.bill?.total_bill,
                reason: 'Booking Cancelled',
                currency: 'AED',
              },
              {
                headers: {
                  Authorization: `Bearer ${shopPayment.tapkey}`,
                  accept: 'application/json',
                  'content-type': 'application/json',
                },
              },
            );
            refunded = true;
          } catch (err) {
            refunded = false;
          }
        }
      }

      let updatedOrderName = order.order_name;
      updatedOrderName = `CNL${updatedOrderName.substring(3)}`;
      const sqlCancelorder = `
          UPDATE public."ShopOrders"
            SET status = 'cancelled',
            cancelled_by = $2,
            cancelled_on = NOW(),
            order_name = $3,
            is_refunded= $4
          WHERE order_id = $1
      `;
      await this.databaseService.query(sqlCancelorder, [
        order.order_id,
        cancelledBy,
        updatedOrderName,
        refunded,
      ]);

      const total_bill = order?.bill?.total_bill;
      const shop = await this.shopService.getShopEmailDetails(
        order?.fk_shop_id,
      );
      const customer = await this.shopService.getCustomerDetailsById(
        order?.fk_customer_id,
        order?.fk_shop_id,
        order?.fk_branch_id,
      );

      if (order?.promo_code) {
        try {
          await this.processDiscountUsage(
            order?.fk_customer_id,
            order?.fk_discount_id,
            true,
          );
        } catch (error) {
          throw new Error('Failed to update discount usage');
        }
      }

      // send email
      await this.commonService.sendMail({
        from: shop.shopSenderEmail,
        to: shop.shopEmails,
        password: shop.shopEmailPassword,
        secret: process.env.EMAIL_SECRET,
        subject: `Cancelled Order: ${order.order_name}`,
        text: `Cancelled By: ${cancelledBy}
Customer Name: ${customer?.customer_name}
Phone: ${customer?.phone_number}
Bill: ${total_bill} AED `,
      });

      // send whatsapp
      await axios.post(
        `${process.env.WHATSAPP_URL}/commerce/send-whatsapp-message`,
        {
          shopId: order?.fk_shop_id,
          customerPhone: customer?.phone_number,
          message: `Your order ${order?.order_name} has been cancelled.${
            refunded
              ? `The amount of ${total_bill} AED will be refunded within 3-5 business days.`
              : ''
          }`,
        },
      );
    } catch (error) {
      console.error('Cancel order error:', error);
    }
  }

  async getRefundType(shopId: string, branchId: string): Promise<any> {
    let sql = `
      SELECT
        shop_id,branch_id,
        auto_refundable auto_refundable
      FROM public."Shops" s
		  JOIN public."ShopBranches" b on b.fk_shop_id = s.shop_id
      WHERE
        s.shop_id = $1
    `;

    const params = [shopId];

    if (branchId !== 'null') {
      sql += `
       AND
        branch_id = $2
      `;
      params.push(branchId);
    }

    try {
      const result = await this.databaseService.query(sql, params);
      if (result.length > 0) {
        return {
          shop_id: result[0].shop_id,
          auto_refundable: result[0].auto_refundable,
        };
      }

      return {
        shop_id: null,
        auto_refundable: null,
      };
    } catch (error) {
      throw new Error('Failed to fetch refund types');
    }
  }

  async addQuotationOrder(orderData: CreateQuotationOrderDto): Promise<any> {
    const {
      customer_id,
      branch_id,
      items,
      additional_notes,
      bill_amount,
      address,
      shop_id,
      zone_id,
    } = orderData;
    try {
      const lastOrderQuery = `
      SELECT o.order_name, s.shop_auto_accept
      FROM public."Shops" s
      LEFT JOIN (
          SELECT o.order_name, o.fk_shop_id
          FROM public."ShopOrders" o
          WHERE o.fk_shop_id = $1
          ORDER BY o.created_at DESC
          LIMIT 1
      ) o ON s.shop_id = o.fk_shop_id
      WHERE s.shop_id = $1;
    `;
      const lastOrderParams = [shop_id];
      let lastOrderResult = null;
      let quantityCheckResult = null;

      let newOrderName = '';
      try {
        lastOrderResult = await this.databaseService.query(
          lastOrderQuery,
          lastOrderParams,
        );
        const lastOrderName =
          +lastOrderResult[0]?.order_name?.split('-')[1] || 0;
        newOrderName = `ORD-${lastOrderName + 1}`;
      } catch (error) {
        throw new Error('Failed to fetch last order');
      }

      const itemQuantitiesMap = items.reduce(
        (acc, item) => {
          if (acc[item.id]) {
            acc[item.id] += item.quantity;
          } else {
            acc[item.id] = item.quantity;
          }
          return acc;
        },
        {} as { [key: string]: number },
      );

      const itemIds = Object.keys(itemQuantitiesMap);

      const quantityCheckQuery = `
      SELECT
        i.item_id,
        i.item_quantity::int4
      FROM
        public."ShopItems" i
      WHERE
        i.fk_branch_id = $1
      AND
        i.item_id = ANY($2::uuid[])
    `;

      const quantityCheckParams = [branch_id, itemIds];
      try {
        quantityCheckResult = await this.databaseService.query(
          quantityCheckQuery,
          quantityCheckParams,
        );
      } catch (error) {
        throw new Error('Failed to fetch item quantity');
      }

      const stockQuantities = quantityCheckResult.reduce(
        (
          acc: { [x: string]: any },
          row: { item_id: string | number; item_quantity: any },
        ) => {
          acc[row.item_id] = row.item_quantity;
          return acc;
        },
        {} as { [key: string]: number },
      );

      let quantityValidationError = false;
      for (const [itemId, requestedQuantity] of Object.entries(
        itemQuantitiesMap,
      )) {
        const availableQuantity = stockQuantities[itemId];
        if (requestedQuantity > availableQuantity) {
          quantityValidationError = true;
          break;
        }
      }

      if (quantityValidationError) {
        throw new BadRequestException(
          'Quantity validation failed for items in the checkout',
        );
      }

      // const autoAccept = lastOrderResult[0].shop_auto_accept;

      const orderStatus = 'quotationSent';

      const sql = `
      INSERT INTO public."ShopOrders" (
        order_name,
        fk_customer_id,
        fk_shop_id,
        fk_branch_id,
        items,
        additional_notes,
        bill,
        customer_address,
        status,
        fk_zone_id,payment_method
      ) VALUES ($1, $2, $3, $4, $5::jsonb,
        $6, $7::jsonb, $8::jsonb, $9,   $10,$11)
      RETURNING *
    `;
      const params = [
        newOrderName,
        customer_id,
        shop_id,
        branch_id,
        JSON.stringify(items),
        // promo_code,
        // discount_id,
        additional_notes,
        JSON.stringify(bill_amount),
        JSON.stringify(address),
        orderStatus,

        zone_id,
        'quotation',
      ];

      let order: any[];
      try {
        order = await this.databaseService.query(sql, params);
      } catch (error) {
        throw new Error('Failed to add order');
      }

      const batchSize = 10;
      const itemEntries = Object.entries(itemQuantitiesMap);

      for (let i = 0; i < itemEntries.length; i += batchSize) {
        const batch = itemEntries.slice(i, i + batchSize);

        const itemIdsBatch = batch.map(([id]) => id);
        const requestedQuantitiesBatch = batch.map(([, quantity]) => quantity);

        const updateQuantitiesQuery = `
        UPDATE
          public."ShopItems"
        SET
          item_quantity = item_quantity - subquery.requested_quantity
        FROM (
          SELECT
            unnest($2::uuid[]) AS item_id,
            unnest($3::int[]) AS requested_quantity
        ) AS subquery
        WHERE
          public."ShopItems".item_id = subquery.item_id
        AND
          public."ShopItems".fk_branch_id = $1;
      `;
        const updateQuantitiesParams = [
          branch_id,
          itemIdsBatch,
          requestedQuantitiesBatch,
        ];

        try {
          await this.databaseService.query(
            updateQuantitiesQuery,
            updateQuantitiesParams,
          );
        } catch (error) {
          throw new Error('Failed to update item quantities');
        }
      }

      const updateStatusQuery = `
      UPDATE public."ShopItems"
        SET item_status = 'out-of-stock'
      WHERE item_quantity <= 0
      AND item_status = 'in-stock';
    `;
      try {
        await this.databaseService.query(updateStatusQuery);
      } catch (error) {
        throw new Error('Failed to update item status');
      }

      try {
        await this.customerShopBranchLinking(customer_id, shop_id, branch_id);
      } catch (error) {
        throw new Error('Customer linking failed');
      }

      try {
        const { order_id } = order[0];

        await axios.post(
          `${process.env.WHATSAPP_URL}/commerce/send-quotation-message`,
          {
            shopId: shop_id?.toLowerCase(),
            orderId: order_id,
          },
        );
      } catch (error) {
        throw new Error(
          'Failed to send email, OneSignal notification and WhatsApp message',
        );
      }
      return { order_id: order[0].order_id };
    } catch (error) {}
  }

  async getProfilePageDetails(branch_id: string): Promise<any> {
    const sql = `
        SELECT
        s.shop_id AS "shopId",
        s.shop_name AS "shopName",
        b.branch_id AS "branchId",
        b.branch_name AS "branchName",
        fl.listing_image AS "listingImage",
        b.branch_address AS "address",
        s.phone_number AS "shopWhatsappNumber",
        branch_timings AS "branchTiming",
        fl.branch_emirate AS "branchEmirate",
        fl.average_spend AS "averageSpend",
        fl.branch_tags AS "branchTags",
        fl.more_info AS "moreInfo",
        fl.shop_landline_numbers AS "shopLandlineNumbers",
        fl.popular_items AS "popularItems",
        fl.known_for AS "knownFor",
        fl.image_gallery AS "imageGallery" ,
        branch_location "branchLocation",
        branch_display_name "branchDisplayName",
        fl.show_in_landing_page "showInLandingPage",
        fl.social_links "socialLinks",
        fl.branch_menus "catalogueImages",
        s.is_only_for_listing AS "onlyForListing"
      FROM
        public."Shops" s
      JOIN
        public."ShopBranches" b ON s.shop_id = b.fk_shop_id
      LEFT JOIN
        public."ShopLandingPage" fl ON s.shop_id = fl.fk_shop_id
          AND fl.branch_id = b.branch_id
      WHERE b.branch_id =$1;
    `;

    const result = await this.databaseService.query(sql, [branch_id]);

    if (result.length === 0) {
      throw new NotFoundException(`branch_id with ID ${branch_id} not found`);
    }

    return result[0];
  }

  async getFAQs(branchId: string) {
    try {
      const sqlQuery = `
            SELECT q.question_id AS faqId, q.question, q.answer, q.fk_category_id AS categoryId, c.category_name AS categoryName
            FROM public."FAQQuestions" q
            JOIN public."FAQCategory" c ON q.fk_category_id = c.categroy_id
            WHERE  q.fk_branch_id =$1 AND q.fk_shop_id IS NOT NULL
            order by c.category_name,q.question;
        `;
      const faqs = await this.databaseService.query(sqlQuery, [branchId]);
      // Grouping FAQs by category
      const groupedFaqs = faqs.reduce((acc, faq) => {
        const categoryIndex = acc.findIndex(
          (cat) => cat.categoryId === faq.categoryid,
        );

        if (categoryIndex !== -1) {
          acc[categoryIndex].faqs.push({
            faqId: faq.faqid,
            question: faq.question,
            answer: faq.answer,
            categoryId: faq.categoryid,
          });
        } else {
          acc.push({
            categoryId: faq.categoryid,
            categoryName: faq.categoryname,
            faqs: [
              {
                faqId: faq.faqid,
                question: faq.question,
                answer: faq.answer,
                categoryId: faq.categoryid,
              },
            ],
          });
        }
        return acc;
      }, []);

      return groupedFaqs;
    } catch (error) {
      throw new Error('Failed to fetch FAQs: ' + error.message);
    }
  }

  async getCustomizationData(type: string, shopId: string, branchId: string) {
    try {
      let fieldToSelect = '';
      switch (type) {
        case 'variants':
          fieldToSelect = 'item_variants';
          break;
        case 'combos':
          fieldToSelect = 'item_combos';
          break;
        case 'add-ons':
          const addOnsQuery = `
          SELECT
             jsonb_agg(addon_groups) AS data
         FROM (
             SELECT DISTINCT ON (addon_groups->>'id')
                 addon_groups
             FROM (
                 SELECT jsonb_build_object(
                     'id', ag.id,
                     'selection', ag.selection,
                     'group_name', ag.group_name,
                     'item_id', i.item_id,
                     'item_name', i.item_name,
                     'add_ons_items', (
                         SELECT jsonb_agg(
                             jsonb_build_object(
                                 'add_on_id', a.add_on_id,
                                 'add_on_name', a.add_on_name,
                                 'add_on_type', a.add_on_type,
                                 'add_on_price', a.add_on_price
                             )
                         )
                         FROM public."ShopAddOns" a
                         WHERE a.add_on_id = ANY(ag.add_ons_items)
                     ),
                     'branch_name', b.branch_name,
                     'branch_id', b.branch_id
                 ) AS addon_groups
                 FROM public."ShopItems" i
                 CROSS JOIN LATERAL jsonb_to_recordset(i.item_add_ons_group)
                     AS ag(id uuid, selection jsonb, group_name text, add_ons_items uuid[])
                 JOIN public."ShopBranches" b ON i.fk_branch_id = b.branch_id
                   AND b.fk_shop_id = $1
                 WHERE i.fk_branch_id IN (
                  SELECT branch_id FROM public."ShopBranches" sb
                  WHERE sb.fk_shop_id = $1
                  ${branchId ? 'AND sb.branch_id = $2' : ''}
                 )
                   AND i.item_add_ons_group IS NOT NULL
                   AND jsonb_array_length(i.item_add_ons_group) > 0
             ) inner_query
             WHERE addon_groups->>'id' IS NOT NULL
             ORDER BY addon_groups->>'id'
         ) final_query;
         `;

          const params = [shopId];

          if (branchId) {
            params.push(branchId);
          }

          const addOnsResult = await this.databaseService.query(
            addOnsQuery,
            params,
          );
          return addOnsResult[0]?.data || [];

        default:
          throw new Error('Invalid type specified');
      }

      const sql = `
      SELECT
          data,
          item_id,
          item_name,
          branch_name,
          branch_id
      FROM (
          SELECT
              ${fieldToSelect} AS data,
              s.created_at,
              item_id,
              item_name,
              b.branch_name,
              b.branch_id
          FROM public."ShopItems" s
          JOIN public."ShopBranches" b ON s.fk_branch_id = b.branch_id
                   AND b.fk_shop_id = $1
          WHERE s.fk_branch_id IN (
            SELECT branch_id FROM public."ShopBranches" sb
            WHERE sb.fk_shop_id = $1
            ${branchId ? 'AND sb.branch_id = $2' : ''}
          )
            AND ${fieldToSelect} IS NOT NULL
            AND jsonb_array_length(${fieldToSelect}) > 0
          ORDER BY ${fieldToSelect}->>'id', s.created_at DESC
      ) subquery;
      `;

      const params = [shopId];

      if (branchId) {
        params.push(branchId);
      }
      const result = await this.databaseService.query(sql, params);
      const uniqueIds = new Set();
      const uniqueData = [];
      result.forEach((item) => {
        item.data.forEach((subItem) => {
          if (!uniqueIds.has(subItem.id)) {
            uniqueIds.add(subItem.id);
            subItem.item_id = item.item_id;
            subItem.item_name = item.item_name;
            subItem.branch_name = item.branch_name;
            subItem.branch_id = item.branch_id;
            uniqueData.push(subItem);
          }
        });
      });

      return uniqueData;
    } catch (error) {
      console.error('Failed to fetch customization data:', error);
      throw new Error('Failed to fetch customization data');
    }
  }

  async getBranchDelayedOrders(branchId: string): Promise<any[]> {
    const sql = `
      SELECT
        jsonb_agg(
          jsonb_build_object(
            'type', status_type,
            'count', count
          )
        ) AS result
      FROM (
        SELECT 'Out For Delivery' AS status_type, COUNT(o.order_id) AS count
        FROM public."ShopOrders" o
        JOIN public."ShopBranches" b ON o.fk_branch_id = b.branch_id
        WHERE o.status = 'accepted'
          AND o.order_type = 'delivery'
          AND o.fk_branch_id = $1
          GROUP BY status_type
          HAVING COUNT(o.order_id) > 0
        UNION ALL
        SELECT 'Ready For Pickup' AS status_type, COUNT(o.order_id) AS count
        FROM public."ShopOrders" o
        JOIN public."ShopBranches" b ON o.fk_branch_id = b.branch_id
        WHERE o.status = 'accepted'
          AND o.order_type = 'pickup'
          AND o.fk_branch_id = $1
          GROUP BY status_type
          HAVING COUNT(o.order_id) > 0
        UNION ALL
        SELECT 'Picked Up' AS status_type, COUNT(o.order_id) AS count
        FROM public."ShopOrders" o
        WHERE o.status = 'ready_for_pickup'
          AND (CURRENT_TIMESTAMP - o.modified_at) >= INTERVAL '1 hour'
          AND o.fk_branch_id = $1
          GROUP BY status_type
          HAVING COUNT(o.order_id) > 0
      ) AS subquery;
    `;

    try {
      const response = await this.databaseService.query(sql, [branchId]);
      return response[0]?.result;
    } catch (error) {
      throw new Error('Failed to fetch branch delayed order count');
    }
  }

  async updateItemOrdering(branchId: string, itemIds: string[]) {
    try {
      // Validate inputs
      if (!Array.isArray(itemIds)) {
        throw new Error('itemIds must be an array');
      }

      // If itemIds is empty, return early
      if (itemIds.length === 0) {
        return 'No items to update';
      }

      const sql = `
        UPDATE public."ShopItems" i
        SET item_order = o.order_number
        FROM (
          SELECT
            unnest($2::uuid[]) as item_id,
            generate_series(1, $3) as order_number
        ) o
        WHERE i.item_id = o.item_id
        AND i.fk_branch_id = $1
      `;
      // Ensure parameters are in the correct format
      const params = [branchId, itemIds, itemIds.length];

      await this.databaseService.query(sql, params);

      return { success: true, message: 'Item ordering updated successfully' };
    } catch (error) {
      console.error('Error in updateItemOrdering:', error);
      throw new Error(`Failed to update item ordering: ${error.message}`);
    }
  }

  async copyItemsBetweenBranches(
    sourceBranchId: string,
    targetBranchId: string,
  ): Promise<{
    itemsCount: number;
    categoriesCount: number;
    // addOnsCount: number;
  }> {
    try {
      if (sourceBranchId === targetBranchId) {
        throw new Error('Cannot copy items within the same branch');
      }

      const [sourceBranch, targetBranch] = await Promise.all([
        this.getBranchInfo(sourceBranchId),
        this.getBranchInfo(targetBranchId),
      ]);

      if (!sourceBranch || !targetBranch) {
        throw new Error('Source or target branch not found');
      }

      console.log(
        `Source branch: ${sourceBranch.branch_name}, Target branch: ${targetBranch.branch_name}`,
      );

      const categoryMappings = await this.copyCategories(
        sourceBranchId,
        targetBranchId,
      );

      const result = await this.copyItems(
        sourceBranchId,
        targetBranchId,
        categoryMappings,
      );

      await this.preserveCategoryOrder(
        sourceBranchId,
        targetBranchId,
        categoryMappings,
      );

      console.log(
        `Successfully copied ${result.itemsCount} items and ${categoryMappings.size} categories from branch ${sourceBranch.branch_name} to ${targetBranch.branch_name}`,
      );

      return {
        itemsCount: result.itemsCount,
        categoriesCount: categoryMappings.size,
        // addOnsCount: result.addOnsCount || 0,
      };
    } catch (error) {
      console.error('Error copying items between branches:', error);
      throw new Error(
        `Failed to copy items between branches: ${error.message}`,
      );
    }
  }

  private async getBranchInfo(branchId: string): Promise<any> {
    const sql = `
      SELECT branch_id, branch_name, branch_category_order
      FROM public."ShopBranches"
      WHERE branch_id = $1
    `;
    const result = await this.databaseService.query(sql, [branchId]);
    return result.length > 0 ? result[0] : null;
  }

  private async copyCategories(
    sourceBranchId: string,
    targetBranchId: string,
  ): Promise<Map<string, string>> {
    const sourceCategories = await this.getCategories(sourceBranchId);
    const categoryMappings = new Map<string, string>();

    if (sourceCategories.length === 0) {
      return categoryMappings;
    }

    console.log(`Found ${sourceCategories.length} categories to copy`);

    const newCategoryData = sourceCategories.map((category) => {
      return {
        fk_branch_id: targetBranchId,
        category_name: category.category_name,
        category_description: category.category_description,
        category_availability: category.category_availability,
        category_availability_timings: category.category_availability_timings,
        status: category.status,
      };
    });

    const newCategoryIds = await this.insertCategories(newCategoryData);

    sourceCategories.forEach((category, index) => {
      categoryMappings.set(category.category_id, newCategoryIds[index]);
    });

    console.log(`Successfully mapped ${categoryMappings.size} categories`);
    return categoryMappings;
  }

  private async getCategories(branchId: string): Promise<any[]> {
    const sql = `
      SELECT
        category_id,
        category_name,
        category_description,
        category_availability,
        category_availability_timings,
        status
      FROM public."ShopCategories"
      WHERE fk_branch_id = $1
      ORDER BY category_name
    `;
    const categories = await this.databaseService.query(sql, [branchId]);

    return categories.map((category) => {
      if (typeof category.category_availability_timings === 'string') {
        try {
          category.category_availability_timings = JSON.parse(
            category.category_availability_timings,
          );
        } catch (e) {
          console.warn(
            `Failed to parse category_availability_timings for category ${category.category_id}:`,
            e,
          );
        }
      }

      return category;
    });
  }

  private async insertCategories(categoriesData: any[]): Promise<string[]> {
    try {
      const insertedIds = [];

      for (const category of categoriesData) {
        const availabilityTimings = category.category_availability_timings
          ? JSON.stringify(category.category_availability_timings)
          : null;

        const sql = `
          INSERT INTO public."ShopCategories" (
            fk_branch_id,
            category_name,
            category_description,
            category_availability,
            category_availability_timings,
            status
          ) VALUES ($1, $2, $3, $4, $5, $6)
          RETURNING category_id
        `;

        const params = [
          category.fk_branch_id,
          category.category_name,
          category.category_description,
          category.category_availability,
          availabilityTimings,
          category.status,
        ];

        console.log(`Inserting category: ${category.category_name}`);

        const result = await this.databaseService.query(sql, params);
        if (result && result.length > 0) {
          insertedIds.push(result[0].category_id);
        }
      }

      return insertedIds;
    } catch (error) {
      console.error('Failed to insert categories:', error);
      throw new Error(`Failed to insert categories: ${error.message}`);
    }
  }

  private async copyItems(
    sourceBranchId: string,
    targetBranchId: string,
    categoryMappings: Map<string, string>,
  ): Promise<{
    itemsCount: number;
    addOnsCount: number;
  }> {
    const sourceItems = await this.getItems(sourceBranchId);
    console.log(`Found ${sourceItems.length} items to copy`);

    const addOnMappings = await this.copyAddOns(sourceBranchId, targetBranchId);
    // console.log(`Created ${addOnMappings.size} add-on mappings`);

    let insertedCount = 0;
    const batchSize = 5;

    for (let i = 0; i < sourceItems.length; i += batchSize) {
      const batch = sourceItems.slice(i, i + batchSize);
      console.log(
        `Processing batch ${i / batchSize + 1}/${Math.ceil(sourceItems.length / batchSize)}`,
      );

      for (const item of batch) {
        try {
          if (!categoryMappings.has(item.fk_category_id)) {
            console.warn(
              `No category mapping found for item ${item.item_name} with category ID ${item.fk_category_id}`,
            );
            continue;
          }

          const newItemId = uuidv4();

          let newImageLinks = [];
          if (
            item.item_image_links &&
            Array.isArray(item.item_image_links) &&
            item.item_image_links.length > 0
          ) {
            for (const imageLink of item.item_image_links) {
              try {
                const newImageLink = await this.copyS3Image(
                  imageLink,
                  sourceBranchId,
                  targetBranchId,
                  newItemId,
                );
                newImageLinks.push(newImageLink);
                console.log(
                  `Copied image for ${item.item_name}: ${newImageLink}`,
                );
              } catch (error) {
                console.error(
                  `Failed to copy image for item ${item.item_name}:`,
                  error,
                );
                newImageLinks.push(imageLink);
              }
            }
          }

          let itemVariants = item.item_variants;
          if (itemVariants && Array.isArray(itemVariants)) {
            itemVariants = this.updateVariantsWithNewIds(itemVariants);
          }

          let itemCombos = item.item_combos;
          if (itemCombos && Array.isArray(itemCombos)) {
            itemCombos = this.updateCombosWithNewIds(itemCombos);
          }

          let itemAddOnsGroup = item.item_add_ons_group;
          if (itemAddOnsGroup && Array.isArray(itemAddOnsGroup)) {
            itemAddOnsGroup = this.updateAddOnsGroupWithNewIds(
              itemAddOnsGroup,
              addOnMappings,
            );
          }

          const newItem = {
            item_id: newItemId,
            fk_branch_id: targetBranchId,
            item_name: item.item_name,
            item_description: item.item_description,
            item_type: item.item_type,
            fk_category_id: categoryMappings.get(item.fk_category_id),
            item_price: item.item_price,
            item_image_links: newImageLinks,
            item_status: item.item_status,
            item_variants: itemVariants,
            item_add_ons_group: itemAddOnsGroup,
            item_combos: itemCombos,
            item_order: item.item_order,
            item_quantity: item.item_quantity || 0,
          };

          await this.insertItem(newItem);
          insertedCount++;
        } catch (error) {
          console.error(`Error processing item ${item.item_name}:`, error);
        }
      }
    }

    return {
      itemsCount: insertedCount,
      addOnsCount: addOnMappings.size,
    };
  }

  private async insertItem(item: any): Promise<string> {
    const variants =
      typeof item.item_variants === 'object'
        ? JSON.stringify(item.item_variants)
        : item.item_variants;

    const addOnsGroup =
      typeof item.item_add_ons_group === 'object'
        ? JSON.stringify(item.item_add_ons_group)
        : item.item_add_ons_group;

    const combos =
      typeof item.item_combos === 'object'
        ? JSON.stringify(item.item_combos)
        : item.item_combos;

    const sql = `
      INSERT INTO public."ShopItems" (
        item_id,
        fk_branch_id,
        item_name,
        item_description,
        item_type,
        fk_category_id,
        item_price,
        item_image_links,
        item_status,
        item_variants,
        item_add_ons_group,
        item_combos,
        item_order,
        item_quantity
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      RETURNING item_id
    `;

    const params = [
      item.item_id,
      item.fk_branch_id,
      item.item_name,
      item.item_description,
      item.item_type,
      item.fk_category_id,
      item.item_price,
      item.item_image_links,
      item.item_status,
      variants,
      addOnsGroup,
      combos,
      item.item_order,
      item.item_quantity,
    ];

    const result = await this.databaseService.query(sql, params);
    return result[0].item_id;
  }

  private async getItems(branchId: string): Promise<any[]> {
    const sql = `
      SELECT
        item_id,
        fk_branch_id,
        item_name,
        item_description,
        item_type,
        fk_category_id,
        item_price,
        item_image_links,
        item_status,
        item_variants,
        item_add_ons_group,
        item_combos,
        item_order,
        item_quantity
      FROM public."ShopItems"
      WHERE fk_branch_id = $1
      ORDER BY item_order, item_name
    `;
    const items = await this.databaseService.query(sql, [branchId]);

    return items.map((item) => {
      ['item_variants', 'item_add_ons_group', 'item_combos'].forEach(
        (field) => {
          if (typeof item[field] === 'string' && item[field]) {
            try {
              item[field] = JSON.parse(item[field]);
            } catch (e) {
              console.warn(`Failed to parse ${field} for item ${item.item_id}`);
            }
          }
        },
      );

      return item;
    });
  }

  private async copyS3Image(
    sourceImageUrl: string,
    sourceBranchId: string,
    targetBranchId: string,
    newItemId: string,
  ): Promise<string> {
    if (
      !sourceImageUrl ||
      !sourceImageUrl.includes('s3.me-central-1.amazonaws.com')
    ) {
      return sourceImageUrl;
    }

    try {
      const timestamp = Date.now();

      const parsedUrl = new URL(sourceImageUrl);
      const bucketName = parsedUrl.host.split('.')[0];
      const sourceKey = parsedUrl.pathname.substring(1);

      console.log(`Source Image URL: ${sourceImageUrl}`);
      console.log(`Source Key: ${sourceKey}`);

      let fileExtension = 'jpg';
      const extensionMatch = sourceImageUrl.match(/\.([^.]+)$/);
      if (extensionMatch && extensionMatch[1]) {
        fileExtension = extensionMatch[1];
      }

      const newFilename = `${newItemId}_${timestamp}.${fileExtension}`;
      let targetKey = sourceKey;

      if (sourceBranchId && targetBranchId) {
        if (sourceKey.includes(`/branches/${sourceBranchId}/`)) {
          targetKey = sourceKey.replace(
            `/branches/${sourceBranchId}/`,
            `/branches/${targetBranchId}/`,
          );
        } else if (sourceKey.includes('/branches/')) {
          const pathParts = sourceKey.split('/');
          const branchIndex = pathParts.indexOf('branches');

          if (branchIndex !== -1 && branchIndex + 1 < pathParts.length) {
            pathParts[branchIndex + 1] = targetBranchId;
            targetKey = pathParts.join('/');
          }
        }
      }

      const lastSlashIndex = targetKey.lastIndexOf('/');
      if (lastSlashIndex !== -1) {
        targetKey = targetKey.substring(0, lastSlashIndex + 1) + newFilename;
      } else {
        targetKey = newFilename;
      }

      console.log(`Target Key: ${targetKey}`);

      const copyCommand = new CopyObjectCommand({
        Bucket: bucketName,
        CopySource: `${bucketName}${parsedUrl.pathname}`,
        Key: targetKey,
        MetadataDirective: 'COPY',
      });

      await s3Client.send(copyCommand);
      console.log(`Successfully copied S3 image to: ${targetKey}`);
      return `https://${bucketName}.s3.me-central-1.amazonaws.com/${targetKey}`;
    } catch (error) {
      console.error('Error copying S3 image:', error);
      return sourceImageUrl;
    }
  }

  private updateVariantsWithNewIds(variants: any[]): any[] {
    return variants.map((variant) => {
      return {
        ...variant,
        id: uuidv4(),
        options: Array.isArray(variant.options)
          ? variant.options.map((option) => {
              if (option.id) {
                return { ...option, id: uuidv4() };
              }
              return option;
            })
          : variant.options,
      };
    });
  }

  private updateCombosWithNewIds(combos: any[]): any[] {
    return combos.map((combo) => {
      return {
        ...combo,
        id: uuidv4(),
        options: Array.isArray(combo.options)
          ? combo.options.map((option) => {
              return { ...option, id: uuidv4() };
            })
          : combo.options,
      };
    });
  }

  private updateAddOnsGroupWithNewIds(
    addOnsGroups: any[],
    addOnMappings: Map<string, string>,
  ): any[] {
    return addOnsGroups.map((group) => {
      const newGroup = {
        ...group,
        id: uuidv4(),
        add_ons_items: Array.isArray(group.add_ons_items)
          ? group.add_ons_items.map((addOnId) => {
              return addOnMappings.get(addOnId) || addOnId;
            })
          : group.add_ons_items,
      };

      console.log(
        `Updated add-on group: ${group.group_name} with ${newGroup.add_ons_items.length} items`,
      );
      return newGroup;
    });
  }

  private async copyAddOns(
    sourceBranchId: string,
    targetBranchId: string,
  ): Promise<Map<string, string>> {
    const addOnMappings = new Map<string, string>();

    try {
      const sourceAddOns = await this.getAddOns(sourceBranchId);
      console.log(`Found ${sourceAddOns.length} add-ons to copy`);

      if (sourceAddOns.length === 0) {
        return addOnMappings;
      }

      const batchSize = 10;

      for (let i = 0; i < sourceAddOns.length; i += batchSize) {
        const batch = sourceAddOns.slice(i, i + batchSize);
        console.log(
          `Processing add-ons batch ${i / batchSize + 1}/${Math.ceil(sourceAddOns.length / batchSize)}`,
        );

        for (const addOn of batch) {
          try {
            const newAddOnId = uuidv4();

            const newAddOn = {
              add_on_id: newAddOnId,
              add_on_name: addOn.add_on_name,
              add_on_type: addOn.add_on_type,
              add_on_price: addOn.add_on_price,
              fk_branch_id: targetBranchId,
            };

            await this.insertAddOn(newAddOn);
            addOnMappings.set(addOn.add_on_id, newAddOnId);
          } catch (error) {
            console.error(`Error copying add-on ${addOn.add_on_name}:`, error);
          }
        }
      }

      console.log(`Successfully copied ${addOnMappings.size} add-ons`);
      return addOnMappings;
    } catch (error) {
      console.error('Error copying add-ons:', error);
      return addOnMappings;
    }
  }

  private async getAddOns(branchId: string): Promise<any[]> {
    const sql = `
      SELECT
        add_on_id,
        add_on_name,
        add_on_type,
        add_on_price,
        fk_branch_id
      FROM public."ShopAddOns"
      WHERE fk_branch_id = $1
    `;

    const addOns = await this.databaseService.query(sql, [branchId]);
    return addOns;
  }

  private async insertAddOn(addOn: any): Promise<string> {
    const sql = `
      INSERT INTO public."ShopAddOns" (
        add_on_id,
        add_on_name,
        add_on_type,
        add_on_price,
        fk_branch_id
      ) VALUES ($1, $2, $3, $4, $5)
      RETURNING add_on_id
    `;

    const params = [
      addOn.add_on_id,
      addOn.add_on_name,
      addOn.add_on_type,
      addOn.add_on_price,
      addOn.fk_branch_id,
    ];

    const result = await this.databaseService.query(sql, params);
    console.log(
      `Inserted add-on: ${addOn.add_on_name} with ID: ${result[0].add_on_id}`,
    );
    return result[0].add_on_id;
  }

  async preserveCategoryOrder(
    sourceBranchId: string,
    targetBranchId: string,
    categoryMappings: Map<string, string>,
  ): Promise<void> {
    try {
      const sourceBranchResult = await this.databaseService.query(
        `SELECT branch_category_order FROM public."ShopBranches" WHERE branch_id = $1`,
        [sourceBranchId],
      );

      if (
        !sourceBranchResult.length ||
        !sourceBranchResult[0].branch_category_order
      ) {
        console.log('No category order to preserve');
        return;
      }

      const sourceCategoryOrder = sourceBranchResult[0].branch_category_order;
      console.log('Source category order:', sourceCategoryOrder);

      const newCategoryOrder = [];

      for (const oldCategoryId of sourceCategoryOrder) {
        const newCategoryId = categoryMappings.get(oldCategoryId);
        if (newCategoryId) {
          newCategoryOrder.push(newCategoryId);
        }
      }

      for (const [oldId, newId] of categoryMappings.entries()) {
        if (
          !sourceCategoryOrder.includes(oldId) &&
          !newCategoryOrder.includes(newId)
        ) {
          newCategoryOrder.push(newId);
        }
      }

      console.log('New category order:', newCategoryOrder);

      await this.databaseService.query(
        `UPDATE public."ShopBranches" SET branch_category_order = $2 WHERE branch_id = $1`,
        [targetBranchId, newCategoryOrder],
      );

      console.log('Successfully updated branch category order');
    } catch (error) {
      console.error('Error preserving category order:', error);
      throw new Error(`Failed to preserve category order: ${error.message}`);
    }
  }

  async toggleBranchOption(
    branchId: string,
    option: 'branch_delivery' | 'branch_pickup',
    status: boolean,
  ) {
    try {
      // Check if branch exists
      const checkBranchSql = `
        SELECT branch_id FROM public."ShopBranches"
        WHERE branch_id = $1
      `;

      const branch = await this.databaseService.query(checkBranchSql, [
        branchId,
      ]);

      if (!branch || branch.length === 0) {
        throw new NotFoundException(`Branch with ID ${branchId} not found`);
      }

      // Update the specified option
      const updateSql = `
        UPDATE public."ShopBranches"
        SET ${option} = $1
        WHERE branch_id = $2
      `;

      await this.databaseService.query(updateSql, [status, branchId]);

      return {
        success: true,
        message: `Branch ${option} updated successfully`,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to update branch ${option}: ${error.message}`);
    }
  }
}

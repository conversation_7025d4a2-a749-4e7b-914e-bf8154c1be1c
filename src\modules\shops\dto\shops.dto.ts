import { ApiProperty } from '@nestjs/swagger';

export class CreateShopDto {
  @ApiProperty()
  shop_id: string;

  @ApiProperty()
  shop_name: string;

  @ApiProperty()
  shop_social_links: any;

  @ApiProperty()
  shop_timings: any;

  @ApiProperty()
  phone_number: string;

  @ApiProperty()
  order_policy: string;

  @ApiProperty()
  payment_methods: any;
}
export class UpdateSubscriptionDto {
  @ApiProperty({ description: 'New subscription ID' })
  subscriptionId: string;

  @ApiProperty({ description: 'New customer ID' })
  customerId: string;
}

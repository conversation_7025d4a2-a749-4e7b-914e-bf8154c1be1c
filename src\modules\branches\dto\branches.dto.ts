import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsNumberString,
  IsOptional,
  IsString,
  IsUUID,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import {
  IsNonZeroNumberString,
  UniqueNames,
} from '../../../common/decorators/unique-name.decorator';

export class CreateBranchDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  shop_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  branch_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  branch_name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  branch_address: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumberString()
  phone_number: string;

  @ApiProperty()
  @IsNotEmpty()
  branch_timings: {
    weekday: Timing;
    weekend: Timing;
  };

  @ApiProperty()
  @IsOptional()
  @IsString()
  branch_logo?: string | null;

  @ApiProperty()
  @IsOptional()
  branch_description?: string | null;
}

class BreakTiming {
  @ApiProperty()
  @ValidateIf((o) => o !== null)
  @IsString()
  @IsNotEmpty()
  start: string;

  @ApiProperty()
  @ValidateIf((o) => o !== null)
  @IsString()
  @IsNotEmpty()
  end: string;
}

class Breaks {
  @ApiProperty({ nullable: true })
  @IsOptional()
  break1: BreakTiming | null;

  @ApiProperty({ nullable: true })
  @IsOptional()
  break2: BreakTiming | null;

  @ApiProperty({ nullable: true })
  @IsOptional()
  break3: BreakTiming | null;
}

export class BreakTimings {
  @ApiProperty()
  day: number;

  @ApiProperty({ type: [Breaks], nullable: true })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Breaks)
  @IsOptional()
  breaks: Breaks[] | null;

  @ApiProperty()
  status: boolean;
}

export class BranchTimings {
  @ApiProperty({ type: 'string' })
  @IsInt()
  day: number;

  @ApiProperty({ type: 'string' })
  @IsString()
  @IsNotEmpty()
  start: string;

  @ApiProperty({ type: 'string' })
  @IsString()
  @IsNotEmpty()
  end: string;

  @ApiProperty({ type: 'boolean' })
  @IsBoolean()
  status: boolean;
}

export class UpdateBranchDetailsDto {
  @ApiProperty()
  @IsString()
  branch_name: string;

  @ApiProperty()
  @IsString()
  branch_display_name: string;

  @ApiProperty()
  @IsString()
  branch_address: string;

  @ApiProperty({ required: false })
  @IsOptional()
  branch_maps_url?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  branch_logo_url?: string;

  @ApiProperty({ required: false, type: 'string', format: 'binary' })
  @IsOptional()
  @IsString()
  branch_logo?: any;

  @ApiProperty({ required: false })
  @IsOptional()
  branch_payment_modes?: string[] | null;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  auto_refundable: string;

  @ApiProperty({ required: false })
  @IsOptional()
  trn_number?: string | null;

  @ApiProperty()
  @IsString()
  branch_cancellation_number: string;
}

export class UpdatePromotionalBannersDto {
  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'binary' },
    required: false,
    maxItems: 5,
  })
  @IsOptional()
  branch_promotional_banners?: any;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
    required: false,
    maxItems: 5,
  })
  @IsOptional()
  branch_promotional_banner_urls?: string[] | null;

  @ApiProperty({
    type: 'array',
    items: { type: 'number' },
    required: false,
    maxItems: 5,
  })
  @IsOptional()
  banner_delete_index?: number[] | null;
}

class VariantOption {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  type: string;

  @ApiProperty()
  @IsNotEmpty()
  @Transform(({ value }) => parseFloat(value), { toClassOnly: true })
  @IsNumber()
  price: number;
}

class AddOnSelection {
  @ApiProperty()
  @IsNotEmpty()
  @Transform(({ value }) => parseInt(value), { toClassOnly: true })
  @IsInt()
  min: number;

  @ApiProperty()
  @IsNotEmpty()
  @Transform(({ value }) => parseInt(value), { toClassOnly: true })
  @IsInt()
  max: number;

  @ApiProperty()
  @IsNotEmpty()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  required: boolean;
}

export class Variant {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsNotEmpty()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  required: boolean;

  @ApiProperty({ type: [VariantOption] })
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => VariantOption)
  options: VariantOption[];
}

export class Combo {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({ type: AddOnSelection })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => AddOnSelection)
  selection: AddOnSelection;

  @ApiProperty({ type: [VariantOption] })
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => VariantOption)
  options: VariantOption[];
}

class AddOnItem {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  add_on_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  add_on_name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  add_on_type: string;

  @ApiProperty()
  @IsNotEmpty()
  @Transform(({ value }) =>
    typeof value === 'string' ? parseFloat(value) : value,
  )
  add_on_price: string | number;
}

export class AddOnGroup {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  group_name: string;

  @ApiProperty({ type: AddOnSelection })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => AddOnSelection)
  selection: AddOnSelection;

  @ApiProperty({ type: [AddOnItem] })
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => AddOnItem)
  add_ons_items: AddOnItem[];
}

export class CreateItemDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  branch_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  item_name: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  item_description: string | null;

  @ApiProperty({ enum: ['none'] })
  @IsNotEmpty()
  @IsString()
  @IsIn(['none'])
  item_type: string;

  @ApiProperty({ format: 'uuid' })
  @IsNotEmpty()
  @IsUUID()
  category_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumberString()
  @IsNonZeroNumberString({
    message:
      'Price of the item cannot be zero and can have up to 2 decimal places',
  })
  item_price: number;

  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'binary' },
    required: false,
    maxItems: 6,
  })
  @IsOptional()
  item_images?: any;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
    required: false,
    maxItems: 6,
  })
  @IsOptional()
  item_image_links?: string[] | null;

  @ApiProperty({
    enum: ['in-stock', 'out-of-stock'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['in-stock', 'out-of-stock'])
  item_status: string;

  @ApiProperty({ type: () => [Variant], required: false })
  @IsOptional()
  @Transform(({ value }) =>
    typeof value === 'string' ? JSON.parse(value) : value,
  )
  @Type(() => Variant)
  @UniqueNames('variants', {
    message: 'Variant titles and option names must be unique.',
  })
  item_variants?: Variant[] | null;

  @ApiProperty({ type: () => [Combo], required: false })
  @IsOptional()
  @Transform(({ value }) =>
    typeof value === 'string' ? JSON.parse(value) : value,
  )
  @Type(() => Combo)
  @UniqueNames('combos', {
    message: 'Combo titles and option names must be unique.',
  })
  item_combos?: Combo[] | null;

  @ApiProperty({
    type: () => [AddOnGroup],
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) =>
    typeof value === 'string' ? JSON.parse(value) : value,
  )
  @Type(() => AddOnGroup)
  @UniqueNames('addons', {
    message: 'Shop add-on group names and shop add-on names must be unique.',
  })
  item_add_ons_group?: AddOnGroup[] | null;

  @ApiProperty({ type: 'number', required: false })
  @IsOptional()
  item_quantity?: number;

  @ApiProperty({
    type: 'array',
    items: { type: 'number' },
    required: false,
    maxItems: 6,
  })
  @IsOptional()
  image_delete_index?: number[] | null;
}

export class CreateCategoryDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  branch_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  category_name: string;

  @ApiProperty()
  @IsOptional()
  category_description: string | null;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @IsIn(['all', 'same', 'different'])
  category_availability: string;

  @ApiProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => Object)
  category_availability_timings: CategoryTiming[] | null;
}

export class CreateBulkItemDto {
  @IsString()
  item_name: string;

  @IsString()
  item_description: string;

  @IsString()
  item_type: string;

  @IsString()
  category: string;

  @IsNumber()
  @IsNonZeroNumberString({
    message:
      'Price of the item cannot be zero and can have up to 2 decimal places',
  })
  @Min(0.01, { message: 'Price of the item must be greater than zero' })
  item_price: number;

  @IsString()
  item_image_link: string;

  @IsString()
  item_status: string;

  @IsArray()
  @IsOptional()
  item_variants: any[];

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  item_quantity: number = 0;
}

export class CreateAddOnDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  branch_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  add_on_name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  add_on_type: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  add_on_price: number;
}

class DiscountDate {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  start_date: string;

  @ApiProperty()
  @IsOptional()
  end_date: string | null;
}

class DiscountDurationDay {
  @ApiProperty()
  @IsOptional()
  start_time: string;

  @ApiProperty()
  @IsOptional()
  end_time: string;
}

export class CreateDiscountDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  shop_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  branch_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  discount_name: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  discount_code: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  uses_per_customer: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @IsIn(['all', 'new'])
  applicable_customer: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @IsIn(['menu', 'promo', 'auto', 'manual'])
  usage_method: string;

  @ApiProperty()
  @IsOptional()
  @IsUUID('4', { each: true })
  applicable_category: string[] | null;

  @ApiProperty()
  @IsOptional()
  @IsUUID('4', { each: true })
  applicable_items: string[] | null;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @IsIn(['percentage', 'flat', 'specific'])
  discount_type: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  discount_value: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  min_amount: number | null;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  max_amount: number | null;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  total_uses: number;

  @ApiProperty()
  @IsNotEmpty()
  discount_date: DiscountDate;

  @ApiProperty()
  @IsOptional()
  discount_duration_day: DiscountDurationDay | null;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  created_by: string;

  @ApiProperty({
    description: 'List of applicable order types',
    enum: ['delivery', 'pickup'],
    default: ['delivery', 'pickup'],
  })
  @IsOptional()
  @IsArray()
  @IsIn(['delivery', 'pickup'], { each: true })
  @Transform(({ value }) => value ?? ['delivery', 'pickup'])
  applicable_order_type?: string[];
}

class ItemVariant {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  type: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  option: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  price: number;
}

class OrderAddOnItem {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  option: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  type: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  price: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  quantity: number | null;
}

class OrderItemAddOn {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ type: [OrderAddOnItem], isArray: true })
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => OrderAddOnItem)
  items: OrderAddOnItem[];
}

class OrderItem {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  type: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  price: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  quantity: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  notes: string | null;

  @ApiProperty({ type: [ItemVariant], isArray: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ItemVariant)
  variants: ItemVariant[] | null;

  @ApiProperty({ type: [OrderItemAddOn], isArray: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => OrderItemAddOn)
  add_ons: OrderItemAddOn[] | null;

  @ApiProperty({ type: [OrderItemAddOn], isArray: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => OrderItemAddOn)
  combos: OrderItemAddOn[] | null;
}

class BillAmount {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  sub_total: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  delivery_charge: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  previous_bill: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  discount_amount: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  total_bill: number;
}

export class Address {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  google_address: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  longitude: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  latitude: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  building: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  sector: string;

  @ApiProperty()
  @IsOptional()
  landmark: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  recipient_name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  recipient_contact: string;
}

export class CreateOrderDto {
  @ApiProperty()
  @IsOptional()
  order_type?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  customer_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  shop_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  branch_id: string;

  @ApiProperty()
  @IsOptional()
  @IsUUID()
  zone_id: string;

  @ApiProperty({ type: [OrderItem], isArray: true })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OrderItem)
  items: OrderItem[];

  @ApiProperty()
  @IsOptional()
  @IsString()
  promo_code: string | null;

  @ApiProperty()
  @IsOptional()
  @IsString()
  discount_id: string | null;

  @ApiProperty()
  @IsOptional()
  @IsString()
  additional_notes: string | null;

  @ApiProperty({ type: BillAmount })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => BillAmount)
  bill_amount: BillAmount;

  @ApiProperty({ type: Address })
  @IsOptional()
  @ValidateNested()
  @Type(() => Address)
  address: Address;

  @ApiProperty({ type: 'string', enum: ['cash', 'card', 'online'] })
  @IsNotEmpty()
  @IsString()
  @IsIn(['cash', 'card', 'online'])
  payment_method: string;
}

class Timing {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  start: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  end: string;
}

class CategoryTiming extends Timing {
  day: number | null;
}

class Coordinate {
  @ApiProperty()
  @IsNotEmpty()
  longitude: number;

  @ApiProperty()
  @IsNotEmpty()
  latitude: number;
}

export class UpdateDeliveryZoneDto {
  @ApiProperty({ type: [Coordinate] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Coordinate)
  coordinates: Coordinate[];

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  zone_name: string;

  @ApiProperty()
  @IsNotEmpty()
  delivery_fee: number;

  @ApiProperty()
  @IsOptional()
  min_cart_amount?: number;
}

export class CreateDeliveryZoneDto extends UpdateDeliveryZoneDto {
  @ApiProperty()
  @IsNotEmpty()
  branch_id: string;

  @ApiProperty()
  @IsNotEmpty()
  shop_id: string;
}

export class EmailDto {
  @ApiProperty({ description: 'Sender email address' })
  from: string;

  @ApiProperty({ description: 'Sender email password' })
  password: string;

  @ApiProperty({ description: 'Recipient email addresses' })
  to: string[];

  @ApiProperty({ description: 'Email subject' })
  subject: string;

  @ApiProperty({ description: 'Email text body' })
  text: string;

  @ApiProperty({ description: 'Secret' })
  secret: string;
}

export class CancelOrders {
  @ApiProperty()
  @IsUUID(4, { each: true })
  @IsNotEmpty()
  order_ids: string[];

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  cancelled_by: string;
  @ApiProperty()
  @IsBoolean()
  @IsNotEmpty()
  refund: boolean;
}

export class DeliveryDriversDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  number: string;
}

export class UpdateDriverDto extends DeliveryDriversDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  driverId: string;
}

export class CreateQuotationOrderDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  customer_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  shop_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  branch_id: string;

  @ApiProperty()
  @IsOptional()
  @IsUUID()
  zone_id: string;

  @ApiProperty({ type: [OrderItem], isArray: true })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OrderItem)
  items: OrderItem[];

  @ApiProperty()
  @IsOptional()
  @IsString()
  additional_notes: string | null;

  @ApiProperty({ type: BillAmount })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => BillAmount)
  bill_amount: BillAmount;

  @ApiProperty({ type: Address })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => Address)
  address: Address;
}
export class UpdateItemOrderingDto {
  @ApiProperty({
    type: [String],
    description: 'Array of item IDs in desired order',
  })
  @IsArray()
  @IsUUID('4', { each: true })
  item_ids: string[];
}

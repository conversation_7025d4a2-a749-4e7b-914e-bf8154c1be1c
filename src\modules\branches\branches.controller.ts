import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  InternalServerErrorException,
  NotFoundException,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { validateOrReject } from 'class-validator';
import { JwtAuthGuard } from '../../common/auth/guards/jwt-auth.guard';
import { Public } from '../../common/decorators/public.decorator';
import { BranchesService } from './branches.service';
import {
  AddOnGroup,
  BranchTimings,
  BreakTimings,
  CancelOrders,
  Combo,
  CreateAddOnDto,
  CreateBranchDto,
  CreateBulkItemDto,
  CreateCategoryDto,
  CreateDeliveryZoneDto,
  CreateDiscountDto,
  CreateItemDto,
  CreateOrderDto,
  CreateQuotationOrderDto,
  DeliveryDriversDto,
  UpdateBranchDetailsDto,
  UpdateDeliveryZoneDto,
  UpdateDriverDto,
  UpdateItemOrderingDto,
  UpdatePromotionalBannersDto,
  Variant,
} from './dto/branches.dto';
import { ToggleBranchOptionDto } from './dto/toggle-branch-option.dto';

@ApiTags('Branches')
@Controller('branches')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('access-token')
export class BranchesController {
  constructor(private readonly branchesService: BranchesService) {}

  @Post()
  @ApiOperation({ summary: 'Add a new branch' })
  @ApiResponse({ status: 201, description: 'Branch added successfully' })
  @ApiBadRequestResponse({ description: 'Invalid branch data' })
  @ApiBody({ type: CreateBranchDto })
  async addBranch(@Body() branchData: CreateBranchDto): Promise<void> {
    try {
      await this.branchesService.addBranch(branchData);
    } catch (error) {
      throw new Error('Failed to add branch');
    }
  }

  @Post('add-item')
  @ApiOperation({ summary: 'Add a new item' })
  @ApiResponse({ status: 201, description: 'Item added successfully' })
  @ApiBadRequestResponse({ description: 'Invalid item data' })
  @ApiBody({ type: CreateItemDto })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FilesInterceptor('item_images', 6))
  async addItem(
    @Body() itemData: CreateItemDto,
    @UploadedFiles() item_images: any[],
  ): Promise<void> {
    try {
      const { item_variants, item_add_ons_group, item_combos } = itemData;

      if (item_variants) {
        const parsedVariants =
          typeof item_variants === 'string'
            ? JSON.parse(item_variants)
            : item_variants;
        if (Array.isArray(parsedVariants)) {
          const variants = plainToInstance(Variant, parsedVariants);
          for (const variant of variants) {
            await validateOrReject(variant);
          }
          itemData.item_variants = variants;
        } else {
          throw new Error('item_variants should be an array');
        }
      }

      if (item_add_ons_group) {
        const parsedAddOnGroups =
          typeof item_add_ons_group === 'string'
            ? JSON.parse(item_add_ons_group)
            : item_add_ons_group;
        if (Array.isArray(parsedAddOnGroups)) {
          const addOnGroups = plainToInstance(AddOnGroup, parsedAddOnGroups);
          for (const addOnGroup of addOnGroups) {
            await validateOrReject(addOnGroup);
          }
          itemData.item_add_ons_group = addOnGroups;
        } else {
          throw new Error('item_add_ons_group should be an array');
        }
      }

      if (item_combos) {
        const parsedCombos =
          typeof item_combos === 'string'
            ? JSON.parse(item_combos)
            : item_combos;
        if (Array.isArray(parsedCombos)) {
          const combos = plainToInstance(Combo, parsedCombos);
          for (const combo of combos) {
            await validateOrReject(combo);
          }
          itemData.item_combos = combos;
        } else {
          throw new Error('item_combos should be an array');
        }
      }

      itemData.item_description = this.branchesService.normalizeToNull(
        itemData.item_description,
      );
      itemData.item_images = item_images ?? null;
      await this.branchesService.addItem(itemData);
    } catch (error) {
      throw new Error('Failed to add item');
    }
  }

  @Post('edit-item/:itemId')
  @ApiOperation({ summary: 'Edit an existing item' })
  @ApiResponse({ status: 201, description: 'Item updated successfully' })
  @ApiBadRequestResponse({ description: 'Invalid item data' })
  @ApiBody({ type: CreateItemDto })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FilesInterceptor('item_images', 6))
  @ApiParam({ name: 'itemId', description: 'Item ID' })
  async editItem(
    @Param('itemId') itemId: string,
    @Body() itemData: CreateItemDto,
    @UploadedFiles() item_images: any[],
  ): Promise<void> {
    try {
      const { item_variants, item_add_ons_group, item_combos } = itemData;

      if (item_variants) {
        const parsedVariants =
          typeof item_variants === 'string'
            ? JSON.parse(item_variants)
            : item_variants;
        if (Array.isArray(parsedVariants)) {
          const variants = plainToInstance(Variant, parsedVariants);
          for (const variant of variants) {
            try {
              await validateOrReject(variant, {
                skipMissingProperties: true,
              });
            } catch (validationErrors) {
              throw validationErrors;
            }
          }
          itemData.item_variants = variants;
        } else {
          throw new Error('item_variants should be an array');
        }
      }

      if (item_add_ons_group) {
        const parsedAddOnGroups =
          typeof item_add_ons_group === 'string'
            ? JSON.parse(item_add_ons_group)
            : item_add_ons_group;
        if (Array.isArray(parsedAddOnGroups)) {
          const addOnGroups = plainToInstance(AddOnGroup, parsedAddOnGroups);
          for (const addOnGroup of addOnGroups) {
            try {
              await validateOrReject(addOnGroup, {
                skipMissingProperties: true,
              });
            } catch (validationErrors) {
              throw validationErrors;
            }
          }
          itemData.item_add_ons_group = addOnGroups;
        } else {
          throw new Error('item_add_ons_group should be an array');
        }
      }

      if (item_combos) {
        const parsedCombos =
          typeof item_combos === 'string'
            ? JSON.parse(item_combos)
            : item_combos;
        if (Array.isArray(parsedCombos)) {
          const combos = plainToInstance(Combo, parsedCombos);
          for (const combo of combos) {
            try {
              await validateOrReject(combo, {
                skipMissingProperties: true,
              });
            } catch (validationErrors) {
              throw validationErrors;
            }
          }
          itemData.item_combos = combos;
        } else {
          throw new Error('item_combos should be an array');
        }
      }

      itemData.item_description = this.branchesService.normalizeToNull(
        itemData.item_description,
      );
      itemData.item_images = item_images ?? null;
      await this.branchesService.editItem(itemId, itemData);
    } catch (error) {
      console.log('edit item error', error);
      throw new Error('Failed to update item');
    }
  }

  @Post('add-bulk-items')
  @ApiOperation({ summary: 'Add multiple items' })
  @ApiResponse({ status: 201, description: 'Items added successfully' })
  @ApiBadRequestResponse({ description: 'Invalid item data' })
  @ApiBody({
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          item_name: { type: 'string' },
          item_description: { type: 'string' },
          item_type: { type: 'string' },
          category: { type: 'string' },
          item_price: { type: 'number' },
          item_image_link: { type: 'string' },
          item_quantity: { type: 'number' },
        },
      },
    },
  })
  async addItems(
    @Query('branchId') branchId: string,
    @Body() itemsData: CreateBulkItemDto[],
  ): Promise<void> {
    try {
      await this.branchesService.addItemsWithCategories(itemsData, branchId);
    } catch (error) {
      throw new Error('Failed to add items');
    }
  }

  @Delete('items/:itemId')
  @ApiOperation({ summary: 'Delete item by item ID' })
  @ApiResponse({
    status: 200,
    description: 'Item deleted successfully',
  })
  @ApiParam({ name: 'itemId', description: 'Item ID' })
  @ApiNotFoundResponse({ description: 'Item not found' })
  async deleteItem(@Param('itemId') itemId: string): Promise<void> {
    try {
      await this.branchesService.deleteItem(itemId);
    } catch (error) {
      throw new Error('Failed to delete item');
    }
  }

  @Post('quantities/:branchId')
  @ApiOperation({ summary: 'Get item quantities by item IDs and branch ID' })
  @ApiParam({
    name: 'branchId',
    description: 'Branch ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns item quantities for the given item IDs and branch ID',
  })
  async getItemQuantities(
    @Param('branchId') branchId: string,
    @Body() itemIds: string[],
  ): Promise<any[]> {
    return this.branchesService.getItemQuantities(branchId, itemIds);
  }

  @Patch('items/:itemId/status')
  @ApiOperation({ summary: 'Update item status' })
  @ApiResponse({
    status: 200,
    description: 'Item status updated successfully',
  })
  @ApiParam({ name: 'itemId', description: 'Item ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        status: {
          type: 'string',
        },
        timestamp: {
          type: 'number',
        },
      },
      required: ['status'],
    },
  })
  async updateItemStatus(
    @Param('itemId') itemId: string,
    @Body() body: { status: string; timestamp?: number | null },
  ): Promise<void> {
    try {
      await this.branchesService.updateItemStatus(itemId, body);
    } catch (error) {
      throw new Error('Failed to update item status');
    }
  }

  @Get('category/details/:categoryId')
  @ApiOperation({ summary: 'Get category details' })
  @ApiResponse({
    status: 200,
    description: 'Category details fetched successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid category data' })
  @ApiParam({ name: 'categoryId', description: 'Category ID' })
  async getCategoryById(@Param('categoryId') categoryId: string): Promise<any> {
    try {
      const category = await this.branchesService.getCategoryById(categoryId);
      return category;
    } catch (error) {
      throw new Error('Failed to fetch category');
    }
  }

  @Post('add-category')
  @ApiOperation({ summary: 'Add a new category' })
  @ApiResponse({ status: 201, description: 'Category added successfully' })
  @ApiBadRequestResponse({ description: 'Invalid category data' })
  @ApiBody({ type: CreateCategoryDto })
  async addCategory(@Body() categoryData: CreateCategoryDto): Promise<void> {
    try {
      await this.branchesService.addCategory(categoryData);
    } catch (error) {
      throw new Error('Failed to add category');
    }
  }

  @Put('edit-category/:categoryId')
  @ApiOperation({ summary: 'Edit an existing category' })
  @ApiResponse({ status: 200, description: 'Category updated successfully' })
  @ApiBadRequestResponse({ description: 'Invalid category data' })
  @ApiBody({ type: CreateCategoryDto })
  @ApiParam({ name: 'categoryId', description: 'Category ID' })
  async editCategory(
    @Param('categoryId') categoryId: string,
    @Body() categoryData: CreateCategoryDto,
  ): Promise<void> {
    try {
      await this.branchesService.editCategory(categoryId, categoryData);
    } catch (error) {
      throw new Error('Failed to update category');
    }
  }

  @Delete('delete-category/:categoryId')
  @ApiOperation({ summary: 'Delete a category' })
  @ApiResponse({ status: 200, description: 'Category deleted successfully' })
  @ApiBadRequestResponse({
    description: 'Invalid request, delete items linked to the category',
  })
  @ApiParam({ name: 'categoryId', description: 'Category ID' })
  async deleteCategory(@Param('categoryId') categoryId: string): Promise<void> {
    await this.branchesService.deleteCategory(categoryId);
  }

  @Post('add-addon')
  @ApiOperation({ summary: 'Add a new addon' })
  @ApiResponse({ status: 201, description: 'Addon added successfully' })
  @ApiBadRequestResponse({ description: 'Invalid addon data' })
  @ApiBody({ type: CreateAddOnDto })
  async addAddon(@Body() addonData: CreateAddOnDto): Promise<void> {
    try {
      await this.branchesService.addAddOn(addonData);
    } catch (error) {
      throw new Error('Failed to add addon');
    }
  }

  @Post('add-discount')
  @ApiOperation({ summary: 'Add a new discount' })
  @ApiResponse({ status: 201, description: 'Discount added successfully' })
  @ApiBadRequestResponse({ description: 'Invalid discount data' })
  @ApiBody({ type: CreateDiscountDto })
  async addDiscount(@Body() discountData: CreateDiscountDto): Promise<void> {
    try {
      await this.branchesService.addDiscount(discountData);
    } catch (error) {
      throw new Error('Failed to add discount');
    }
  }

  @Post('add-order')
  @ApiOperation({ summary: 'Add a new order' })
  @ApiResponse({ status: 201, description: 'Order added successfully' })
  @ApiBadRequestResponse({ description: 'Invalid order data' })
  @ApiBody({ type: CreateOrderDto })
  async addOrder(@Body() orderData: CreateOrderDto): Promise<any> {
    const order = await this.branchesService.addOrder(orderData);
    return order;
  }

  @Patch('categories/:categoryId/status')
  @ApiOperation({ summary: 'Update category status' })
  @ApiResponse({
    status: 200,
    description: 'Category status updated successfully',
  })
  @ApiParam({ name: 'categoryId', description: 'Category ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        status: {
          type: 'boolean',
        },
      },
      required: ['status'],
    },
  })
  async updateCategoryStatus(
    @Param('categoryId') categoryId: string,
    @Body() body: { status: boolean },
  ): Promise<void> {
    try {
      const { status } = body;
      await this.branchesService.updateCategoryStatus(categoryId, status);
    } catch (error) {
      throw new Error('Failed to update category status');
    }
  }

  @Get(':branchId/details')
  @ApiOperation({ summary: 'Get branch details by branch ID' })
  @ApiResponse({
    status: 200,
    description: 'Branch details retrieved successfully',
  })
  @ApiParam({ name: 'branchId', description: 'Branch ID' })
  async getBranchDetails(@Param('branchId') branchId: string): Promise<any[]> {
    try {
      const response = await this.branchesService.getBranchDetails(branchId);
      return response[0];
    } catch (error) {
      throw new Error('Failed to get branch details');
    }
  }

  @Post('update-branch-details/:branchId')
  @ApiOperation({ summary: 'Update branch details' })
  @ApiResponse({
    status: 200,
    description: 'Branch details updated successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid branch data' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('branch_logo'))
  async updateBranchDetails(
    @Param('branchId') branchId: string,
    @Body() branchData: UpdateBranchDetailsDto,
    @UploadedFile() branch_logo: any,
  ): Promise<void> {
    try {
      if (branch_logo) {
        branchData.branch_logo = branch_logo;
      }
      await this.branchesService.updateBranchDetails(branchId, branchData);
    } catch (error) {
      throw new Error('Failed to update branch details');
    }
  }

  @Post('update-promotional-banners/:branchId')
  @ApiOperation({ summary: 'Update promotional banners' })
  @ApiResponse({
    status: 200,
    description: 'Promotional banners updated successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid banner data' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FilesInterceptor('branch_promotional_banners', 5))
  async updatePromotionalBanners(
    @Param('branchId') branchId: string,
    @Body() bannerData: UpdatePromotionalBannersDto,
    @UploadedFiles() branch_promotional_banners: any[],
  ): Promise<void> {
    try {
      if (branch_promotional_banners && branch_promotional_banners.length > 0) {
        bannerData.branch_promotional_banners = branch_promotional_banners;
      }
      await this.branchesService.updatePromotionalBanners(branchId, bannerData);
    } catch (error) {
      throw new Error('Failed to update promotional banners');
    }
  }

  @Get('status/:branchId')
  @ApiOperation({ summary: 'Get branch status' })
  @ApiResponse({
    status: 200,
    description: 'Branch status fetched successfully',
  })
  @ApiParam({ name: 'branchId', description: 'Branch ID' })
  @ApiQuery({ name: 'serviceType', description: 'Service Type' })
  async getBranchStatus(
    @Param('branchId') branchId: string,
    @Query('serviceType') serviceType: string,
  ): Promise<any[]> {
    try {
      const status = await this.branchesService.getBranchStatus(
        branchId,
        serviceType,
      );
      return status;
    } catch (error) {
      throw new Error('Failed to get branch status');
    }
  }

  @Patch('status/:branchId')
  @ApiOperation({ summary: 'Update branch status' })
  @ApiResponse({
    status: 200,
    description: 'Branch status updated successfully',
  })
  @ApiParam({ name: 'branchId', description: 'Branch ID' })
  @ApiQuery({ name: 'serviceType', description: 'Service Type' })
  @ApiBody({
    schema: { type: 'object', properties: { status: { type: 'boolean' } } },
  })
  async toggleBranchStatus(
    @Param('branchId') branchId: string,
    @Query('serviceType') serviceType: string,
    @Body('status') status: boolean,
  ): Promise<void> {
    try {
      await this.branchesService.updateBranchStatus(
        branchId,
        status,
        serviceType,
      );
    } catch (error) {
      throw new Error('Failed to update branch status');
    }
  }

  @Get(':branchId/branch-timings')
  @ApiOperation({ summary: 'Get branch timings by branch ID' })
  @ApiParam({
    name: 'branchId',
    description: 'Branch ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns branch timings for a branch',
    type: Object,
  })
  @ApiNotFoundResponse({
    description: 'Branch timings not found for the branch',
  })
  async getBranchTimings(@Param('branchId') branchId: string): Promise<any[]> {
    const branchTimings = await this.branchesService.getBranchTimings(branchId);
    if (!branchTimings) {
      throw new NotFoundException('Branch timings not found for the branch');
    }
    return branchTimings;
  }

  @Get(':branchId/break-timings')
  @ApiOperation({ summary: 'Get break timings by branch ID' })
  @ApiParam({
    name: 'branchId',
    description: 'Branch ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns break timings for a branch',
    type: Object,
  })
  @ApiNotFoundResponse({
    description: 'Break timings not found for the branch',
  })
  async getBreakTimings(@Param('branchId') branchId: string): Promise<any[]> {
    const breakTimings = await this.branchesService.getBreakTimings(branchId);
    return breakTimings;
  }

  @Post(':branchId/branch-timings')
  @ApiOperation({
    summary: 'Update branch timings by branch ID',
  })
  @ApiParam({
    name: 'branchId',
    type: 'string',
  })
  @ApiBody({ type: [BranchTimings] })
  @ApiResponse({
    status: 201,
    description: 'Successful updated the branch timings',
  })
  async updateBranchTimings(
    @Param('branchId') branchId: string,
    @Body() branchTimings: BranchTimings[],
  ): Promise<void> {
    try {
      await this.branchesService.updateBranchTimings(branchId, branchTimings);
    } catch (error) {
      throw new Error('Failed to update branch timings');
    }
  }

  @Post(':branchId/break-timings')
  @ApiOperation({
    summary: 'Update break timings by branch ID',
  })
  @ApiParam({
    name: 'branchId',
    type: 'string',
  })
  @ApiBody({ type: [BreakTimings] })
  @ApiResponse({
    status: 201,
    description: 'Successful updated the break timings',
  })
  async updateBreakTimings(
    @Param('branchId') branchId: string,
    @Body() breakTimings: BreakTimings[],
  ): Promise<void> {
    try {
      await this.branchesService.updateBreakTimings(branchId, breakTimings);
    } catch (error) {
      throw new Error('Failed to update branch timings');
    }
  }

  @Get('items/:branchId')
  @ApiOperation({ summary: 'Get all items for add-ons by branch ID' })
  @ApiParam({
    name: 'branchId',
    description: 'Branch ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns items for a branch',
    type: Object,
  })
  @ApiNotFoundResponse({ description: 'Items not found' })
  async getBranchItems(@Param('branchId') branchId: string): Promise<any[]> {
    const items = await this.branchesService.getAllItems(branchId);
    if (!items) {
      throw new NotFoundException('Items not found');
    }
    return items;
  }

  @Patch('items/:item_id')
  @ApiOperation({ summary: 'Update item availability' })
  @ApiResponse({
    status: 200,
    description: 'Item availability updated successfully',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        availability: {
          type: 'string',
        },
      },
      required: ['availability'],
    },
  })
  async updateItemAvailability(
    @Param('item_id') itemId: string,
    @Body() body: { availability: string },
  ): Promise<void> {
    try {
      const { availability } = body;
      await this.branchesService.updateItemAvailability(itemId, availability);
    } catch (error) {
      throw new Error('Failed to update item availability');
    }
  }

  @Get('discounts/:shopId')
  @ApiOperation({ summary: 'Get discounts by shop ID' })
  @ApiParam({
    name: 'shopId',
    description: 'Shop ID',
  })
  @ApiQuery({
    name: 'branchIds',
    description: 'Branch IDs (comma-separated)',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns discounts for the shop',
    type: Object,
    isArray: true,
  })
  @ApiNotFoundResponse({ description: 'No discounts found for the shop' })
  async getDiscountsByShopId(
    @Param('shopId') shopId: string,
    @Query('branchIds') branchIds?: string,
  ): Promise<any[]> {
    try {
      const discounts = await this.branchesService.getAllDiscounts(
        shopId,
        branchIds ? branchIds.split(',') : null,
      );
      return discounts;
    } catch (error) {
      throw new Error('Failed to fetch discounts');
    }
  }

  @Patch('discounts/:discountId/status')
  @ApiOperation({ summary: 'Update discount status' })
  @ApiResponse({
    status: 200,
    description: 'Discount status updated successfully',
  })
  @ApiParam({ name: 'discountId', description: 'Discount ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        status: {
          type: 'boolean',
        },
      },
      required: ['status'],
    },
  })
  async updateDiscountStatus(
    @Param('discountId') discountId: string,
    @Body() body: { status: boolean },
  ): Promise<void> {
    try {
      const { status } = body;
      await this.branchesService.updateDiscountStatus(discountId, status);
    } catch (error) {
      throw error;
    }
  }

  @Get('add-ons/:branchId')
  @ApiOperation({ summary: 'Get add-ons by branch ID' })
  @ApiParam({
    name: 'branchId',
    description: 'Branch ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns addons for a branch',
    type: Object,
  })
  @ApiNotFoundResponse({ description: 'Addons not found' })
  async getBranchAddOns(@Param('branchId') branchId: string): Promise<any[]> {
    const addOns = await this.branchesService.getAllAddOns(branchId);
    if (!addOns) {
      throw new NotFoundException('Category not found');
    }
    return addOns;
  }

  @Put('add-ons/:branchId/:addOnId')
  @ApiOperation({ summary: 'Edit an add-on' })
  @ApiResponse({
    status: 200,
    description: 'Add-on updated successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid request' })
  @ApiParam({ name: 'branchId', description: 'Branch ID' })
  @ApiParam({ name: 'addOnId', description: 'Add-on ID' })
  @ApiBody({
    description: 'Data to update the add-on',
    type: Object,
    schema: {
      type: 'object',
      properties: {
        add_on_name: { type: 'string' },
        add_on_type: { type: 'string' },
        add_on_price: { type: 'number' },
      },
      required: ['add_on_name', 'add_on_type', 'add_on_price'],
    },
  })
  async editAddOn(
    @Param('branchId') branchId: string,
    @Param('addOnId') addOnId: string,
    @Body()
    updatedData: {
      add_on_name: string;
      add_on_type: string;
      add_on_price: number;
    },
  ): Promise<void> {
    try {
      await this.branchesService.editAddOn(branchId, addOnId, updatedData);
    } catch (error) {
      throw new Error('Failed to update add-on');
    }
  }

  @Delete('add-ons/:branchId/:addOnId')
  @ApiOperation({ summary: 'Delete an add-on' })
  @ApiResponse({
    status: 200,
    description: 'Add-on deleted successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid request' })
  @ApiParam({ name: 'branchId', description: 'Branch ID' })
  @ApiParam({ name: 'addOnId', description: 'Add-on ID' })
  async deleteAddOn(
    @Param('branchId') branchId: string,
    @Param('addOnId') addOnId: string,
  ): Promise<{
    items: Array<{ item_id: string; name: string }>;
    isDeleted: boolean;
  }> {
    try {
      const response = await this.branchesService.deleteAddOn(
        branchId,
        addOnId,
      );
      return response;
    } catch (error) {
      throw new Error('Failed to delete add-on');
    }
  }

  @Get('orders/:orderId/status')
  @ApiOperation({ summary: 'Get order status' })
  @ApiResponse({
    status: 200,
    description: 'Order status fetched successfully',
  })
  @ApiParam({ name: 'orderId', description: 'Order ID' })
  async getOrderStatus(
    @Param('orderId') orderId: string,
  ): Promise<{ status: string }> {
    try {
      const status = await this.branchesService.getOrderStatus(orderId);
      return { status };
    } catch (error) {
      throw new Error('Failed to get order status');
    }
  }

  @Public()
  @Get('orders/:orderId/details')
  @ApiOperation({ summary: 'Get order details for customer order summary' })
  @ApiResponse({
    status: 200,
    description: 'Order status fetched successfully',
  })
  @ApiParam({ name: 'orderId', description: 'Order ID' })
  async getOrderDetails(@Param('orderId') orderId: string): Promise<any[]> {
    try {
      const orderDetails = await this.branchesService.getOrderDetails(orderId);
      return orderDetails;
    } catch (error) {
      throw new Error('Failed to get order details for customer');
    }
  }

  @Patch('orders/:orderId/status')
  @ApiOperation({ summary: 'Update order status' })
  @ApiResponse({
    status: 200,
    description: 'Order status updated successfully',
  })
  @ApiParam({ name: 'orderId', description: 'Order ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        status: {
          type: 'string',
        },
        rejected_reason: {
          type: 'string',
        },
        id: {
          type: 'string',
        },
        name: {
          type: 'string',
        },
        number: {
          type: 'string',
        },
      },
      required: ['status'],
    },
  })
  async updateOrderStatus(
    @Param('orderId') orderId: string,
    @Body()
    body: {
      status: string;
      rejected_reason?: string;
      id?: string;
      name?: string;
      number?: string;
    },
  ): Promise<string> {
    try {
      const response = await this.branchesService.updateOrderStatus(
        orderId,
        body,
      );
      return response;
    } catch (error) {
      throw new Error('Failed to update order status');
    }
  }

  @Get('delivery-zones/:branchId')
  @ApiOperation({ summary: 'Get all delivery zones by branch ID' })
  @ApiResponse({
    status: 200,
    description: 'Delivery zones retrieved successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid request' })
  @ApiParam({ name: 'branchId', description: 'Branch ID' })
  async getZonesByBranchId(
    @Param('branchId') branchId: string,
  ): Promise<any[]> {
    try {
      return await this.branchesService.getZonesByBranchId(branchId);
    } catch (error) {
      throw new Error('Failed to get delivery zones');
    }
  }

  @Post('delivery-zones')
  @ApiOperation({ summary: 'Add a new delivery zone' })
  @ApiResponse({ status: 201, description: 'Delivery zone added successfully' })
  @ApiBadRequestResponse({ description: 'Invalid delivery zone data' })
  @ApiBody({ type: CreateDeliveryZoneDto })
  async addDeliveryZone(
    @Body() zoneData: CreateDeliveryZoneDto,
  ): Promise<void> {
    try {
      await this.branchesService.addDeliveryZone(zoneData);
    } catch (error) {
      throw new Error('Failed to add delivery zone');
    }
  }

  @Put('delivery-zones/:zoneId')
  @ApiOperation({ summary: 'Update a delivery zone' })
  @ApiResponse({
    status: 200,
    description: 'Delivery zone updated successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid delivery zone data' })
  @ApiParam({ name: 'zoneId', description: 'Zone ID' })
  @ApiBody({ type: UpdateDeliveryZoneDto })
  async updateDeliveryZone(
    @Param('zoneId') zoneId: string,
    @Body() zoneData: UpdateDeliveryZoneDto,
  ): Promise<void> {
    try {
      await this.branchesService.updateDeliveryZone(zoneId, zoneData);
    } catch (error) {
      throw new Error('Failed to update delivery zone');
    }
  }

  @Patch('delivery-zones/:zoneId')
  @ApiOperation({ summary: 'Update delivery zone status' })
  @ApiResponse({
    status: 200,
    description: 'Delivery zone status updated successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid request' })
  @ApiParam({ name: 'zoneId', description: 'Zone ID' })
  @ApiBody({
    schema: { type: 'object', properties: { status: { type: 'boolean' } } },
  })
  async updateZoneStatus(
    @Param('zoneId') zoneId: string,
    @Body('status') status: boolean,
  ): Promise<void> {
    try {
      await this.branchesService.updateZoneStatus(zoneId, status);
    } catch (error) {
      throw new Error('Failed to update delivery zone status');
    }
  }

  @Delete('delivery-zones/:zoneId')
  @ApiOperation({ summary: 'Delete a delivery zone' })
  @ApiResponse({
    status: 200,
    description: 'Delivery zone deleted successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid request' })
  @ApiParam({ name: 'zoneId', description: 'Zone ID' })
  async deleteZoneById(@Param('zoneId') zoneId: string): Promise<void> {
    try {
      await this.branchesService.deleteZoneById(zoneId);
    } catch (error) {
      throw new Error('Failed to delete delivery zone');
    }
  }

  @Get('orders/:shop_id/:branch_id/active')
  @ApiOperation({ summary: 'Get active orders' })
  @ApiResponse({ status: 200, description: 'Orders retrieved successfully' })
  @ApiQuery({
    name: 'order_type',
    enum: ['new', 'accepted'],
  })
  async getActiveOrders(
    @Param('shop_id') shopId: string,
    @Param('branch_id') branchId: string,
    @Query('order_type') orderType: 'new' | 'accepted',
  ): Promise<any[]> {
    try {
      return await this.branchesService.getActiveOrders(
        shopId,
        branchId,
        orderType,
      );
    } catch (error) {
      throw new Error('Failed to retrieve orders');
    }
  }

  @Public()
  @Get('category/:branchId')
  @ApiOperation({ summary: 'Get category order by branch ID' })
  @ApiParam({
    name: 'branchId',
    description: 'Branch ID',
  })
  @ApiQuery({
    name: 'hideInvalidCategory',
    description: 'Hide invalid categories',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns category in order',
    type: Object,
  })
  @ApiNotFoundResponse({ description: 'Category not found' })
  async getCategoryByBranchId(
    @Param('branchId') branchId: string,
    @Query('hideInvalidCategory') hideInvalidCategory?: boolean,
  ): Promise<any[]> {
    const category = await this.branchesService.findCategoryOrderByBranchId(
      branchId,
      hideInvalidCategory,
    );
    if (!category) {
      throw new NotFoundException('Category not found');
    }
    return category;
  }

  @Patch('category/:branchId')
  @ApiOperation({ summary: 'Update category order by branch ID' })
  @ApiParam({
    name: 'branchId',
    description: 'Branch ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Category order updated successfully',
  })
  async updateCategoryOrder(
    @Param('branchId') branchId: string,
    @Body() categoryOrder: string[],
  ): Promise<void> {
    await this.branchesService.updateCategoryOrderByBranchId(
      branchId,
      categoryOrder,
    );
  }

  @Get(':branchId/menu')
  @ApiOperation({ summary: 'Get branch menu by branch ID' })
  @ApiResponse({
    status: 200,
    description: 'Branch menu retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Branch not found' })
  async getBranchMenuData(@Param('branchId') branchId: string): Promise<any> {
    try {
      const branchData =
        await this.branchesService.getMenuItemsForBranch(branchId);
      if (!branchData) {
        throw new Error('Branch not found');
      }
      return branchData;
    } catch (error) {
      throw new Error('Failed to fetch branch data');
    }
  }

  @Public()
  @Get(':shopId/:branchId/menu')
  @ApiOperation({
    summary: 'Get customer branch menu by shop and branch ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Branch menu retrieved successfully',
  })
  @ApiQuery({ name: 'customerId', required: false })
  @ApiResponse({ status: 404, description: 'Branch not found' })
  async getCustomerBranchMenuData(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Query('date') date: string,
    @Query('customerId') customerId?: string,
  ): Promise<any> {
    try {
      const branchData = await this.branchesService.getCustomerBranchMenuData(
        shopId,
        branchId,
        customerId,
        date,
      );
      if (!branchData) {
        throw new Error('Branch not found');
      }
      return branchData;
    } catch (error) {
      throw new Error('Failed to fetch branch menu data');
    }
  }

  @Get('payment-modes/:branchId')
  @ApiOperation({ summary: 'Get payment modes for a branch' })
  @ApiParam({ name: 'branchId', description: 'Branch ID' })
  @ApiResponse({
    status: 200,
    description: 'Payment modes fetched successfully',
  })
  @ApiNotFoundResponse({ description: 'Branch payment modes not found' })
  async getBranchPaymentModes(
    @Param('branchId') branchId: string,
  ): Promise<any> {
    try {
      const paymentModes =
        await this.branchesService.getBranchPaymentModes(branchId);
      return paymentModes;
    } catch (error) {
      throw new NotFoundException('Branch payment modes not found');
    }
  }

  @Public()
  @Get(':branchId/:itemId')
  @ApiOperation({ summary: 'Get item details for an item' })
  @ApiParam({ name: 'branchId', description: 'Branch ID' })
  @ApiParam({ name: 'itemId', description: 'Item ID' })
  @ApiResponse({
    status: 200,
    description: 'Item details fetched successfully',
  })
  @ApiNotFoundResponse({ description: 'Item not found' })
  async getItemDetails(
    @Param('branchId') branchId: string,
    @Param('itemId') itemId: string,
  ): Promise<any> {
    try {
      const item = await this.branchesService.getItemDetails(branchId, itemId);
      return item;
    } catch (error) {
      throw new NotFoundException('Item not found');
    }
  }

  @Get('delivery-drivers')
  @ApiOperation({ summary: 'Get delivery drivers' })
  @ApiQuery({ name: 'shopId', description: 'Shop ID', required: true })
  @ApiQuery({
    name: 'dateOption',
    type: String,
    enum: ['today', 'last_seven_days', 'last_thirty_days', 'last_ninety_days'],
    required: true,
    description:
      'Date filter option: today, last_seven_days, last_thirty_days, last_ninety_days',
  })
  @ApiQuery({ name: 'branchId', description: 'Branch ID', required: false })
  @ApiQuery({ name: 'timeZone', description: 'Time zone', required: false })
  @ApiResponse({
    status: 200,
    description: 'Delivery driver details fetched successfully',
  })
  @ApiNotFoundResponse({ description: 'Drivers not found' })
  async getDeliveryDrivers(
    @Query('shopId') shopId: string,
    @Query('dateOption') dateOption: string,
    @Query('branchId') branchId: string,
    @Query('timeZone') timeZone?: string,
  ): Promise<any> {
    try {
      const drivers = await this.branchesService.getDeliveryDrivers(
        shopId,
        branchId,
        dateOption,
        timeZone,
      );
      return drivers;
    } catch (error) {
      throw new Error('Drivers not found');
    }
  }

  @Get('delivery-drivers/:branchId/:driverId/orders')
  @ApiOperation({
    summary: 'Get orders delivered by delivery driver by Driver ID',
  })
  @ApiParam({
    name: 'branchId',
    description: 'Branch ID',
  })
  @ApiParam({
    name: 'driverId',
    description: 'Driver ID',
  })
  @ApiQuery({
    name: 'dateOption',
    type: String,
    enum: ['today', 'last_seven_days', 'last_thirty_days', 'last_ninety_days'],
    required: true,
    description:
      'Date filter option: today, last_seven_days, last_thirty_days, last_ninety_days',
  })
  @ApiQuery({ name: 'timeZone', description: 'Time zone', required: false })
  @ApiResponse({
    status: 200,
    description:
      'Order delivered by delivery driver details fetched successfully',
  })
  @ApiNotFoundResponse({ description: 'Driver not found' })
  async getOrderDeliveredByDeliveryDriver(
    @Param('branchId') branchId: string,
    @Param('driverId') driverId: string,
    @Query('dateOption') dateOption: string,
    @Query('timeZone') timeZone?: string,
  ): Promise<any> {
    try {
      const orders =
        await this.branchesService.getOrderDeliveredByDeliveryDriver(
          branchId,
          driverId,
          dateOption,
          timeZone,
        );
      return orders;
    } catch (error) {
      throw new Error('Driver not found');
    }
  }

  @Post('delivery-drivers')
  @ApiOperation({ summary: 'Add new delivery drivers' })
  @ApiResponse({
    status: 201,
    description: 'Delivery drivers added successfully',
  })
  @ApiQuery({ name: 'branchId', description: 'Branch ID' })
  @ApiBody({
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          name: { type: 'string' },
          number: { type: 'string' },
        },
      },
    },
  })
  async addDeliveryDrivers(
    @Query('branchId') branchId: string,
    @Body() driverData: DeliveryDriversDto[],
  ): Promise<void> {
    try {
      await this.branchesService.addDeliveryDrivers(driverData, branchId);
    } catch (error) {
      throw new Error('Failed to add delivery drivers');
    }
  }

  @Patch('delivery-driver')
  @ApiOperation({ summary: 'Edit delivery driver details by driver ID' })
  @ApiBody({
    description: 'Driver details to be updated',
    type: UpdateDriverDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Delivery driver details updated successfully',
  })
  @ApiNotFoundResponse({ description: 'Driver not found' })
  async updateDriverDetails(
    @Body() updateData: UpdateDriverDto,
  ): Promise<void> {
    const { driverId, name, number } = updateData;
    try {
      await this.branchesService.updateDriverDetails(driverId, name, number);
    } catch (error) {
      throw new Error('Failed to update driver details');
    }
  }

  @Delete('delivery-driver/:driverId')
  @ApiOperation({ summary: 'Delete delivery driver by driver ID' })
  @ApiResponse({
    status: 200,
    description: 'Delivery driver deleted successfully',
  })
  @ApiParam({ name: 'driverId', description: 'Driver ID' })
  @ApiNotFoundResponse({ description: 'Driver not found' })
  async deleteDeliveryDriver(
    @Param('driverId') driverId: string,
  ): Promise<void> {
    try {
      await this.branchesService.deleteDeliveryDriver(driverId);
    } catch (error) {
      throw new Error('Failed to delete driver details');
    }
  }

  @Get('get-all-flags/:shopId/status')
  @ApiOperation({ summary: 'Get status for inbox and delivery module' })
  @ApiParam({ name: 'shopId', description: 'Shop ID' })
  @ApiQuery({ name: 'branchId', description: 'Branch ID', required: false })
  @ApiResponse({
    status: 200,
    description: 'Inbox and delivery module status fetched successfully',
  })
  @ApiNotFoundResponse({ description: 'Branch not found' })
  async getInboxStatus(
    @Param('shopId') shopId: string,
    @Query('branchId') branchId?: string | null,
  ): Promise<any> {
    try {
      const status = await this.branchesService.getInboxStatus(
        shopId,
        branchId,
      );
      return status;
    } catch (error) {
      throw new NotFoundException(
        'Status for inbox and delivery module failed',
      );
    }
  }

  @Patch('update-inbox-status/:shopId/:branchId/:status')
  @ApiOperation({
    summary: 'Update Inbox status by shopId and branch ID',
  })
  @ApiParam({
    name: 'branchId',
    description: 'Branch ID',
  })
  @ApiParam({ name: 'shopId', description: 'Shop ID' })
  @ApiParam({ name: 'status', description: 'Inbox Status' })
  @ApiResponse({
    status: 200,
    description: 'Inbox status updated successfully',
  })
  async updateInboxStatus(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Param('status') status: boolean,
  ): Promise<void> {
    await this.branchesService.updateInboxStatus(shopId, branchId, status);
  }

  @Post('cancel')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cancel multiple order',
    description: 'Cancel orders based on provided order IDs',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Orders successfully canceled',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input or error in cancelling orders',
  })
  async cancelOrders(@Body() cancelOrders: CancelOrders) {
    try {
      await this.branchesService.cancelOrders(cancelOrders);
    } catch (error) {
      throw new InternalServerErrorException('Error cancelling orders');
    }
  }

  @Get('get-refund-type/:shopId/:branchId')
  @ApiOperation({ summary: 'Get Refund Type' })
  @ApiParam({ name: 'branchId', description: 'Branch ID' })
  @ApiParam({ name: 'shopId', description: 'Shop ID' })
  @ApiResponse({
    status: 200,
    description: 'Refund details fetched successfully',
  })
  @ApiNotFoundResponse({ description: 'Return type data not found' })
  async getRefundType(
    @Param('branchId') branchId: string,
    @Param('shopId') shopId: string,
  ): Promise<any> {
    try {
      const data = await this.branchesService.getRefundType(shopId, branchId);
      return data;
    } catch (error) {
      throw new NotFoundException('Refund type not found');
    }
  }
  @Post('add-quotation-order')
  @ApiOperation({ summary: 'Add a new order' })
  @ApiResponse({ status: 201, description: 'Order added successfully' })
  @ApiBadRequestResponse({ description: 'Invalid order data' })
  @ApiBody({ type: CreateQuotationOrderDto })
  async addQuotationOrder(
    @Body() orderData: CreateQuotationOrderDto,
  ): Promise<any> {
    const order = await this.branchesService.addQuotationOrder(orderData);
    return order;
  }

  @Public()
  @Get('landing-page/profile-page-details/:id')
  @ApiOperation({
    summary: 'Get detailed branch information for profile page by branch ID',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    description: 'The ID of the branch',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successful retrieval of detailed branch information',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'branch not found',
  })
  async getProfilePageDetails(@Param('id') id: string) {
    const details = await this.branchesService.getProfilePageDetails(id);
    if (!details) {
      throw new NotFoundException(`branch with ID ${id} not found`);
    }
    return details;
  }

  @Public()
  @Get('landing-page/get-branch-faqs/:branchId')
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiOperation({ summary: 'Get all FAQs' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Get all FAQs',
  })
  async getFAQs(@Param('branchId') branchId: string) {
    console.log('🚀 ~ BranchesController ~ getFAQs ~ branchId:', branchId);
    try {
      const faqs = await this.branchesService.getFAQs(branchId);
      return faqs;
    } catch (error) {
      throw new NotFoundException(`branch with ID ${branchId} not found`);
    }
  }

  @Get('get-customization-data/:shopId/:type')
  @ApiOperation({ summary: 'Get item details for an item' })
  @ApiParam({ name: 'shopId', description: 'Shop ID' })
  @ApiParam({
    name: 'type',
    description: 'Type of customization data',
    enum: ['variants', 'combos', 'add-ons'],
  })
  @ApiQuery({ name: 'branchId', description: 'Branch ID', required: false })
  @ApiResponse({
    status: 200,
    description: 'Item details fetched successfully',
  })
  @ApiNotFoundResponse({ description: 'Item not found' })
  async getCustomizationData(
    @Param('type') type: string,
    @Param('shopId') shopId: string,
    @Query('branchId') branchId?: string,
  ): Promise<any> {
    try {
      const item = await this.branchesService.getCustomizationData(
        type,
        shopId,
        this.branchesService.normalizeToNull(branchId) || '',
      );
      return item;
    } catch (error) {
      throw new NotFoundException('Item not found');
    }
  }

  @Post('delayed-orders/:branchId')
  @ApiOperation({ summary: 'Get delayed order count by branch ID' })
  @ApiResponse({
    status: 200,
    description: 'Branch delayed orders retrieved successfully',
  })
  @ApiParam({ name: 'branchId', description: 'Branch ID' })
  async getDelayedOrdersCount(
    @Param('branchId') branchId: string,
  ): Promise<any[]> {
    try {
      const response =
        await this.branchesService.getBranchDelayedOrders(branchId);
      return response;
    } catch (error) {
      throw new Error('Failed to get branch delayed order count');
    }
  }

  @Post('update-item-ordering/:branchId')
  @ApiOperation({ summary: 'Update ordering of items by branch ID' })
  @ApiParam({ name: 'branchId', description: 'Branch ID' })
  @ApiBody({ type: UpdateItemOrderingDto })
  @ApiResponse({
    status: 200,
    description: 'Item ordering updated successfully',
  })
  async updateItemOrdering(
    @Param('branchId') branchId: string,
    @Body() body: UpdateItemOrderingDto,
  ) {
    try {
      if (body.item_ids.length === 0) {
        throw new Error('Item IDs are required');
      }
      return await this.branchesService.updateItemOrdering(
        branchId,
        body.item_ids,
      );
    } catch (error) {
      throw new Error(error);
    }
  }

  @Post('copy-menu/:sourceBranchId/:targetBranchId')
  async copyBranchItems(
    @Param('sourceBranchId') sourceBranchId: string,
    @Param('targetBranchId') targetBranchId: string,
  ) {
    return await this.branchesService.copyItemsBetweenBranches(
      sourceBranchId,
      targetBranchId,
    );
  }

  @Patch(':branchId/toggle-option')
  @ApiOperation({ summary: 'Toggle branch delivery or pickup option' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Branch option toggled successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid request data' })
  @ApiNotFoundResponse({ description: 'Branch not found' })
  @ApiParam({ name: 'branchId', description: 'Branch ID' })
  async toggleBranchOption(
    @Param('branchId') branchId: string,
    @Body() toggleData: ToggleBranchOptionDto,
  ) {
    try {
      return await this.branchesService.toggleBranchOption(
        branchId,
        toggleData.option,
        toggleData.status,
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to toggle branch option: ${error.message}`);
    }
  }
}

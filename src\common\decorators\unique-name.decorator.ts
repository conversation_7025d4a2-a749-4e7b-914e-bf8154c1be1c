import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

@ValidatorConstraint({ async: false })
class UniqueNamesConstraint implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    if (!Array.isArray(value)) {
      return false;
    }

    const propertyToCheck = args.constraints[0];

    if (propertyToCheck === 'variants') {
      const variantTitles = value.map((variant) => variant.title);
      const uniqueVariantTitles = new Set(variantTitles);

      if (variantTitles.length !== uniqueVariantTitles.size) {
        return false;
      }

      for (const variant of value) {
        if (variant.options) {
          const optionNames = variant.options.map((option: any) => option.name);
          const uniqueOptionNames = new Set(optionNames);

          if (optionNames.length !== uniqueOptionNames.size) {
            return false;
          }
        }
      }
      return true;
    }

    if (propertyToCheck === 'addons') {
      const groupNames = value.map((group) => group.group_name);
      const uniqueGroupNames = new Set(groupNames);

      if (groupNames.length !== uniqueGroupNames.size) {
        return false;
      }

      for (const group of value) {
        if (group.add_ons_items) {
          const addOnNames = group.add_ons_items.map(
            (addOn: any) => addOn.add_on_name,
          );
          const uniqueAddOnNames = new Set(addOnNames);

          if (addOnNames.length !== uniqueAddOnNames.size) {
            return false;
          }
        }
      }
      return true;
    }

    if (propertyToCheck === 'combos') {
      const comboTitles = value.map((combo) => combo.title);
      const uniqueComboTitles = new Set(comboTitles);

      if (comboTitles.length !== uniqueComboTitles.size) {
        return false;
      }

      for (const combo of value) {
        if (combo.options) {
          const optionNames = combo.options.map((option: any) => option.name);
          const uniqueOptionNames = new Set(optionNames);

          if (optionNames.length !== uniqueOptionNames.size) {
            return false;
          }
        }
      }
      return true;
    }

    return true;
  }

  defaultMessage(args: ValidationArguments) {
    if (args.constraints[0] === 'variants') {
      return `Variant titles and option names must be unique.`;
    }
    if (args.constraints[0] === 'addons') {
      return `Add-on group names and add-on names must be unique.`;
    }
    return `The provided names contain duplicates. All names must be unique.`;
  }
}

export function UniqueNames(
  property: string,
  validationOptions?: ValidationOptions,
) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [property],
      validator: UniqueNamesConstraint,
    });
  };
}

export function IsNonZeroNumberString(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isNonZeroNumberString',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {
          const num = typeof value === 'number' ? value : parseFloat(value);
          if (isNaN(num) || num === 0) return false;
          const decimalPlaces = num.toString().split('.')[1];
          return !decimalPlaces || decimalPlaces.length <= 2;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a non-zero number with up to 2 decimal places`;
        },
      },
    });
  };
}

import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { format, toZonedTime } from 'date-fns-tz';
import { DatabaseService } from '../../common/config/database.services';
import { BranchesService } from '../branches/branches.service';
import { Customer } from './dto/customers.dto';

@Injectable()
export class CustomerQueryService {
  private readonly logger = new Logger(CustomerQueryService.name);
  constructor(
    private readonly databaseService: DatabaseService,
    @Inject(forwardRef(() => BranchesService))
    private readonly branchService: BranchesService,
  ) {}

  async getCustomerByPhoneNumber(
    phoneNumber: string,
  ): Promise<Customer | null> {
    const query = `
      SELECT
        c.*,
        COALESCE(cr.customer_name, c.customer_name) AS "customer_name"
      FROM
        public."Customers" c
        LEFT JOIN public."Customer_Shops" cr ON cr.fk_customer_id = c.customer_id
      WHERE
        c."phone_number" = $1
    `;
    const result = await this.databaseService.query(query, [phoneNumber]);

    return result.length > 0 ? result[0] : null;
  }

  async addCustomer(data: {
    name: string;
    phone_number: string;
  }): Promise<string> {
    const { name, phone_number } = data;

    const query =
      'INSERT INTO public."Customers" (customer_name, phone_number) VALUES($1, $2) RETURNING *';
    const customer = await this.databaseService.query(query, [
      name,
      phone_number,
    ]);

    return customer[0].customer_id;
  }

  async updateCustomer(data: {
    customer_id: string;
    total_bookings: number;
    total_spending: number;
  }): Promise<object> {
    const { customer_id, total_bookings, total_spending } = data;

    const updateQuery = `
      UPDATE "Customers" SET "total_bookings" = $2,
      "total_spending" = $3 WHERE "customer_id" = $1;`;

    const updatedCustomer = await this.databaseService.query(updateQuery, [
      customer_id,
      total_bookings,
      total_spending,
    ]);

    return updatedCustomer;
  }

  async markOrderAsDelivered(): Promise<void> {
    try {
      const sql = `
        UPDATE
          public."ShopOrders" 
        SET
          status = 'delivered', 
          modified_at = NOW()
        WHERE
          "status" = 'out_for_delivery'
        AND
          (CURRENT_TIMESTAMP - "modified_at") >= INTERVAL '30 minutes';
    `;
      await this.databaseService.query(sql);
    } catch (error) {
      throw error;
    }
  }

  async autoRejectOrders(): Promise<void> {
    try {
      const rejectedReason = 'Shop took too long to accept';
      const sql = `
        SELECT
          o.order_id,
          o.order_name,
          o.payment_method,
          o.fk_shop_id,
          o.fk_branch_id,
          o.fk_customer_id,
          o.fk_discount_id,
          o.promo_code,
          c.phone_number,
          b.cancellation_number,
          s.shop_name,
          o.items
        FROM
          public."ShopOrders" o
        LEFT JOIN
          public."ShopBranches" b ON o.fk_branch_id = b.branch_id
        LEFT JOIN
          public."Customers" c ON o.fk_customer_id = c.customer_id
        LEFT JOIN
          public."Shops" s ON o.fk_shop_id = s.shop_id
        WHERE
          o.status = 'initiated'
        AND
          (CURRENT_TIMESTAMP - GREATEST(o.created_at, o.modified_at)) >= INTERVAL '7 minutes';
      `;
      const orders = await this.databaseService.query(sql);

      const branchItemQuantities: {
        [branchId: string]: { [itemId: string]: number };
      } = {};

      for (const order of orders) {
        const {
          order_id,
          fk_shop_id,
          fk_branch_id,
          phone_number,
          order_name,
          cancellation_number,
          payment_method,
          fk_customer_id,
          fk_discount_id,
          promo_code,
          items,
        } = order;
        const sql = `
          UPDATE
            public."ShopOrders"
          SET
            status = 'rejected',
            modified_at = NOW(),
            rejected_reason = $2
          WHERE order_id = $1;`;
        const params = [order_id, rejectedReason];
        await this.databaseService.query(sql, params);

        if (promo_code) {
          try {
            await this.branchService.processDiscountUsage(
              fk_customer_id,
              fk_discount_id,
              true,
            );
          } catch (error) {
            throw new Error('Failed to update discount usage');
          }
        }

        const baseMessage = `Your Order *${order_name}*`;
        const rejectedMessage = ' was rejected by the shop.';
        const rejectedOnlineMessage = '\nYour refund has been initiated.';
        const apologyMessage = ` We apologize for the inconvenience caused.\nYour order was rejected because:\n\n${rejectedReason}\n\nPlease call *+${cancellation_number}* to know more.\n\n`;
        const endMessage =
          'This conversation has now ended, please send a *“Hi”* to initiate a new conversation.';

        let message =
          baseMessage + rejectedMessage + apologyMessage + endMessage;
        if (payment_method === 'online') {
          message =
            baseMessage +
            rejectedMessage +
            rejectedOnlineMessage +
            apologyMessage +
            endMessage;
        }

        await axios.post(
          `${process.env.WHATSAPP_URL}/commerce/send-whatsapp-message`,
          {
            shopId: fk_shop_id,
            customerPhone: phone_number,
            message,
          },
        );
        const orderItems = JSON.parse(items);

        if (!branchItemQuantities[fk_branch_id]) {
          branchItemQuantities[fk_branch_id] = {};
        }

        for (const item of orderItems) {
          if (!branchItemQuantities[fk_branch_id][item.id]) {
            branchItemQuantities[fk_branch_id][item.id] = 0;
          }
          branchItemQuantities[fk_branch_id][item.id] += item.quantity;
        }
      }

      const batchSize = 10;

      for (const [branchId, itemsQuantities] of Object.entries(
        branchItemQuantities,
      )) {
        const itemIdsBatch = Object.keys(itemsQuantities);

        for (let i = 0; i < itemIdsBatch.length; i += batchSize) {
          const batchItemIds = itemIdsBatch.slice(i, i + batchSize);
          const batchQuantities = batchItemIds.map((id) => itemsQuantities[id]);

          const updateQuantitiesQuery = `
            UPDATE
              public."ShopItems"
            SET
              item_quantity = item_quantity + subquery.requested_quantity
            FROM (
              SELECT
                unnest($2::uuid[]) AS item_id,
                unnest($3::int[]) AS requested_quantity
            ) AS subquery
            WHERE
              public."ShopItems".item_id = subquery.item_id
            AND
              public."ShopItems".fk_branch_id = $1;
          `;
          const updateQuantitiesParams = [
            branchId,
            batchItemIds,
            batchQuantities,
          ];

          try {
            await this.databaseService.query(
              updateQuantitiesQuery,
              updateQuantitiesParams,
            );
          } catch (error) {
            throw new Error('Failed to update item quantities');
          }
        }

        for (let i = 0; i < itemIdsBatch.length; i += batchSize) {
          const batchItemIds = itemIdsBatch.slice(i, i + batchSize);

          const updateStatusQuery = `
            UPDATE public."ShopItems"
            SET item_status = 'in-stock'
            WHERE item_quantity > 0
            AND fk_branch_id = $1
            AND item_id = ANY($2::uuid[]);
          `;
          const updateStatusParams = [branchId, batchItemIds];

          try {
            await this.databaseService.query(
              updateStatusQuery,
              updateStatusParams,
            );
          } catch (error) {
            throw new Error('Failed to update item stock status');
          }
        }
      }
    } catch (error) {
      throw error;
    }
  }

  async restockOutOfStockItems(): Promise<number> {
    const sql = `
      UPDATE public."ShopItems"
      SET
        item_status = CASE
        WHEN item_quantity > 0 THEN 'in-stock'
        ELSE item_status
      END,
      out_of_stock_until = NULL
      WHERE item_status = 'out-of-stock'
      AND out_of_stock_until IS NOT NULL
      AND out_of_stock_until <= NOW()
      RETURNING item_id;
    `;

    try {
      const result = await this.databaseService.query(sql);
      return result.length;
    } catch (error) {
      throw new Error('Failed to restock items');
    }
  }

  async deactivatePaymentLinks(): Promise<number> {
    const sql = `
      SELECT 
        stripe_payment_link_id, 
        fk_shop_id ,fk_branch_id
      FROM 
        public."ShopOrders" 
      WHERE 
        status = 'processing' 
      AND 
        (CURRENT_TIMESTAMP - "created_at") >= INTERVAL '1 hour';
    `;

    try {
      const processingOrders = await this.databaseService.query(sql);

      for (const order of processingOrders) {
        const { fk_shop_id, stripe_payment_link_id, fk_branch_id } = order;
        const { stripeKey } = await this.branchService.findStripeAPIKey(
          fk_shop_id,
          fk_branch_id,
        );

        await this.markPaymentLinkInactiveForStripe(
          stripe_payment_link_id,
          stripeKey,
        );
      }

      return processingOrders.length;
    } catch (error) {
      return error;
    }
  }

  private async markPaymentLinkInactiveForStripe(
    paymentLinkId: string,
    paymentApiKey: string,
  ): Promise<void> {
    try {
      if (!paymentLinkId || !paymentApiKey) {
        return;
      }
      const response = await axios.post(
        `https://api.stripe.com/v1/payment_links/${paymentLinkId}`,
        {
          active: false,
        },
        {
          headers: {
            Authorization: `Bearer ${paymentApiKey}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );

      if (response.status === 200) {
        this.logger.log(
          `Payment link ${paymentLinkId} marked as inactive for Stripe.`,
        );
      } else {
        throw new Error(
          `Failed to mark payment link ${paymentLinkId} as inactive for Stripe.`,
        );
      }
    } catch (error) {
      this.logger.error(
        'Error marking payment link as inactive for Stripe:',
        error,
      );
      throw error;
    }
  }

  async deleteProcessingOrders(): Promise<number> {
    const sql = `
      DELETE FROM
        public."ShopOrders"
      WHERE
        status = 'processing'
      AND
        (CURRENT_TIMESTAMP - "created_at") >= INTERVAL '2 hours'
      RETURNING
        order_id;
    `;
    try {
      const deleteOrders = await this.databaseService.query(sql);
      return deleteOrders.length;
    } catch (error) {
      return error;
    }
  }

  async getCurrentTimeForBranch(
    branchId: string,
    timezone: string = 'Asia/Dubai',
  ): Promise<{ time: string; offset: number }> {
    if (branchId) {
      const sql = `
        SELECT branch_timezone FROM public."ShopBranches" 
        WHERE branch_id = $1;
      `;
      const result = await this.databaseService.query(sql, [branchId]);
      if (!result || result.length === 0) {
        throw new Error(
          `Branch with ID ${branchId} not found or missing timezone.`,
        );
      }
      timezone = result[0].branch_timezone;
    }

    const currentUTCDate = new Date();
    const zonedDate = toZonedTime(currentUTCDate, timezone);
    const time = format(zonedDate, 'yyyy-MM-dd HH:mm:ss', {
      timeZone: timezone,
    });

    const standardOffset = format(zonedDate, 'xxx', { timeZone: timezone });
    const match = standardOffset.match(/([+-])(\d{2}):(\d{2})/);
    if (!match) {
      throw new Error('Invalid offset format');
    }

    const [_, sign, hours, minutes] = match;
    const offset =
      (parseInt(hours) + parseInt(minutes) / 60) * (sign === '+' ? 1 : -1);

    return { time, offset };
  }
}

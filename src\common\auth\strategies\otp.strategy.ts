import { Inject, Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { AuthService } from '../auth.service';

@Injectable()
export class OTPStrategy extends PassportStrategy(Strategy, 'OTPStrategy') {
  constructor(
    @Inject(AuthService)
    private readonly authService: AuthService,
  ) {
    super({
      usernameField: 'username',
      passwordField: 'otp',
    });
  }

  async validate(username: string, otp: any): Promise<any> {
    const user = await this.authService.verifyOTP(username, otp);
    if (!user) {
      throw new UnauthorizedException();
    }
    return user;
  }
}
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Roles } from '../../common/decorators/roles.decorator';
import { CommonService } from './common.service';
import { PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { s3Client } from 'src/common/utilities/s3';
import { Public } from 'src/common/decorators/public.decorator';
import { EmailDto } from '../branches/dto/branches.dto';
import { FileInterceptor } from '@nestjs/platform-express';

export interface PresignedPostFields {
  [key: string]: string | number;
  key: string;
  'Content-Type': string;
}

@ApiBearerAuth('access-token')
@Roles('merchant')
@ApiTags('Common Methods')
@Controller('common')
export class CommonController {
  constructor(private readonly commonService: CommonService) {}

  @Post('/create-presigned-url')
  @ApiOperation({ summary: 'Create Presigned URL for uploading files' })
  @ApiBody({
    description: 'Name and fileType of the file to be uploaded',
    type: 'object',
    schema: {
      properties: {
        name: { type: 'string' },
        fileType: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the presigned URL',
  })
  async createPresignedUrl(@Body() input: { name: string; fileType: string }) {
    try {
      const id = Date.now();

      const ex = input.fileType.split('/')[1];
      const key = `${process.env.STORAGE_FOLDER}/${
        input?.name + '_' + id
      }.${ex}`;

      const command = new PutObjectCommand({
        Bucket: 'cravin',
        Key: key,
        ACL: 'public-read',
      });
      return getSignedUrl(s3Client, command, { expiresIn: 3600 });
    } catch (e) {
      throw new Error('Failed to add image.::' + e);
    }
  }

  @Get('/get-support-chat-details/:shopId/:branchId/:type/:receptionId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiParam({ name: 'type', required: true, type: String })
  @ApiParam({ name: 'receptionId', required: true, type: String })
  @ApiOperation({ summary: 'Get all Chat details' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Get all Chat details',
  })
  async getAllChatInfo(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Param('type') type: string,
    @Param('receptionId') receptionId: string,
  ) {
    try {
      const supportRes = await this.commonService.getChatSupportInfo(
        shopId,
        branchId,
        type,
        receptionId,
      );

      if (supportRes.message) {
        throw new BadRequestException(supportRes.message);
      }

      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get(
    '/get-support-chat-history/:shopId/:branchId/:customerPhone/:receptionId',
  )
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiParam({ name: 'customerPhone', required: true, type: String })
  @ApiParam({ name: 'receptionId', required: true, type: String })
  @ApiOperation({ summary: 'Get all Chat history details' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Get all Chat history details',
  })
  @ApiQuery({
    name: 'type',
    type: String,
    description: 'The filter type',
  })
  async getChatHistory(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Param('customerPhone') customerPhone: string,
    @Param('receptionId') receptionId: string,
    @Query('type') type: string,
  ) {
    try {
      const supportRes = await this.commonService.getChatHistory(
        shopId,
        branchId,
        customerPhone,
        receptionId,
        type,
      );
      if (supportRes.message) {
        throw new BadRequestException(supportRes.message);
      }
      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('/start-end-support-chat')
  @ApiOperation({ summary: 'API to start support chat' })
  @ApiBody({
    description: 'Details of chat id and reception id',
    type: 'object',
    schema: {
      properties: {
        customerPhone: { type: 'string' },
        chatId: { type: 'string' },
        receptionId: { type: 'string' },
        receptionName: { type: 'string' },
        shopId: { type: 'string' },
        branchId: { type: 'string' },
        type: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the success',
  })
  async startSupportChat(
    @Body()
    bodyData: {
      customerPhone: string;
      receptionId: string;
      receptionName: string;
      shopId: string;
      branchId: string;
      type: string;
    },
  ) {
    try {
      const supportRes = await this.commonService.startSupportChat(bodyData);
      if (supportRes.message || !supportRes) {
        throw new BadRequestException(supportRes.message);
      }
      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
  @Post('/send-support-message')
  @ApiOperation({ summary: 'API to send support chat message' })
  @ApiBody({
    description: 'Details of chat id and reception id',
    type: 'object',
    schema: {
      properties: {
        customerPhone: { type: 'string' },
        receptionId: { type: 'string' },
        receptionName: { type: 'string' },
        shopId: { type: 'string' },
        branchId: { type: 'string' },
        message: { type: 'string' },
        chatId: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the success',
  })
  async storeSupportMessages(
    @Body()
    bodyData: {
      customerPhone: string;
      receptionId: string;
      receptionName: string;
      shopId: string;
      branchId: string;
      chatId: string;
    },
  ) {
    try {
      const supportRes =
        await this.commonService.storeSupportMessages(bodyData);
      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('/store-notification-id')
  @ApiOperation({ summary: 'API to store user notification id' })
  @ApiBody({
    description: 'Details of user id and notification id',
    type: 'object',
    schema: {
      properties: {
        userId: { type: 'string' },
        deviceId: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the success',
  })
  async storeNotificationId(
    @Body()
    bodyData: {
      userId: string;
      deviceId: string;
    },
  ) {
    try {
      const supportRes =
        await this.commonService.storeNotificationId(bodyData);
      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Public()
  @Post('/send-notification-to-merchant')
  @ApiOperation({ summary: 'API to send  notification' })
  @ApiBody({
    description: 'Details of user id , message and shop id',
    type: 'object',
    schema: {
      properties: {
        userId: { type: 'string' },
        message: { type: 'string' },
        shopId: { type: 'string' },
        branchId: { type: 'string' },
        url: { type: 'string' },
        title: { type: 'string' },
        messageType: { type: 'string' },
        orderType: { type: 'string' },
        orderId: { type: 'string' },
      },
      required: ['message', 'shopId', 'branchId', 'url', 'title'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the success',
  })
  async sendNotification(
    @Body()
    bodyData: {
      userId: string;
      message: string;
      shopId: string;
      branchId: string;
      title: string;
      url: string;
      messageType: string;
      orderType?: string;
      orderId?: string;
    },
  ) {
    try {
      const supportRes = await this.commonService.sendNotification(bodyData);
      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('/get-customer-notes/:shopId/:customerPhone')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'customerPhone', required: true, type: String })
  @ApiQuery({ name: 'branchId', required: false, type: String })
  @ApiOperation({ summary: 'Get all customer notes' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Get all  customer notes',
  })
  async getCustomerNotes(
    @Param('shopId') shopId: string,
    @Param('customerPhone') customerPhone: string,
    @Query('branchId') branchId?: string,
  ) {
    try {
      const supportRes = await this.commonService.getCustomerNotes(
        shopId,
        customerPhone,
        this.commonService.normalizeToNull(branchId) || '',
      );

      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('/add-customer-notes')
  @ApiOperation({ summary: 'API to add customer notes' })
  @ApiBody({
    description: 'Details of user id , message and shop id',
    type: 'object',
    schema: {
      properties: {
        customerPhone: { type: 'string' },
        shopId: { type: 'string' },
        branchId: { type: 'string' },
        notes: { type: 'string' },
        categoryName: { type: 'string' },
        addedBy: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the success',
  })
  async addCustomerNotes(
    @Body()
    bodyData: {
      customerPhone: string;
      shopId: string;
      branchId: string;
      notes: string;
      categoryName: string;
      addedBy: string;
    },
  ) {
    try {
      const supportRes = await this.commonService.addCustomerNotes(bodyData);
      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Put('/edit-customer-notes/:notesId')
  @ApiOperation({ summary: 'API to add customer notes' })
  @ApiParam({ name: 'notesId', required: true, type: String })
  @ApiBody({
    description: 'Details of user id , notes and shop id',
    type: 'object',
    schema: {
      properties: {
        customerPhone: { type: 'string' },
        shopId: { type: 'string' },
        branchId: { type: 'string' },
        notes: { type: 'string' },
        categoryName: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the success',
  })
  async editCustomerNotes(
    @Body()
    bodyData: {
      customerPhone: string;
      shopId: string;
      branchId: string;
      notes: string;
      categoryName: string;
    },
    @Param('notesId') notesId: string,
  ) {
    try {
      const supportRes = await this.commonService.editCustomerNotes(
        bodyData,
        notesId,
      );
      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Delete('/delete-customer-notes/:shopId/:branchId/:notesId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiParam({ name: 'notesId', required: true, type: String })
  @ApiOperation({ summary: 'delete customer notes' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'delete customer notes',
  })
  async deleteCustomerNotes(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Param('notesId') notesId: string,
  ) {
    try {
      const supportRes = await this.commonService.deleteCustomerNotes(
        shopId,
        branchId,
        notesId,
      );

      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('/get-customer-tags/:productId/:shopId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'productId', required: true, type: String })
  @ApiQuery({ name: 'branchId', required: false, type: String })
  @ApiQuery({ name: 'customerPhone', required: false, type: String })
  @ApiOperation({ summary: 'Get all customer tags' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Get all  customer tags',
  })
  async getCustomerTags(
    @Param('shopId') shopId: string,
    @Param('productId') productId: string,
    @Query('branchId') branchId?: string,
    @Query('customerPhone') customerPhone?: string,
  ) {
    try {
      const supportRes = await this.commonService.getCustomerTags(
        shopId,
        this.commonService.normalizeToNull(branchId) || '',
        this.commonService.normalizeToNull(customerPhone) || '',
        productId,
      );

      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('get-master-tags/:productId/:shopId/:branchId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiParam({ name: 'productId', required: true, type: String })
  @ApiQuery({ name: 'superTagId', required: false, type: String })
  @ApiOperation({ summary: 'Get all master tags' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Get all  master tags',
  })
  async getMasterTags(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Param('productId') productId: string,
    @Query('superTagId') superTagId?: string,
  ) {
    try {
      const supportRes = await this.commonService.getMasterTags(
        shopId,
        branchId,
        productId,
        this.commonService.normalizeToNull(superTagId) || '',
      );

      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
  @Post('/add-customer-tags')
  @ApiOperation({ summary: 'API to  tag customer profile' })
  @ApiBody({
    description: 'Details of user id , message and shop id',
    type: 'object',
    schema: {
      properties: {
        customerPhone: { type: 'string' },
        shopId: { type: 'string' },
        branchId: { type: 'string' },
        tagIds: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the success',
  })
  async addCustomerTag(
    @Body()
    bodyData: {
      customerPhone: string;
      shopId: string;
      branchId: string;
      tagIds: string[];
    },
  ) {
    try {
      const supportRes = await this.commonService.addCustomerTag(bodyData);
      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Delete('/delete-customer-tags/:shopId/:branchId/:customerPhone/:tagId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiParam({ name: 'customerPhone', required: true, type: String })
  @ApiParam({ name: 'tagId', required: true, type: String })
  @ApiOperation({ summary: 'delete customer Tag' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'delete customer Tag',
  })
  async deleteCustomerTag(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Param('customerPhone') customerPhone: string,
    @Param('tagId') tagId: string,
  ) {
    try {
      const supportRes = await this.commonService.deleteCustomerTag(
        shopId,
        branchId,
        customerPhone,
        tagId,
      );

      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('/add-sub-tag')
  @ApiOperation({ summary: 'api to create new sub tag' })
  @ApiBody({
    description: 'Details of sub tag',
    type: 'object',
    schema: {
      properties: {
        shopId: { type: 'string' },
        branchId: { type: 'string' },
        tagName: { type: 'string' },
        superTagId: { type: 'string' },
        addedBy: { type: 'string' },
        color: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the success',
  })
  async addNewSubTag(
    @Body()
    bodyData: {
      tagName: string;
      shopId: string;
      branchId: string;
      superTagId: string;
      color: string;
      addedBy: string;
    },
  ) {
    try {
      const supportRes = await this.commonService.addNewSubTag(bodyData);
      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
  @Put('/edit-sub-tag')
  @ApiOperation({ summary: 'api to edit sub tag' })
  @ApiBody({
    description: 'Details of sub tag',
    type: 'object',
    schema: {
      properties: {
        shopId: { type: 'string' },
        branchId: { type: 'string' },
        tagName: { type: 'string' },
        superTagId: { type: 'string' },
        subTagId: { type: 'string' },
        color: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the success',
  })
  async editSubTag(
    @Body()
    bodyData: {
      tagName: string;
      shopId: string;
      branchId: string;
      color: string;
      superTagId: string;
      subTagId: string;
    },
  ) {
    try {
      const supportRes = await this.commonService.editSubTag(bodyData);
      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('/replace-sub-tag')
  @ApiOperation({ summary: 'api to move sub tag' })
  @ApiBody({
    description: 'Details of sub tag',
    type: 'object',
    schema: {
      properties: {
        shopId: { type: 'string' },
        branchId: { type: 'string' },
        superTagId: { type: 'string' },
        newSubTagBranchId: { type: 'string' },
        subTagId: { type: 'string' },
        newSubTagId: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the success',
  })
  async replaceSubTag(
    @Body()
    bodyData: {
      shopId: string;
      branchId: string;
      superTagId: string;
      newSubTagBranchId: string;
      subTagId: string;
      newSubTagId: string;
    },
  ) {
    try {
      const supportRes = await this.commonService.replaceSubTag(bodyData);
      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('/get-faqs/:shopId/:branchId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiOperation({ summary: 'Get all FAQs' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Get all FAQs',
  })
  async getFAQs(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
  ) {
    try {
      const faqs = await this.commonService.getFAQs(shopId, branchId);
      return faqs;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('/get-faq-categories/:product')
  @ApiParam({ name: 'product', required: true, type: String })
  @ApiOperation({ summary: 'Get FAQ categories' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Get FAQ categories successfully',
  })
  async getFAQCategories(@Param('product') product: string) {
    try {
      const faqCategories = await this.commonService.getFAQCategories(product);
      return {
        statusCode: HttpStatus.OK,
        data: faqCategories,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('/add-new-faq')
  @ApiOperation({ summary: 'API to store FAQ' })
  @ApiBody({
    description: 'Details of shopId, question, answer, and categoryId',
    type: 'object',
    schema: {
      properties: {
        shopId: { type: 'string' },
        branchId: { type: 'string' },
        question: { type: 'string' },
        answer: { type: 'string' },
        categoryId: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Returns true if FAQ is stored successfully',
  })
  async addNewFAQ(
    @Body()
    bodyData: {
      shopId: string;
      branchId: string;
      question: string;
      answer: string;
      categoryId: string;
    },
  ) {
    try {
      const faqRes = await this.commonService.addNewFAQ(bodyData);
      return faqRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Put('/edit-faq/:faqId')
  @ApiOperation({ summary: 'API to edit FAQ' })
  @ApiParam({ name: 'faqId', required: true, type: String })
  @ApiBody({
    description: 'Details of shop id, question, answer, and category id',
    type: 'object',
    schema: {
      properties: {
        shopId: { type: 'string' },
        branchId: { type: 'string' },
        question: { type: 'string' },
        answer: { type: 'string' },
        categoryId: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Returns true if the FAQ is successfully edited',
  })
  async editFAQ(
    @Body()
    bodyData: {
      shopId: string;
      branchId: string;
      question: string;
      answer: string;
      categoryId: string;
    },
    @Param('faqId') faqId: string,
  ) {
    try {
      const editResult = await this.commonService.editFAQ(bodyData, faqId);
      return editResult;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
  @Delete('/delete-faq/:shopId/:branchId/:faqId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiParam({ name: 'faqId', required: true, type: String })
  @ApiOperation({ summary: 'Delete FAQ' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'FAQ deleted successfully',
  })
  async deleteFAQ(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Param('faqId') faqId: string,
  ) {
    try {
      await this.commonService.deleteFAQ(shopId, branchId, faqId);

      return true;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  // Transfer Chat to other
  @Post('/transfer-support-chat')
  @ApiOperation({ summary: 'API to  transfer chat to another reception' })
  @ApiBody({
    description: 'Details of user id , message and shop id',
    type: 'object',
    schema: {
      properties: {
        customerPhone: { type: 'string' },
        shopId: { type: 'string' },
        branchId: { type: 'string' },
        receptionId: { type: 'string' },
        transferredFrom: { type: 'string' },
        newReceptonId: { type: 'string' },
        chatId: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the success',
  })
  async transferChat(
    @Body()
    bodyData: {
      customerPhone: string;
      shopId: string;
      branchId: string;
      receptionId: string;
      transferredFrom: string;
      newReceptonId: string;
      chatId: string;
    },
  ) {
    try {
      const supportRes = await this.commonService.transferChat(bodyData);
      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('/get-support-chat-count/:shopId/:branchId/:receptionId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiParam({ name: 'receptionId', required: true, type: String })
  @ApiOperation({ summary: 'Get all Chat count' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Get all Chat count',
  })
  async getAllChatCount(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Param('receptionId') receptionId: string,
  ) {
    try {
      const supportRes = await this.commonService.getAllChatCount(
        shopId,
        branchId,
        'current',
        receptionId,
      );

      if (supportRes.message) {
        throw new BadRequestException(supportRes.message);
      }

      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
  @Get('/get-support-reception-data/:shopId/:branchId/:type')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiParam({ name: 'type', required: true, type: String })
  @ApiOperation({ summary: 'Get all reception data' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Get all Chat history details',
  })
  async getSupportReceptionData(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Param('type') type: string,
  ) {
    try {
      const supportRes = await this.commonService.getSupportReceptionData(
        shopId,
        branchId,
        type,
      );
      if (supportRes.message) {
        throw new BadRequestException(supportRes.message);
      }
      return supportRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Public()
  @Post('sendMail')
  @ApiOperation({ summary: 'Send email' })
  @ApiBody({ description: 'Email details', type: EmailDto })
  async sendMail(@Body() emailDto: EmailDto): Promise<void> {
    await this.commonService.sendMail(emailDto);
  }

  @Get('get-customer-profile-details/:shopId/:customerPhone')
  @ApiOperation({ summary: 'Get profile details for a specific customer' })
  @ApiResponse({
    status: 200,
    description: 'Customer profile details retrieved successfully',
  })
  @ApiParam({
    name: 'customerPhone',
    type: 'string',
    description: 'The phone number of the customer',
  })
  @ApiParam({
    name: 'shopId',
    type: 'string',
    description: 'Shop ID',
  })
  async getCustomerInfo(
    @Param('shopId') shopId: string,
    @Param('customerPhone') customerPhone: string,
  ) {
    try {
      const data = await this.commonService.getCustomerByPhone(
        shopId,
        customerPhone,
      );

      return data;
    } catch (error) {
      throw new Error('Unable to fetch customer profile details');
    }
  }

  @Public()
  @Post('store-analytics/:shopId')
  @ApiOperation({ summary: 'analytics' })
  @ApiResponse({
    status: 200,
    description: 'Analytics updated successfully',
  })
  @ApiParam({
    name: 'shopId',
    type: 'string',
    description: 'Shop ID',
  })
  @ApiBody({
    description: 'analytics data',
    type: 'object',
    schema: {
      properties: {
        customerId: { type: 'string' },
        branchId: { type: 'string' },
        type: { type: 'string' },
        additionalInfo: {
          type: 'object',
        },
      },
      required: ['customerId', 'type'],
    },
  })
  async createTag(@Param('shopId') shopId: string, @Body() body: any) {
    try {
      const data = await this.commonService.createAnalyticsTag(shopId, body);
      return data;
    } catch (error) {
      throw new Error('Unable to create tag');
    }
  }

  @ApiConsumes('multipart/form-data')
  @Post('/upload-images/:shopId')
  @UseInterceptors(FileInterceptor('file'))
  @ApiParam({
    name: 'shopId',
    description: 'ID of the Shop',
    type: 'string',
  })
  @ApiQuery({
    name: 'filepath',
    description: 'filepath',
    type: 'string',
    required: false,
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
      required: ['file'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'returns the  File handler key',
  })
  async uploadImage(
    @Param('shopId') shopId: string,
    @UploadedFile() image: any,
    @Query('filepath') filePath: string,
  ) {
    try {
      return await this.commonService.uploadImage(shopId, filePath, image);
    } catch (error) {
      throw new Error('Failed to upload image');
    }
  }

  @Delete('delete-image/:imagePath')
  @ApiOperation({ summary: 'delete image' })
  @ApiResponse({ status: 200, description: 'Image deleted successfully' })
  @ApiBadRequestResponse({ description: 'Invalid Image data' })
  @ApiParam({
    name: 'imagePath',
    type: 'string',
    description: 'Image path',
  })
  async deleteImage(@Param('imagePath') imagePath: string): Promise<void> {
    try {
      await this.commonService.deleteImage(imagePath);
    } catch (error) {
      throw new Error('Failed to delete image');
    }
  }

  @Public()
  @Post('log-errors')
  @ApiOperation({ summary: 'log errors to telegram' })
  @ApiResponse({
    status: 200,
    description: 'log created successfully',
  })
  @ApiBody({
    description: 'log errors to telegram data',
    type: 'object',
    schema: {
      properties: {
        customerId: { type: 'string' },
        branchId: { type: 'string' },
        type: { type: 'string' },
        typeOf: { type: 'string' },
        additionalInfo: {
          type: 'object',
        },
      },
    },
  })
  async logErrors(@Body() body: any) {
    try {
      const data = await this.commonService.logErrors(body);
      return data;
    } catch (error) {
      throw new Error('Unable to create tag');
    }
  }

  @Public()
  @Post('send-review-to-telegram')
  @ApiOperation({ summary: 'Send review to Telegram' })
  @ApiResponse({
    status: 200,
    description: 'Review sent successfully',
  })
  @ApiBody({
    description: 'Review data',
    type: 'object',
    schema: {
      properties: {
        shopId: { type: 'string' },
        customerName: { type: 'string' },
        customerNumber: { type: 'string' },
        review: { type: 'string' },
      },
    },
  })
  async sendReviewToTelegram(@Body() body: any) {
    try {
      const data = await this.commonService.sendReviewToTelegram(body);
      return data;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  
  @Public()
  @Post('store-merchant-analytics/:shopId')
  @ApiOperation({ summary: 'Store merchant analytics data' })
  @ApiResponse({
    status: 200,
    description: 'Merchant analytics stored successfully',
  })
  @ApiParam({
    name: 'shopId',
    type: 'string',
    description: 'Shop ID',
  })
  @ApiBody({
    description: 'Merchant analytics data',
    type: 'object',
    schema: {
      properties: {
        merchantId: { type: 'string' },
        merchantName: { type: 'string' },
        // branchId: { type: 'string' },
        type: { type: 'string' },
        // typeOf: { type: 'string' },
        additionalInfo: {
          type: 'object',
        },
      },
      required: ['merchantId', 'merchantName', 'type'],
    },
  })
  async storeMerchantAnalytics(
    @Param('shopId') shopId: string,
    @Body() body: any,
  ) {
    try {
      const data = await this.commonService.storeMerchantAnalytics(
        shopId,
        body,
      );
      return data;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
}

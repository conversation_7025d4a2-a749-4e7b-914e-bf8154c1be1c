import { DeleteObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  UnauthorizedException,
} from '@nestjs/common';
import axios from 'axios';
import * as nodemailer from 'nodemailer';
import { s3Client } from 'src/common/utilities/s3';
import { DatabaseService } from '../../common/config/database.services';
import { EmailDto } from '../branches/dto/branches.dto';
require('dotenv').config();

const whatsappURL = process.env.WHATSAPP_URL;
const whatsappAuthToken = process.env.WHATSAPP_AUTH_TOKEN;
const appId = process.env.ONESIGNAL_APP_ID;
const oneSignalAuthToken = process.env.ONESIGNAL_AUTH_TOKEN;
@Injectable()
export class CommonService {
  private readonly botToken = process.env.TELEGRAM_OTP_BOT_TOKEN;
  private readonly apiUrl = `https://api.telegram.org/bot${this.botToken}/`;
  constructor(private readonly databaseService: DatabaseService) {}

  async getChatSupportInfo(
    shopId: string,
    branchId: string,
    type: string,
    receptionId: string,
  ) {
    try {
      const config = {
        method: 'get',
        url: `${whatsappURL}/support/get-support-chat-details/${shopId}/${type}/${receptionId}/shop${branchId != 'null' ? '?branchId=' + branchId : ''}`,
        headers: {
          Authorization: `Bearer ${whatsappAuthToken}`,
          'Content-Type': 'application/json',
        },
      };
      const res = await axios(config);
      return res?.data;
    } catch (err) {
      return err?.response?.data; // Assuming you want to return the error response data
    }
  }

  async getChatHistory(
    shopId: string,
    branchId: string,
    customerPhone: string,
    receptionId: string,
    type: string,
  ) {
    try {
      const config = {
        method: 'get',
        url: `${whatsappURL}/support/get-support-chat-history/${shopId}/${customerPhone}/${receptionId}/shop${
          type ? '?type=' + type : ''
        }${branchId != 'null' ? (type ? '&' : '?' + 'branchId=' + branchId) : ''}`,
        headers: {
          Authorization: `Bearer ${whatsappAuthToken}`,
          'Content-Type': 'application/json',
        },
      };
      const res = await axios(config);
      return res?.data;
    } catch (err) {
      return err?.response?.data; // Assuming you want to return the error response data
    }
  }
  async startSupportChat(bodyData) {
    try {
      const headers = {
        'Content-Type': 'application/json; charset=utf-8',
        Authorization: `Bearer ${whatsappAuthToken}`,
      };
      const data = await axios.post(
        `${whatsappURL}/support/start-end-support-chat/shop`,
        bodyData,
        { headers },
      );
      return data?.data;
    } catch (err) {
      return err?.response?.data; // Assuming you want to return the error response data
    }
  }
  async storeSupportMessages(bodyData) {
    try {
      const headers = {
        'Content-Type': 'application/json; charset=utf-8',
        Authorization: `Bearer ${whatsappAuthToken}`,
      };

      const data = await axios.post(
        `${whatsappURL}/support/send-support-message/shop`,
        bodyData,
        { headers },
      );
      return data?.data;
    } catch (err) {
      return err?.response?.data; // Assuming you want to return the error response data
    }
  }

  async storeNotificationId(bodyData: { userId: string; deviceId: string }) {
    const { userId, deviceId } = bodyData;
    try {
      const sqlQuery = `
      UPDATE public."AuthMasterMerchant"
        SET onesignal_id = onesignal_id ||  ARRAY['${deviceId}']  
      WHERE user_id = '${userId}'  AND NOT ('${deviceId}' = ANY(onesignal_id));
      `;
      return await this.databaseService.query(sqlQuery);
    } catch (error) {}
  }
  async sendNotification(bodyData: any) {
    const {
      userId = '',
      message,
      shopId,
      branchId,
      title,
      url,
      messageType = 'order',
      orderType = '',
      orderId = '',
    } = bodyData;
    try {
      const sqlQuery = `
      SELECT ARRAY_AGG(onesignal_id) filter(where onesignal_id <> '{}') AS all_onesignal_ids
      FROM public."AuthMasterMerchant"
      WHERE '${branchId}' = ANY(branch_ids)
        AND onesignal_id IS NOT NULL
        AND shop_id ='${shopId}'
        AND user_type = 'manager'
        ${userId !== '' ? `AND user_id = CAST('${userId}' AS uuid)` : ''};
      
      `;
      const data = await this.databaseService.query(sqlQuery);
      if (data?.length > 0) {
        let onesignalIds = [];
        for (const each of data[0]?.all_onesignal_ids) {
          if (each) {
            onesignalIds = [...onesignalIds, ...each];
          }
        }
        if (onesignalIds.length > 0) {
          const headers = {
            'Content-Type': 'application/json; charset=utf-8',
            Authorization: `Basic ${oneSignalAuthToken}`,
          };

          const res = await axios.post(
            `https://api.onesignal.com/notifications`,
            {
              app_id: appId,
              name: 'string',
              contents: {
                en: message,
              },
              headings: {
                en: title,
              },
              url: url,
              include_player_ids: onesignalIds,
              data: { messageType, orderType, orderId },
            },
            { headers },
          );

          return res?.data;
        }
      }
    } catch (error) {}
  }

  async getCustomerNotes(
    shopId: string,
    customerPhone: string,
    branchId: string,
  ) {
    try {
      let sqlQuery = `
        SELECT notes_id notesId, sb.fk_shop_id shopId, notes, addedby, addedon, customer_phone customerPhone,category_name category,
        sb.fk_branch_id,b.branch_name branch_name 
          FROM public."CustomerNotes" sb
            JOIN public."ShopBranches" b ON 
                  sb.fk_branch_id = b.branch_id 
                  AND sb.fk_shop_id = b.fk_shop_id
        WHERE customer_phone='${customerPhone}' 
        AND sb.fk_shop_id ='${shopId}'
       
      `;

      if (branchId) {
        sqlQuery += `
           AND sb.fk_branch_id ='${branchId}';
        `;
      }
      return await this.databaseService.query(sqlQuery);
    } catch (error) {}
  }

  async addCustomerNotes(bodyData) {
    const { customerPhone, shopId, branchId, notes, categoryName, addedBy } =
      bodyData;
    try {
      const sqlQuery = `
      INSERT INTO public."CustomerNotes"(
        fk_shop_id,fk_branch_id, notes, addedby, customer_phone, category_name)
      SELECT $1::text,$6::text, $2::text, $3::text, $4::text, $5::text
      WHERE NOT EXISTS (
        SELECT 1 FROM public."CustomerNotes"
        WHERE customer_phone = $4::text AND fk_shop_id = $1::text
        AND notes = $2::text AND category_name = $5::text
        AND fk_branch_id =$6::text
      );
    `;

      // Pass parameters separately from the query
      const values = [
        shopId,
        notes,
        addedBy,
        customerPhone,
        categoryName,
        branchId,
      ];

      await this.databaseService.query(sqlQuery, values);
      return true;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async editCustomerNotes(
    bodyData: {
      customerPhone: string;
      shopId: string;
      branchId: string;
      notes: string;
      categoryName: string;
    },
    notesId: string,
  ) {
    const { customerPhone, shopId, branchId, notes, categoryName } = bodyData;
    try {
      const sqlQuery = `
      UPDATE public."CustomerNotes"
      SET fk_shop_id = $1::text,
          notes = $2::text,
          customer_phone = $3::text,
          category_name = $4::text
      WHERE customer_phone = $3::text
        AND fk_shop_id = $1::text
        AND fk_branch_id = $6::text
        AND notes_id = $5::uuid;
    `;

      // Pass parameters separately from the query
      const values = [
        shopId,
        notes,
        customerPhone,
        categoryName,
        notesId,
        branchId,
      ];

      await this.databaseService.query(sqlQuery, values);
      return true;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async deleteCustomerNotes(shopId: string, branchId: string, notesId: string) {
    try {
      const sqlQuery = `
        DELETE FROM public."CustomerNotes"
        WHERE notes_id=CAST('${notesId}' AS uuid)  
        AND fk_shop_id ='${shopId}' 
        AND fk_branch_id ='${branchId}'
      `;
      await this.databaseService.query(sqlQuery);
      return true;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async getCustomerTags(
    shopId: string,
    branchId: string,
    customerPhone: string,
    productId: string,
  ) {
    try {
      let sqlQuery = '';
      if (customerPhone) {
        sqlQuery = `
    SELECT
        cst.mapping_id AS mappingid,
        sst.tag_id AS supertagid,
        sst.tag_name AS supertagname,
        mst.tag_id AS subtagid,
        mst.color AS color,
        mst.tag_name AS subtagname,
        b.branch_name branch_name,
        b.branch_id 
    FROM
        public."CustomerTagsMapping" cst
    INNER JOIN
        public."MasterSubTags" mst ON cst.fk_sub_tag_id = mst.tag_id
    LEFT JOIN
        public."MasterSuperTags" sst ON mst.fk_super_tag_id = sst.tag_id
         LEFT JOIN public."ShopBranches" b ON 
                  mst.fk_branch_id = b.branch_id AND mst.fk_shop_id = b.fk_shop_id

    WHERE
        cst.fk_shop_id = '${shopId}'`;

        if (branchId) {
          sqlQuery += ` AND cst.fk_branch_id = '${branchId}'`;
        }

        sqlQuery += ` 
        AND cst.fk_customer_phone = '${customerPhone}'   
             AND  sst.product_name = '${productId}'`;
      } else {
        sqlQuery = `
              SELECT
                  sst.tag_id AS supertagid,
                  sst.tag_name AS supertagname,
                  mst.tag_id AS subtagid,
                  mst.color AS color,
                  mst.tag_name AS subtagname,
                  b.branch_name branch_name,
                  b.branch_id
              FROM
                public."MasterSuperTags" sst
               JOIN
                public."MasterSubTags" mst ON sst.tag_id = mst.fk_super_tag_id
               LEFT JOIN public."ShopBranches" b ON 
                  mst.fk_branch_id = b.branch_id AND mst.fk_shop_id = b.fk_shop_id

              WHERE
                mst.fk_shop_id = '${shopId}'`;

        if (branchId) {
          sqlQuery += ` AND mst.fk_branch_id = '${branchId}'`;
        }

        sqlQuery += ` 
                   AND  sst.product_name = '${productId}'`;
      }

      // Group the results by superTagId and superTagName
      sqlQuery += `
            ORDER BY
                sst.tag_id,
                mst.tag_id`;

      const result = await this.databaseService.query(sqlQuery);
      // Structure the response according to the provided format
      const response = [];
      const superTagMap = {}; // Map to store super tags

      for (const row of result) {
        if (!superTagMap[row.supertagid]) {
          // If the super tag doesn't exist in the map, create it
          superTagMap[row.supertagid] = {
            superTagId: row.supertagid,
            superTagName: row.supertagname,
            subTags: [],
          };
          response.push(superTagMap[row.supertagid]);
        }

        // Push each subTag object into the subTags array of its super tag
        superTagMap[row.supertagid].subTags.push({
          mappingId: row.mappingid,
          subTagId: row.subtagid,
          superTagId: row.supertagid,
          color: row.color,
          subTagName: row.subtagname,
          branchId: row.branch_id ? row.branch_id : 'null',
          branchName: row.branch_name ? row.branch_name : 'null',
        });
      }

      return response;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async getMasterTags(
    shopId: string,
    branchId: string,
    productId: string,
    superTagId: string,
  ) {
    try {
      let sqlQuery = '';

      // If superTagId is provided, retrieve subtags related to that superTagId
      if (superTagId) {
        sqlQuery = `
                SELECT
                    tag_id,
                    tag_name,
                    fk_super_tag_id,
                    sb.fk_shop_id,
                    added_by,
                    added_on,
                    color,
                    b.branch_id,
                    b.branch_name
                FROM
                    public."MasterSubTags" sb
                    LEFT JOIN public."ShopBranches" b ON 
                  sb.fk_branch_id = b.branch_id AND sb.fk_shop_id = b.fk_shop_id

                WHERE
                    fk_super_tag_id = '${superTagId}'
                    AND sb.fk_shop_id= '${shopId}'
                    AND sb.fk_branch_id ='${branchId}' `;
      } else {
        // Otherwise, retrieve all super tags
        sqlQuery = `
                SELECT
                    tag_id,
                    tag_name,
                    product_name
                FROM
                    public."MasterSuperTags"
                WHERE product_name = '${productId}'`;
      }

      return await this.databaseService.query(sqlQuery);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async addCustomerTag(bodyData: {
    customerPhone: string;
    shopId: string;
    branchId: string;
    tagIds: string[];
  }) {
    const { customerPhone, shopId, tagIds, branchId } = bodyData;
    try {
      // Iterate over each tag ID and insert individually
      for (const tagId of tagIds) {
        const sqlQuery = `
          INSERT INTO public."CustomerTagsMapping"(
              mapping_id, fk_shop_id, fk_sub_tag_id,fk_branch_id, fk_customer_phone, added_on)
          SELECT gen_random_uuid(), '${shopId}', '${tagId}','${branchId}',  '${customerPhone}', NOW()
          WHERE NOT EXISTS (
              SELECT 1
              FROM public."CustomerTagsMapping"
              WHERE fk_sub_tag_id = '${tagId}'
              AND fk_customer_phone = '${customerPhone}'
              AND fk_shop_id = '${shopId}'
              AND fk_branch_id = '${branchId}'
          );
            `;
        await this.databaseService.query(sqlQuery);
      }

      return true;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async deleteCustomerTag(
    shopId: string,
    branchId: string,
    customerPhone: string,
    tagId: string,
  ) {
    try {
      const deleteQuery = `
            DELETE FROM public."CustomerTagsMapping"
            WHERE fk_shop_id = '${shopId}'
            AND fk_customer_phone = '${customerPhone}'
            AND fk_sub_tag_id = '${tagId}'
            AND fk_branch_id ='${branchId}';
        `;
      await this.databaseService.query(deleteQuery);

      return true;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async addNewSubTag(bodyData: {
    tagName: string;
    shopId: string;
    branchId: string;
    superTagId: string;
    addedBy: string;
    color: string;
  }) {
    const { tagName, shopId, branchId, superTagId, addedBy, color } = bodyData;
    try {
      // Check if the number of subtags under the supertag has reached the limit
      const countQuery = `
        SELECT COUNT(*) AS subTagCount
         FROM public."MasterSubTags"
        WHERE fk_super_tag_id = '${superTagId}' 
        AND fk_shop_id  ='${shopId}'
        AND fk_branch_id ='${branchId}';
      `;
      const result = await this.databaseService.query(countQuery);
      const subTagCount = parseInt(result[0].subTagCount);

      if (subTagCount >= 10) {
        throw new Error('Cannot add more than 10 subtags to this supertag.');
      }

      // Check for duplicate subtag name under the same supertag
      const duplicateCheckQuery = `
        SELECT 1
           FROM public."MasterSubTags"
        WHERE tag_name='${tagName}' 
        AND fk_super_tag_id ='${superTagId}'
        AND fk_shop_id  ='${shopId}'
        AND fk_branch_id ='${branchId}';
      `;
      const duplicateCheckResult =
        await this.databaseService.query(duplicateCheckQuery);

      if (duplicateCheckResult.length > 0) {
        throw new Error(
          'Subtag with the same name already exists under this supertag.',
        );
      }

      // Insert the new subtag if all checks pass
      const sqlQuery = `
      INSERT INTO public."MasterSubTags"(
        tag_id, tag_name, fk_super_tag_id, fk_shop_id, added_by, color,fk_branch_id)
      VALUES (
        gen_random_uuid(), 
        $1::text, 
        $2::uuid, 
        $3::text, 
        $4::text, 
        $5::text, 
        $6::text
      );
    `;

      // Pass parameters separately from the query
      const values = [tagName, superTagId, shopId, addedBy, color, branchId];

      await this.databaseService.query(sqlQuery, values);
      const recordQuery = `
      SELECT *
        FROM public."MasterSubTags"
      WHERE tag_name='${tagName}' 
      AND fk_super_tag_id ='${superTagId}'
      AND fk_branch_id ='${branchId}'
      AND fk_shop_id  ='${shopId}';
    `;
      return await this.databaseService.query(recordQuery);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async editSubTag(bodyData: {
    tagName: string;
    shopId: string;
    branchId: string;
    superTagId: string;
    color: string;
    subTagId: string;
  }) {
    const { tagName, shopId, branchId, superTagId, color, subTagId } = bodyData;
    try {
      // Check if the subtag exists
      let checkQuery = `
      SELECT 1
      FROM public."MasterSubTags"
      WHERE tag_name = $1::text
      AND fk_super_tag_id = $2::uuid
      AND fk_shop_id = $3::text 
    
    `;
      const values2 = [tagName, superTagId, shopId];

      if (branchId && branchId !== 'null') {
        checkQuery += `AND fk_branch_id = $4::text;
    `;
        values2.push(branchId);
      }

      // Pass parameters separately from the query

      const checkResult = await this.databaseService.query(checkQuery, values2);
      if (checkResult.length > 0) {
        throw new Error(
          'Subtag with the same name already exists under this supertag.',
        );
      }

      // Update the subtag details
      let updateQuery = `
      UPDATE public."MasterSubTags"
      SET color = $1::text,
          tag_name = $2::text
      WHERE 
        fk_super_tag_id = $3::uuid
        AND fk_shop_id = $4::text 
    
        AND tag_id = $5::uuid
    `;
      const values = [color, tagName, superTagId, shopId, subTagId];
      if (branchId && branchId !== 'null') {
        updateQuery += `
        AND fk_branch_id = $6::text;
        `;
        values.push(branchId);
      }
      // Pass parameters separately from the query

      await this.databaseService.query(updateQuery, values);

      return true;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
  async replaceSubTag(bodyData: {
    shopId: string;
    branchId: string;
    superTagId: string;
    newSubTagBranchId: string;
    subTagId: string;
    newSubTagId: string;
  }) {
    const {
      shopId,
      branchId,
      superTagId,
      newSubTagBranchId,
      subTagId,
      newSubTagId,
    } = bodyData;
    try {
      if (newSubTagId !== '') {
        // Update subtag ID in the customer mapping table
        const updateMappingQuery = `
            
            UPDATE public."CustomerTagsMapping"
                SET fk_sub_tag_id = '${newSubTagId}',
                fk_branch_id= '${newSubTagBranchId}' 
            WHERE fk_sub_tag_id = '${subTagId}' 
             AND fk_shop_id  ='${shopId}'
             AND fk_branch_id  ='${branchId}' ;

          DELETE FROM public."CustomerTagsMapping"
          WHERE mapping_id IN (
              SELECT mappingid
              FROM (
                  SELECT
                      cst.mapping_id AS mappingid,
                      ROW_NUMBER() OVER (PARTITION BY cst.fk_customer_phone, mst.tag_id ORDER BY cst.mapping_id) AS row_num
                  FROM
                      public."CustomerTagsMapping" cst
                  INNER JOIN
                      public."MasterSubTags" mst ON cst.fk_sub_tag_id = mst.tag_id
                  LEFT JOIN
                      public."MasterSuperTags" sst ON mst.fk_super_tag_id = sst.tag_id
                  WHERE
                      cst.fk_shop_id = '${shopId}' and
                      cst.fk_branch_id = '${newSubTagBranchId}'
               ) AS subquery
            WHERE row_num > 1
          );

        `;
        await this.databaseService.query(updateMappingQuery);
      }

      // Delete the old subtag from the MasterSubTags table
      const deleteOldSubTagQuery = `
            DELETE FROM public."MasterSubTags"
            WHERE tag_id = '${subTagId}' 
            AND fk_super_tag_id ='${superTagId}'
            AND fk_shop_id  ='${shopId}'
            AND fk_branch_id  ='${branchId}';

           DELETE FROM public."CustomerTagsMapping" 
            WHERE fk_sub_tag_id = '${subTagId}' 
             AND fk_shop_id  ='${shopId}'
             AND fk_branch_id  ='${branchId}' ;
        `;
      await this.databaseService.query(deleteOldSubTagQuery);

      return true;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async getFAQs(shopId: string, branchId: string) {
    try {
      const sqlQuery = `
            SELECT q.question_id AS faqId, q.question, q.answer, q.fk_category_id AS categoryId, c.category_name AS categoryName
            FROM public."FAQQuestions" q
            JOIN public."FAQCategory" c ON q.fk_category_id = c.categroy_id
            WHERE q.fk_shop_id = '${shopId}' 
              AND q.fk_branch_id = '${branchId}'
            order by c.category_name,q.question;
        `;
      const faqs = await this.databaseService.query(sqlQuery);

      // Grouping FAQs by category
      const groupedFaqs = faqs.reduce((acc, faq) => {
        const categoryIndex = acc.findIndex(
          (cat) => cat.categoryId === faq.categoryid,
        );

        if (categoryIndex !== -1) {
          acc[categoryIndex].faqs.push({
            faqId: faq.faqid,
            question: faq.question,
            answer: faq.answer,
            categoryId: faq.categoryid,
          });
        } else {
          acc.push({
            categoryId: faq.categoryid,
            categoryName: faq.categoryname,
            faqs: [
              {
                faqId: faq.faqid,
                question: faq.question,
                answer: faq.answer,
                categoryId: faq.categoryid,
              },
            ],
          });
        }
        return acc;
      }, []);

      return groupedFaqs;
    } catch (error) {
      throw new Error('Failed to fetch FAQs: ' + error.message);
    }
  }

  async getFAQCategories(product: string) {
    try {
      const sqlQuery = `
      SELECT categroy_id categoryId, category_name categoryName
          FROM public."FAQCategory"
      WHERE product = '${product}';
      `;

      return await this.databaseService.query(sqlQuery);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
  async addNewFAQ(bodyData: {
    shopId: string;
    branchId: string;
    question: string;
    answer: string;
    categoryId: string;
  }) {
    try {
      const { shopId, branchId, question, answer, categoryId } = bodyData;

      // Check if the same question already exists in the database
      const checkQuery = `
        SELECT COUNT(*) AS question_count
            FROM public."FAQQuestions"
        WHERE fk_shop_id = $1::text 
        AND fk_category_id = $2::uuid AND question = $3::text
        AND fk_branch_id = $4::text 
      `;
      const checkResult = await this.databaseService.query(checkQuery, [
        shopId,
        categoryId,
        question,
        branchId,
      ]);
      const questionCount = checkResult[0].question_count;

      if (questionCount > 0) {
        throw new Error(`This question already exists in the FAQ.`);
      }

      // Check the current count of FAQ questions in the specified category and club
      const countQuery = `
        SELECT COUNT(*) AS faq_count
            FROM public."FAQQuestions"
        WHERE fk_shop_id = $1::text 
        AND fk_category_id = $2::uuid
        AND fk_branch_id = $3::text 
      `;
      const countResult = await this.databaseService.query(countQuery, [
        shopId,
        categoryId,
        branchId,
      ]);
      const faqCount = countResult[0].faq_count;

      const maxFAQCount = 5;

      if (faqCount >= maxFAQCount) {
        throw new Error(
          `Cannot add more FAQ questions. Maximum limit (${maxFAQCount}) reached for this category.`,
        );
      }

      // Proceed with adding the new FAQ question if the count is within limits
      const sqlQuery = `
        INSERT INTO public."FAQQuestions"(
          question, answer, fk_shop_id, fk_category_id,fk_branch_id)
        VALUES (
          $1::text, 
          $2::text, 
          $3::text, 
          $4::uuid,
           $5::text 
        );
      `;

      // Pass parameters separately from the query
      const values = [question, answer, shopId, categoryId, branchId];

      await this.databaseService.query(sqlQuery, values);
      return true;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async editFAQ(
    bodyData: {
      shopId: string;
      branchId: string;
      question: string;
      answer: string;
      categoryId: string;
    },
    faqId: string,
  ) {
    const { shopId, branchId, question, answer, categoryId } = bodyData;
    try {
      // Check if the same question already exists in the database
      const checkQuery = `
        SELECT COUNT(*) AS question_count
            FROM public."FAQQuestions"
        WHERE fk_shop_id = $1::text 
        AND fk_category_id = $2::uuid 
        AND question = $3::text AND question_id <> $4::uuid
         AND fk_branch_id = $5::text 
      `;
      const checkResult = await this.databaseService.query(checkQuery, [
        shopId,
        categoryId,
        question,
        faqId,
        branchId,
      ]);
      const questionCount = checkResult[0].question_count;

      if (questionCount > 0) {
        throw new Error(`This question already exists in the FAQ.`);
      }

      const updateQuery = `
        UPDATE public."FAQQuestions"
        SET fk_shop_id = $1::text,
            question = $2::text,
            answer = $3::text,
            fk_branch_id = $6::text,
            fk_category_id = $4::uuid
        WHERE question_id = $5::uuid;
      `;

      // Pass parameters separately from the query
      const values = [shopId, question, answer, categoryId, faqId, branchId];

      await this.databaseService.query(updateQuery, values);
      return true;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async deleteFAQ(shopId: string, branchId: string, faqId: string) {
    try {
      const sqlQuery = `
        DELETE FROM public."FAQQuestions"
        WHERE question_id = CAST('${faqId}' AS uuid) 
        AND fk_shop_id = '${shopId}' 
        AND fk_branch_id = '${branchId}';
      `;
      await this.databaseService.query(sqlQuery);
      return true;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
  async getSupportReceptionData(
    shopId: string,
    branchId: string,
    type: string,
  ) {
    try {
      const config = {
        method: 'get',
        url: `${whatsappURL}/support/get-support-reception-data/${shopId}/${type}/shop${branchId != 'null' ? '?branchId=' + branchId : ''}`,
        headers: {
          Authorization: `Bearer ${whatsappAuthToken}`,
          'Content-Type': 'application/json',
        },
      };
      const res = await axios(config);
      return res?.data;
    } catch (err) {
      return err?.response?.data; // Assuming you want to return the error response data
    }
  }
  async transferChat(bodyData: {
    customerPhone: string;
    shopId: string;
    branchId: string;
    receptionId: string;
    transferredFrom: string;
    newReceptonId: string;
    chatId: string;
  }) {
    try {
      const { customerPhone, shopId, branchId } = bodyData;

      // Link customer to branch if customer exists and valid IDs are provided
      await this.linkCustomerToBranchByPhone(customerPhone, shopId, branchId, 'chat transfer');

      const headers = {
        'Content-Type': 'application/json; charset=utf-8',
        Authorization: `Bearer ${whatsappAuthToken}`,
      };
      const data = await axios.post(
        `${whatsappURL}/support/transfer-chat/shop`,
        bodyData,
        { headers },
      );
      return data?.data;
    } catch (err) {
      return err?.response?.data; // Assuming you want to return the error response data
    }
  }

  async getAllChatCount(
    shopId: string,
    branchId: string,
    type: string,
    receptionId: string,
  ) {
    try {
      const config = {
        method: 'get',
        url: `${whatsappURL}/support/get-support-chat-count/${shopId}/${type}/${receptionId}/shop${branchId != 'null' ? '?branchId=' + branchId : ''}`,
        headers: {
          Authorization: `Bearer ${whatsappAuthToken}`,
          'Content-Type': 'application/json',
        },
      };
      const res = await axios(config);
      return res?.data;
    } catch (err) {
      return err?.response?.data; // Assuming you want to return the error response data
    }
  }
  async sendMail(emailDto: EmailDto): Promise<void> {
    if (emailDto.secret !== process.env.EMAIL_SECRET) {
      throw new UnauthorizedException('Invalid secret!');
    }

    const transporter = nodemailer.createTransport({
      host: 'smtp.zoho.com',
      secure: true,
      port: 465,
      auth: {
        user: emailDto.from,
        pass: emailDto.password,
      },
    });

    const mailOptions = {
      from: emailDto.from,
      to: emailDto.to,
      subject: emailDto.subject,
      text: emailDto.text,
    };

    return new Promise((resolve, reject) => {
      transporter.sendMail(mailOptions, (error) => {
        if (error) {
          reject(error);
        } else {
          resolve();
        }
      });
    });
  }

  async getCustomerByPhone(
    shopId: string,
    phoneNumber: string,
  ): Promise<{
    customer_id: string;
    customer_name: string;
    phone_number: string;
    email_address: string | null;
    gender: string | null;
    nationality: string | null;
    customer_address: object;
    total_orders: string;
    total_spending: string;
    broadcast: boolean;
    branch_names?: string;
  }> {
    try {
      const sql = `
        WITH filtered_orders AS (
          SELECT o.order_id, o.fk_customer_id, (o.bill->>'total_bill')::numeric AS total_bill
          FROM public."ShopOrders" o
          WHERE o.status IN ('delivered', 'picked_up')
          AND o.fk_shop_id = $1
        ), customer_branches AS (
          SELECT cr.fk_customer_id, array_agg(DISTINCT b.branch_name) AS branch_names
          FROM public."Customer_Shops" cr
          LEFT JOIN public."ShopBranches" b ON b.branch_id = ANY(cr.branch_ids)
          WHERE cr.fk_shop_id = $1
          GROUP BY cr.fk_customer_id
        )
        SELECT c.customer_id,
          COALESCE(cr.customer_name, c.customer_name) AS customer_name,
          c.phone_number,
          c.email_address,
          c.gender,
          c.nationality,
            CASE
            WHEN c.customer_address IS NOT NULL THEN jsonb_agg(DISTINCT 
              jsonb_build_object(
                'address', 
                  (addr.value->>'sector')::text || ', ' || 
                  (addr.value->>'building')::text || ', ' || 
                  (addr.value->>'google_address')::text,
                'landmark', (addr.value->>'landmark')::text,
                'latitude', (addr.value->>'latitude')::numeric,
                'longitude', (addr.value->>'longitude')::numeric,
                'recipient_name', (addr.value->>'recipient_name')::text,
                'recipient_contact', (addr.value->>'recipient_contact')::text,
                'type', addr.key::text,
                'is_customer_favourite', (addr.key = c.customer_favourite_address_tag)::boolean
              )
            )
            ELSE NULL
          END AS customer_address,
          COUNT(DISTINCT fo.order_id)::int AS total_orders,
          ROUND(COALESCE(SUM(fo.total_bill), 0), 2)::real AS total_spending,
          CASE
            WHEN cr.broadcast = true THEN 'yes'
            ELSE 'no'
          END AS broadcast ,cr.created_at
        FROM public."Customers" c
        JOIN public."Customer_Shops" cr ON c.customer_id = cr.fk_customer_id
        LEFT JOIN filtered_orders fo ON cr.fk_customer_id = fo.fk_customer_id
        LEFT JOIN customer_branches cb ON c.customer_id = cb.fk_customer_id
        LEFT JOIN LATERAL (select * from jsonb_each(c.customer_address)) addr ON true
        WHERE cr.fk_shop_id = $1
        AND c.phone_number = $2
        GROUP BY c.customer_id, cr.customer_name, c.customer_name, c.phone_number, c.customer_address, cr.broadcast, cb.branch_names ,cr.created_at
      `;

      const params = [shopId, phoneNumber];
      const result = await this.databaseService.query(sql, params);

      return result.length > 0 ? result[0] : null;
    } catch (error) {
      throw new Error('Failed to fetch customer information by phone number');
    }
  }

  async createAnalyticsTag(
    shopId: string,
    body: {
      customerId: string;
      type: string;
      typeOf?: string;
      branchId?: string;
      additionalInfo?: any;
    },
  ) {
    const {
      customerId,
      branchId = null,
      type,
      typeOf = 'button',
      additionalInfo,
    } = body;
    if (customerId !== 'null' && customerId !== null) {
      if (type === 'MENU_PAGE') {
        const sql = `
          SELECT 1
          FROM public."CustomerAnalytics"
          WHERE type = $1 
            AND created_at >= NOW() - INTERVAL '5 minutes'
            AND fk_customer_id = $2
            AND fk_shop_id = $3;
      `;
        const params = ['MENU_PAGE', customerId, shopId];
        try {
          const result = await this.databaseService.query(sql, params);
          if (result.length > 0) {
            return;
          }
        } catch (error) {
          console.error('Error executing query:', error);
        }
      } else if (
        (type === 'VIEW_ITEM' || type === 'ADD_ITEM') &&
        additionalInfo?.itemId
      ) {
                const sql = `
            SELECT 1
            FROM public."CustomerAnalytics"
            WHERE type = $1
              AND created_at >= NOW() - INTERVAL '5 minutes'
              AND fk_customer_id = $2
              AND additional_info->>'itemId' = $3
              AND fk_shop_id=$4;  
        `;
        const params = [type, customerId, additionalInfo?.itemId, shopId];
        try {
          const result = await this.databaseService.query(sql, params);
          if (result.length > 0) {
            return;
          }
        } catch (error) {
          console.error('Error executing query:', error);
        }
      } else if (type === 'OPEN_CART' && customerId && branchId) {
        // Ensure customer is linked to the branch when they open cart
        try {
          await this.ensureBranchLinkedToCustomer(customerId, shopId, branchId);
        } catch (error) {
          console.error('Error linking customer to branch:', error);
        }

        // remove the existing users open cart based on branch
        const sql = `
          DELETE FROM public."CustomerAnalytics"
          WHERE type = 'OPEN_CART'
            AND fk_customer_id = $1
            AND fk_shop_id = $2
            AND fk_branch_id = $3;
        `;
        const params = [customerId, shopId, branchId];
        try {
          await this.databaseService.query(sql, params);
        } catch (error) {
          console.error('Error executing query:', error);
        }
      }
    }
    const sql = `
    INSERT INTO public."CustomerAnalytics" (fk_customer_id, type, fk_shop_id,
    fk_branch_id,type_of,additional_info)
    VALUES ($1, $2, $3, $4,$5,$6)
    `;
    const params = [customerId, type, shopId, branchId, typeOf, additionalInfo];
    try {
      await this.databaseService.query(sql, params);
      fetch(process.env.WHATSAPP_URL + '/send-telegram-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ shopId, Product: 'Commerce', ...body }),
      });

      return true;
    } catch (error) {
      throw new Error('Failed to store analytics for customer');
    }
  }

  async clearCartFromAnalytics(
    shopId: string,
    customerId: string,
    branch_id: string,
    order_id: string,
  ) {
    const sql = `
    UPDATE public."CustomerAnalytics"
      SET type = 'PAYMENT_STARTED',
      additional_info = jsonb_set(additional_info, '{order_id}', '"${order_id}"')
     
      WHERE 
          fk_customer_id = $1
          AND fk_shop_id = $2
          AND type = 'OPEN_CART'
          AND created_at >= NOW() - INTERVAL '20 minutes'
          AND fk_branch_id = $3;;

    `;
    const params = [customerId, shopId, branch_id];
    try {
      await this.databaseService.query(sql, params);
      return true;
    } catch (error) {
      throw new Error('Failed to store analytics for customer');
    }
  }

  /**
   * Helper method to link customer to branch by phone number
   * Used by support chat methods to automatically link customers to branches
   * 
   * @param customerPhone - The customer's phone number
   * @param shopId - The shop's UUID
   * @param branchId - The branch's UUID to link to the customer
   * @param context - Context for error logging (e.g., 'chat transfer', 'support chat')
   */
  private async linkCustomerToBranchByPhone(
    customerPhone: string,
    shopId: string,
    branchId: string,
    context: string,
  ): Promise<void> {
    if (!customerPhone || !shopId || !branchId || branchId === 'null') {
      return;
    }

    try {
      // Get customer ID from phone number
      const customerQuery = `
        SELECT customer_id 
        FROM public."Customers" 
        WHERE phone_number = $1
      `;
      const customerResult = await this.databaseService.query(customerQuery, [customerPhone]);
      
      if (customerResult.length > 0) {
        const customerId = customerResult[0].customer_id;
        await this.ensureBranchLinkedToCustomer(customerId, shopId, branchId);
      }
    } catch (linkError) {
      console.error(`Error linking customer to branch during ${context}:`, linkError);
      // Continue with operation even if branch linking fails
    }
  }

  /**
   * Ensures that a customer is linked to a specific branch in the Customer_Shops table
   * This method will either create a new record or update existing one to include the branch
   * Called automatically when customers open cart or add items to cart
   * 
   * @param customerId - The customer's UUID
   * @param shopId - The shop's UUID
   * @param branchId - The branch's UUID to link to the customer
   */
  async ensureBranchLinkedToCustomer(
    customerId: string,
    shopId: string,
    branchId: string,
  ): Promise<void> {
    const sql = `
      INSERT INTO public."Customer_Shops" (
        fk_customer_id,
        fk_shop_id,
        branch_ids,
        broadcast
      ) VALUES ($1, $2, ARRAY[$3], true)
      ON CONFLICT (fk_customer_id, fk_shop_id)
      DO UPDATE SET
        branch_ids = CASE
          WHEN NOT EXISTS (
            SELECT 1
            FROM unnest("Customer_Shops".branch_ids) AS f
            WHERE f = $3
          ) THEN "Customer_Shops".branch_ids || ARRAY[$3]
          ELSE "Customer_Shops".branch_ids
        END
    `;

    const params = [customerId, shopId, branchId];

    try {
      await this.databaseService.query(sql, params);
    } catch (error) {
      console.error('Error ensuring branch is linked to customer:', error);
      throw new Error('Failed to link customer to branch');
    }
  }

  async uploadImage(shopId, folderName = 'listing-images', image) {
    try {
      const originalnameParts = image.originalname.split('.');
      const image_extension =
        originalnameParts.length > 1
          ? originalnameParts.pop().toLowerCase()
          : image.mimetype.split('/').pop();
      const fileName = `${process.env.STORAGE_FOLDER}/${folderName}/${shopId}/${Date.now()}.${image_extension}`;

      const uploadParams = {
        Bucket: 'cravin',
        Key: fileName,
        Body: image.buffer,
        ContentType: image.mimetype,
        PutObjectAcl: 'public-read',
      };

      const command = new PutObjectCommand(uploadParams);

      await s3Client.send(command);

      const item_image_link = `https://cravin.s3.me-central-1.amazonaws.com/${fileName}`;

      return item_image_link;
    } catch (error) {
      throw new Error('Failed to add image');
    }
  }
  // // add delete image api
  async deleteImage(imageUrl: string) {
    try {
      const key = imageUrl.split(
        'https://cravin.s3.me-central-1.amazonaws.com/',
      )[1];
      const deleteParams = {
        Bucket: 'cravin',
        Key: key,
      };

      const command = new DeleteObjectCommand(deleteParams);

      await s3Client.send(command);

      return true;
    } catch (error) {
      throw new Error('Failed to delete image');
    }
  }
  async logErrors(body: any) {
    try {
      fetch(process.env.WHATSAPP_URL + '/send-telegram-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          Product: 'Commerce',
          isError: true,
          ...body,
        }),
      });
      return true;
    } catch (error) {
      throw new Error('Failed to store analytics for customer');
    }
  }

  async sendReviewToTelegram(payload: any) {
    try {
      const { shopId, customerName, customerNumber, review } = payload;
      const sql = `
        SELECT shop_name
        FROM public."Shops"
        WHERE shop_id = $1
      `;
      const params = [shopId];
      const result = await this.databaseService.query(sql, params);
      if (result.length === 0) {
        return false;
      }
      const shopName = result[0]?.shop_name;
      const telMsg = `From ${customerName}\nMobile No - ${customerNumber}\nShop - ${shopName}\nReview: ${review}`;

      await this.sendTelegramMessage('-1002045209428', telMsg);
      return true;
    } catch (error) {
      throw new Error('Failed to store analytics for customer');
    }
  }

  async sendTelegramMessage(chatId: string, message: string): Promise<void> {
    const apiUrl = `${this.apiUrl}sendMessage`;
    const payload: any = {
      chat_id: chatId,
      text: message,
    };
    try {
      await axios.post(apiUrl, payload);
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to send Telegram message: ${error.message}`,
      );
    }
  }

    async storeMerchantAnalytics(
    shopId: string,
    body: {
      merchantId: string;
      merchantName: string;
      type: string;
      // typeOf?: string;
      branchId?: string;
      additionalInfo?: any;
    },
  ) {
    const {
      merchantId,
      merchantName,
      branchId = null,
      type,
      additionalInfo,
    } = body;
    const typeOf = '';
    if (merchantId !== 'null' && merchantId !== null) {
      // Insert the analytics data
      const sql = `
        INSERT INTO public."MerchantAnalytics" (
          fk_merchant_id, 
          merchant_name,
          type, 
          fk_shop_id,
          fk_branch_id,
          type_of,
          additional_info
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `;
      const params = [
        merchantId,
        merchantName,
        type,
        shopId,
        branchId,
        typeOf,
        additionalInfo,
      ];

      try {
        await this.databaseService.query(sql, params);
        return true;
      } catch (error) {
        throw new Error('Failed to store analytics for merchant');
      }
    }

    return false;
  }

  normalizeToNull(value: any): any {
    if (
      value === undefined ||
      value === null ||
      value === '' ||
      value === 'null' ||
      value === 'undefined'
    ) {
      return null;
    }
    return value;
  }
}

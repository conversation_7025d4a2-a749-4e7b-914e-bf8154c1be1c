import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsObject,
  IsArray,
  ValidateNested,
  IsNumber,
  IsOptional,
  IsBoolean,
  IsEnum,
  MinLength,
} from 'class-validator';
class BranchMenu {
  @ApiProperty({ description: 'Name of the menu' })
  @IsString()
  menuName: string;

  @ApiProperty({ description: 'Array of menu image URLs' })
  @IsArray()
  @IsString({ each: true })
  menuImages: string[];
}
class PaymentMethods {
  @ApiProperty({ description: 'Enable cash payment', default: true })
  @IsBoolean()
  @IsOptional()
  cash: boolean;

  @ApiProperty({ description: 'Enable card payment', default: false })
  @IsBoolean()
  @IsOptional()
  card: boolean;

  @ApiProperty({ description: 'Enable online payment', default: false })
  @IsBoolean()
  @IsOptional()
  online: boolean;
}

enum DayOfWeek {
  SUNDAY = 0,
  MONDAY = 1,
  TUESDAY = 2,
  WEDNESDAY = 3,
  THURSDAY = 4,
  FRIDAY = 5,
  SATURDAY = 6,
}

class DayTiming {
  @ApiProperty({
    enum: DayOfWeek,
    description: 'Day of the week (0 = Sunday, 6 = Saturday)',
  })
  @IsEnum(DayOfWeek)
  day: DayOfWeek;

  @ApiProperty({
    description: 'Opening time in 24-hour format (e.g., "07:00")',
    example: '07:00',
  })
  @IsString()
  start: string;

  @ApiProperty({
    description: 'Closing time in 24-hour format (e.g., "23:00")',
    example: '23:00',
  })
  @IsString()
  end: string;

  @ApiProperty({
    description: 'Whether the branch is open on this day',
    default: true,
  })
  @IsBoolean()
  status: boolean;
}

class Branch {
  @ApiProperty({ description: 'Unique identifier for the branch' })
  @IsString()
  branch_id: string;

  @ApiProperty({ description: 'Name of the branch' })
  @IsString()
  branch_name: string;

  @ApiProperty({
    description: 'Google Maps link for the branch location',
    required: false,
  })
  @IsString()
  @IsOptional()
  google_maps_link?: string;

  @ApiProperty({
    type: [DayTiming],
    description: 'Operating hours for each day of the week',
  })
  @ValidateNested({ each: true })
  @Type(() => DayTiming)
  @IsArray()
  timings: DayTiming[];

  // latitude
  @ApiProperty({ description: 'Latitude of the branch location' })
  @IsNumber()
  latitude: number;

  // longitude
  @ApiProperty({ description: 'Longitude of the branch location' })
  @IsNumber()
  longitude: number;
  // branch_address
  @ApiProperty({ description: 'Address of the branch' })
  @IsString()
  branch_address: string;

  // branch_delivery
  @ApiProperty({
    description: 'Enable delivery service for this branch',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  branch_delivery?: boolean;

  // branch_pickup
  @ApiProperty({
    description: 'Enable pickup service for this branch',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  branch_pickup?: boolean;

  // driver_module
  @ApiProperty({
    description: 'Enable driver module at branch level',
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  driver_module?: boolean;

  @ApiProperty({
    type: PaymentMethods,
    description: 'Accepted payment methods for this branch',
  })
  @IsObject()
  @ValidateNested()
  @Type(() => PaymentMethods)
  payment_methods: PaymentMethods;

  @ApiProperty({
    description: 'Minimum cart amount in local currency for this branch',
    minimum: 0,
    default: 0,
  })
  @IsNumber()
  @IsOptional()
  branch_min_cart_amount: number;

  @ApiProperty({
    description: 'Payment configuration object with any structure',
    type: 'object',
    additionalProperties: true,
    example: {
      payment_method: 'ccavenue',
      access_code: '34',
      merchant_id: 345,
      working_key: '543534',
    },
  })
  @IsObject()
  @IsOptional()
  payment_config: Record<string, any>;
}

class Shop {
  @ApiProperty({ description: 'Unique identifier for the shop' })
  @IsString()
  shop_id: string;

  @ApiProperty({ description: 'Name of the shop' })
  @IsString()
  shop_name: string;

  @ApiProperty({ description: 'Contact phone number' })
  @IsString()
  phone_number: string;

  @ApiProperty({ description: 'IS it only for listing' })
  @IsBoolean()
  is_only_for_listing: boolean;

  // Shop level toggle fields
  @ApiProperty({
    description: 'Access to give inbox module to merchant',
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  inbox_access_enabled?: boolean;

  @ApiProperty({
    description: 'Enable support functionality on whatsapp',
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  enable_support?: boolean;

  @ApiProperty({
    description: 'Auto accept order',
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  shop_auto_accept?: boolean;

  @ApiProperty({
    description: 'Enable pickup module at shop level',
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  pickup_module?: boolean;
}

class NewShop {
  @ApiProperty({ description: 'Unique identifier for the shop' })
  @IsString()
  shop_id: string;

  @ApiProperty({ description: 'Name of the shop' })
  @IsString()
  shop_name: string;

  @ApiProperty({ description: 'Contact phone number' })
  @IsString()
  phone_number: string;

  @ApiProperty({ description: 'IS it only for listing' })
  @IsBoolean()
  is_only_for_listing: boolean;

  // Shop level toggle fields
  @ApiProperty({
    description: 'Access to give inbox module to merchant',
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  inbox_access_enabled?: boolean;

  @ApiProperty({
    description: 'Enable support functionality on whatsapp',
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  enable_support?: boolean;

  @ApiProperty({
    description: 'Auto accept order',
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  shop_auto_accept?: boolean;

  @ApiProperty({
    description: 'Enable pickup module at shop level',
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  pickup_module?: boolean;
}
export class CreateShopOnboardingDto {
  @ApiProperty({ type: NewShop })
  @ValidateNested()
  @Type(() => NewShop)
  shop: NewShop;
}

export class UpdateShopBranchesDto {
  @ApiProperty({ type: Shop })
  @ValidateNested()
  @Type(() => Shop)
  shop: Shop;

  @ApiProperty({
    type: [Branch],
    description: 'List of branches to update or create',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Branch)
  branches: Branch[];
}

export class LandingPageDto {
  @ApiProperty()
  @IsString()
  branchId: string;

  @ApiProperty()
  @IsString()
  @MinLength(1)
  branch_emirate: string;

  @ApiProperty()
  @IsString()
  @MinLength(1)
  branch_display_name: string;

  @ApiProperty()
  @IsArray()
  @IsString({ each: true })
  branch_tags: string[];

  @ApiProperty()
  @IsArray()
  @IsString({ each: true })
  more_info: string[];

  @ApiProperty()
  @IsArray()
  @IsString({ each: true })
  popular_items: string[];

  @ApiProperty()
  @IsString()
  @MinLength(10)
  known_for: string;

  @ApiProperty()
  @IsBoolean()
  show_in_landing_page: boolean;

  @ApiProperty()
  @IsArray()
  @IsString({ each: true })
  image_gallery: string[];

  @ApiProperty()
  @IsString()
  @MinLength(1)
  listing_image: string;

  @ApiProperty({
    description: 'Average spend amount per customer',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  average_spend?: number;

  @ApiProperty()
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  shop_landline_numbers: string[];

  @ApiProperty()
  @IsObject()
  @IsOptional()
  social_links: Record<string, string>;

  @ApiProperty({
    description: 'Branch menus with name and images',
    type: 'array',
    example: [
      {
        menuName: 'Breakfast menu',
        menuImages: [
          'https://cravin.s3.me-central-1.amazonaws.com/CravinFood/listing-images/Cravin/1747387470423.jpg',
        ],
      },
    ],
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => BranchMenu)
  branch_menus: BranchMenu[];
}

import { sendWhatsAppMessage } from './send-wp-message';

export const sendOTP = async (
  from: string,
  shopWhatsAppToken: string,
  to: string,
  otp: number,
) => {
  try {
    const response = await sendWhatsAppMessage(
      from,
      to,
      shopWhatsAppToken,
      otp,
    );
    if (response) {
      return { success: true, otp };
    } else {
      return { success: false };
    }
  } catch (error) {
    return { success: false, error: 'Error sending OTP' };
  }
};

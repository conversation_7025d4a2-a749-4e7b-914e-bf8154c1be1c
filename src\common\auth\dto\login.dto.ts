import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsNumberString, IsOptional, IsString, Min<PERSON>ength, Matches } from 'class-validator';

export class CustomerLoginDto {
  @IsNotEmpty()
  @ApiProperty({ example: 'Cravin' })
  shop_id: string;

  @IsNotEmpty()
  @ApiProperty({ example: '971501234567' })
  username: string;
}

export class CustomerLoginOTPDto {
  @IsNotEmpty()
  @ApiProperty({ example: '971501234567' })
  username: string;

  @IsOptional()
  @ApiProperty({ example: "123456" })
  otp: any;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ example: true })
  encrypted: boolean;
}

export class MerchantLoginDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'commerce123' })
  username: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'demo123' })
  password: string;
}

export class BranchPasswordDto {
  @ApiProperty({
    description: 'Branch ID',
    example: 'branch123',
  })
  @IsString()
  @IsNotEmpty()
  branch_id: string;

  @ApiProperty({
    description: 'Branch name',
    example: 'Main Branch',
  })
  @IsString()
  @IsNotEmpty()
  branch_name: string;

  @ApiProperty({
    description: 'Branch username name',
    example: 'Main Branch',
  })
  @IsString()
  @IsNotEmpty()
  managerUsername: string;

  @ApiProperty({
    description: 'Branch password',
    example: 'Branch@123',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  @Matches(/[A-Z]/, { message: 'Password must contain at least one uppercase letter' })
  @Matches(/[a-z]/, { message: 'Password must contain at least one lowercase letter' })
  @Matches(/[0-9]/, { message: 'Password must contain at least one number' })
  @Matches(/[^A-Za-z0-9]/, { message: 'Password must contain at least one special character' })
  password: string;
}

export class UpdateMerchantPasswordDto {
  @ApiProperty({
    description: 'Shop ID',
    example: 'shop-123',
  })
  @IsString()
  shopId: string;

  @ApiProperty({
    description: 'Owner username',
    example: 'shop-123',
  })
  @IsString()
  ownerUsername: string;

  @ApiProperty({
    description: 'Owner password',
    example: 'Owner@123',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  @Matches(/[A-Z]/, { message: 'Password must contain at least one uppercase letter' })
  @Matches(/[a-z]/, { message: 'Password must contain at least one lowercase letter' })
  @Matches(/[0-9]/, { message: 'Password must contain at least one number' })
  @Matches(/[^A-Za-z0-9]/, { message: 'Password must contain at least one special character' })
  ownerPassword: string;

  @ApiProperty({
    description: 'Branch passwords',
    type: [BranchPasswordDto],
  })
  @IsNotEmpty()
  branchPasswords: BranchPasswordDto[];
}
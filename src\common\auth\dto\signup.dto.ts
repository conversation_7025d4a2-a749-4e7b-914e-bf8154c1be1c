import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsIn,
  IsNotEmpty,
  IsString,
} from 'class-validator';
import { MerchantLoginDto } from './login.dto';

export class SignupDto extends MerchantLoginDto {
  @ApiProperty()
  @IsString()
  shopId: string;

  @ApiProperty()
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  branchIds: string[];

  @IsString()
  @IsNotEmpty()
  @IsIn(['owner', 'manager', 'customer-service'])
  @ApiProperty({ example: 'owner' })
  userType: string;

  @IsString()
  @IsNotEmpty()
  @IsIn(['commerce'])
  @ApiProperty({ example: 'commerce' })
  product: string = 'commerce';
}

import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { compare, hash } from 'bcryptjs';
import * as CryptoJS from 'crypto-js';
import { CommonService } from 'src/modules/common/common.service';
import { v4 as uuidv4 } from 'uuid';
import { CustomerQueryService } from '../../modules/customers/customer-queries';
import { DatabaseService } from '../config/database.services';
import { sendOTP } from '../utilities/send-otp-message';
import { UpdateMerchantPasswordDto } from './dto/login.dto';
import { SignupDto } from './dto/signup.dto';
import { UpdatePasswordDto } from './dto/update-password.dto';

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly databaseService: DatabaseService,
    private readonly customerQueryService: CustomerQueryService,
    private readonly commonQueryService: CommonService,
  ) {}

  async signUp(signupData: SignupDto) {
    try {
      const existingUser = await this.databaseService.query(
        'SELECT * FROM "AuthMasterMerchant" WHERE username = $1',
        [signupData.username],
      );

      if (existingUser.length > 0) {
        throw new ConflictException(
          'User already exists, kindly login with the credentials',
        );
      }

      const hashedPassword = await hash(signupData.password, 10);

      const newUser = await this.databaseService.query(
        'INSERT INTO "AuthMasterMerchant" (username, password, shop_id, branch_ids, user_type, product) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *',
        [
          signupData.username,
          hashedPassword,
          signupData.shopId,
          signupData.branchIds,
          signupData.userType,
          signupData.product,
        ],
      );

      return newUser;
    } catch (error) {
      throw error;
    }
  }

  async getShopWhatsAppDetails(shopId: string): Promise<any> {
    try {
      const result = await this.databaseService.query(
        `SELECT shop_whatsapp_id AS "whatsAppId",
        shop_whatsapp_token AS "whatsAppToken"
        FROM public."Shops"
        WHERE shop_id = $1`,
        [shopId],
      );

      return result[0];
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch shop WhatsApp data',
      );
    }
  }

  async generateAndSaveOTP(
    whatsAppId: string,
    whatsAppToken: string,
    phoneNumber: string,
    shopId?: string,
  ): Promise<{
    messaged: boolean;
    otp?: number;
  }> {
    try {
      const userMessagedQuery = `
        SELECT id
        FROM
          public."UserDataCache" u
        JOIN
          public."Shops" s ON s.shop_whatsapp_id = u.shop_phone_number 
        WHERE
          s.shop_whatsapp_id = $1
        AND
          u.phone_number = $2
        AND
          modified_at >= NOW() - INTERVAL '60 minutes';
       `;
      const checkUserMessaged = await this.databaseService.query(
        userMessagedQuery,
        [whatsAppId, phoneNumber],
      );

      if (checkUserMessaged.length > 0) {
        const otp = Math.floor(100000 + Math.random() * 900000);
        const hashedOTP = await hash(`${otp}`, 10);

        const userInfo = await this.databaseService.query(
          `INSERT INTO "AuthMasterCustomer" (username, otp) VALUES ($1, $2)
       ON CONFLICT (username) DO UPDATE SET otp = EXCLUDED.otp
       RETURNING fk_customer_id`,
          [phoneNumber, hashedOTP],
        );

        await sendOTP(whatsAppId, whatsAppToken, phoneNumber, otp);
        this.commonQueryService.createAnalyticsTag(shopId, {
          customerId: userInfo[0].fk_customer_id,
          type: 'OTP_GENERATED',
        });
        if (process.env.ENV === 'production') {
          return { messaged: true };
        }
        return { messaged: true, otp };
      } else {
        return { messaged: false };
      }
    } catch (error) {
      throw new InternalServerErrorException('Failed to generate OTP');
    }
  }

  async verifyOTP(
    phone_number: string,
    enteredOTP: any,
  ): Promise<{
    isOTPValid: boolean;
    customer_id?: string;
    customer_name?: string;
    error?: string;
  }> {
    const storedHashedOTP = await this.databaseService.query(
      'SELECT fk_customer_id, otp FROM "AuthMasterCustomer" WHERE username = $1',
      [phone_number],
    );

    if (!storedHashedOTP.length) {
      return { isOTPValid: false, error: 'Generate an OTP first' };
    }

    const customer =
      await this.customerQueryService.getCustomerByPhoneNumber(phone_number);

    if (!customer?.customer_name) {
      delete customer?.customer_name;
    }

    let customerId = customer?.customer_id;
    if (!customerId) {
      customerId = await this.customerQueryService.addCustomer({
        name: null,
        phone_number,
      });
    }

    await this.databaseService.query(
      `INSERT INTO "AuthMasterCustomer" (username, fk_customer_id) VALUES ($1, $2)
        ON CONFLICT (username) DO UPDATE SET fk_customer_id = EXCLUDED.fk_customer_id`,
      [phone_number, customerId],
    );

    if (storedHashedOTP && storedHashedOTP.length > 0) {
      const isOTPValid = await compare(`${enteredOTP}`, storedHashedOTP[0].otp);
      return {
        isOTPValid,
        customer_id: customerId,
        customer_name: customer?.customer_name,
      };
    }

    return { isOTPValid: false };
  }

  async verifyToken(token: string) {
    const payload = await this.jwtService.verifyAsync(token, {
      secret: process.env.JWT_SECRET,
    });
    if (payload) {
      return payload;
    }
    return null;
  }

  async login(user: any, userType: 'customer' | 'merchant') {
    if (userType === 'customer') {
      const query = `SELECT * FROM "AuthMasterCustomer" WHERE username = $1`;
      const result = await this.databaseService.query(query, [user.username]);
      const customerQuery = `SELECT * FROM "Customers" WHERE phone_number = $1`;
      const customerResult = await this.databaseService.query(customerQuery, [
        user.username,
      ]);

      if (result && result.length > 0) {
        const user = result[0];
        const customer = customerResult[0];

        return {
          access_token: this.jwtService.sign(
            { userType, userId: result[0].user_id },
            {
              secret: process.env.JWT_SECRET,
            },
          ),
          customer_id: customer.customer_id,
          customer_name: customer.customer_name,
          phone_number: user.username,
        };
      } else {
        throw new InternalServerErrorException('Login again using OTP');
      }
    } else if (userType === 'merchant') {
      const query = `
        SELECT 
          a.user_id, 
          a.username, 
          a.shop_id, 
          a.user_type, 
          json_agg(json_build_object('branch_id', b.branch_id, 'branch_name', b.branch_name)) as branches,
          a.product
        FROM public."AuthMasterMerchant" a
        LEFT JOIN public."ShopBranches" b ON b.branch_id = ANY(a.branch_ids)
        WHERE a.username = $1
        GROUP BY a.user_id, a.username, a.shop_id, a.user_type, a.product
      `;

      const result = await this.databaseService.query(query, [user.username]);

      if (result && result.length > 0 && result[0].product === 'commerce') {
        const subscriptionStatus = await this.getSubscriptionStatusForCommerce(
          result[0].shop_id,
        );
        if (!subscriptionStatus) {
          throw new ForbiddenException('Subscription inactive');
        }
        return {
          access_token: this.jwtService.sign(
            {
              userType: result[0].user_type,
              userId: result[0].user_id,
              shopId: result[0].shop_id,
              branches: result[0].branches,
            },
            { secret: process.env.JWT_SECRET },
          ),
          user_id: result[0].user_id,
          user_type: result[0].user_type,
          username: user.username,
          shop_id: result[0].shop_id,
          branches: result[0].branches,
          device_id: uuidv4(),
        };
      } else {
        throw new InternalServerErrorException('User not found');
      }
    } else {
      throw new InternalServerErrorException('Unknown user type');
    }
  }

  async validateUser(username: string, password: string) {
    try {
      const query = `SELECT user_id, user_type, product FROM public."AuthMasterMerchant" WHERE username = $1`;
      const result = await this.databaseService.query(query, [username]);

      if (result && result.length > 0 && result[0].product === 'commerce') {
        const user = await this.getUserByUserId({
          userId: result[0].user_id,
          userType: result[0].user_type,
        });

        const isPasswordValid = await this.verifyPassword(
          password,
          user?.password,
        );

        if (user && isPasswordValid) {
          user.password = undefined;
          return user;
        } else {
          return null;
        }
      } else {
        throw new NotFoundException('User Not Found');
      }
    } catch (error) {
      throw new BadRequestException('Wrong credentials provided');
    }
  }

  async getUserByUserId(payload: any): Promise<any> {
    try {
      const userType = payload.userType;
      const userId = payload.userId;
      if (userType === 'customer') {
        const query = 'SELECT * FROM "AuthMasterCustomer" WHERE user_id = $1';
        const result = await this.databaseService.query(query, [userId]);
        return result[0];
      } else if (
        userType === 'owner' ||
        userType === 'manager' ||
        userType === 'customer-service'
      ) {
        const query = 'SELECT * FROM "AuthMasterMerchant" WHERE user_id = $1';
        const result = await this.databaseService.query(query, [userId]);
        return result[0];
      } else {
        throw new BadRequestException('Invalid user type');
      }
    } catch (error) {
      throw new NotFoundException('User Not Found');
    }
  }

  async getUserDetailsForTracking(userId: string): Promise<any> {
    try {
      const query = `
      SELECT 
        amm.username,
        amm.shop_id,
        b.branch_id,
        b.branch_name
      FROM 
        "AuthMasterMerchant" amm
      JOIN 
        LATERAL unnest(amm.branch_ids) AS bid(branch_id) ON TRUE
      JOIN 
        "ShopBranches" b ON b.branch_id = bid.branch_id
      WHERE 
        amm.user_id = $1`;
      const result = await this.databaseService.query(query, [userId]);
      return result[0];
    } catch (error) {
      throw new NotFoundException('User Not Found');
    }
  }

  async getAllUsersByShop(shopId: string): Promise<any[]> {
    try {
      const query = `
        SELECT 
          amm.user_id,
          amm.username,
          amm.shop_id,
          b.branch_id,
          b.branch_name
        FROM 
          "AuthMasterMerchant" amm
        JOIN 
          LATERAL unnest(amm.branch_ids) AS bid(branch_id) ON TRUE
        JOIN 
          "ShopBranches" b ON b.branch_id = bid.branch_id
        WHERE 
          amm.shop_id = $1
        AND amm.user_type != 'owner'
        ORDER BY 
          b.branch_name ASC
      `;

      const result = await this.databaseService.query(query, [shopId]);
      return result;
    } catch (error) {
      throw new NotFoundException('Shop not found');
    }
  }

  async verifyPassword(password: string, hashedPassword: string) {
    const isPasswordMatching = await compare(password, hashedPassword);
    return isPasswordMatching;
  }
  async getSubscriptionStatusForCommerce(shopId: string): Promise<boolean> {
    try {
      const query = `
        SELECT subscription_status, stripe_subscription_id, stripe_customer_id
        FROM "Shops"
        WHERE shop_id = $1
      `;
      const result = await this.databaseService.query(query, [shopId]);

      if (result && result.length > 0) {
        const {
          subscription_status,
          stripe_subscription_id,
          stripe_customer_id,
        } = result[0];

        if (
          (!stripe_subscription_id || stripe_subscription_id.trim() === '') &&
          (!stripe_customer_id || stripe_customer_id.trim() === '')
        ) {
          return true;
        }
        return subscription_status;
      }
      return false;
    } catch (error) {
      throw new InternalServerErrorException(
        'Error fetching subscription status',
      );
    }
  }
  async decryptAuthHash(encryptedData) {
    try {
      const key = CryptoJS.enc.Base64.parse(
        CryptoJS.SHA256(process.env.ORDER_URL_JWT_SECRET).toString(
          CryptoJS.enc.Base64,
        ),
      );
      const decrypted = CryptoJS.AES.decrypt(encryptedData, key, {
        mode: CryptoJS.mode.ECB,
      });
      return decrypted.toString(CryptoJS.enc.Utf8); // Decode to UTF-8 string
    } catch (error) {
      console.error('Error during decryption:', error);
    }
  }

  async createMerchantAccounts(updatePasswordDto: UpdateMerchantPasswordDto) {
    try {
      const { shopId, ownerPassword, branchPasswords, ownerUsername } =
        updatePasswordDto;

      // Hash owner password
      const hashedOwnerPassword = await hash(ownerPassword, 10);

      const ownerResult = await this.databaseService.query(
        'INSERT INTO "AuthMasterMerchant" (username, password, shop_id, branch_ids, user_type, created_at,product) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, $6) RETURNING user_id',
        [
          ownerUsername,
          hashedOwnerPassword,
          shopId,
          branchPasswords.map((b) => b.branch_id),
          'owner',
          'commerce',
        ],
      );

      // Create branch accounts
      const branchResults = await Promise.all(
        branchPasswords.map(async (branch) => {
          const hashedBranchPassword = await hash(branch.password, 10);
          const branchUsername = branch.managerUsername;

          const result = await this.databaseService.query(
            'INSERT INTO "AuthMasterMerchant" (username, password, shop_id, branch_ids, user_type, created_at,product) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, $6) RETURNING user_id',
            [
              branchUsername,
              hashedBranchPassword,
              shopId,
              [branch.branch_id],
              'manager',
              'commerce',
            ],
          );

          return {
            branch_id: branch.branch_id,
            branch_name: branch.branch_name,
            user_id: result[0].user_id,
          };
        }),
      );

      return {
        message: 'Merchant accounts created successfully',
        owner: {
          user_id: ownerResult[0].user_id,
          username: ownerUsername,
        },
        branches: branchResults,
      };
    } catch (error) {
      if (error.constraint === 'unique_username') {
        throw new ConflictException('Username already exists');
      }
      throw new InternalServerErrorException(
        'Failed to create merchant accounts',
      );
    }
  }

  async getShopLogins(shopId: string) {
    try {
      const query =
        'SELECT  username, shop_id, branch_ids, user_type FROM "AuthMasterMerchant" WHERE shop_id = $1';
      const result = await this.databaseService.query(query, [shopId]);
      return result;
    } catch (error) {
      console.error('Error retrieving shop logins:', error);
      throw new InternalServerErrorException('Failed to retrieve shop logins');
    }
  }

  async updatePassword(updatePasswordDto: UpdatePasswordDto) {
    try {
      const { shopId, password, branch_id, newBranch } = updatePasswordDto;

      if (updatePasswordDto?.staffUsername) {
        const username = updatePasswordDto?.staffUsername;
        /**staff user with user_type counter-staff */
        const newStaff = updatePasswordDto?.newStaff;
        if (newStaff) {
          // create new account
          const hashedStaffPassword = await hash(password, 10);
          const query = `INSERT INTO "AuthMasterMerchant" (username, password, shop_id, branch_ids, user_type, created_at, product)
           VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, 'commerce') RETURNING user_id`;
          const result = await this.databaseService.query(query, [
            username,
            hashedStaffPassword,
            shopId,
            [branch_id],
            'counter-staff',
          ]);
          return {
            success: true,
            message: 'Password updated successfully',
            user_id: result[0].user_id,
            username: username,
          };
        } else {
          // update existing account
          const hashedStaffPassword = await hash(password, 10);
          const query = `UPDATE "AuthMasterMerchant" SET password = $1 WHERE username = $2 RETURNING user_id`;
          const result = await this.databaseService.query(query, [
            hashedStaffPassword,
            username,
          ]);
          return {
            success: true,
            message: 'Password updated successfully',
            user_id: result[0].user_id,
            username: username,
          };
        }
      }
      // Determine if it's a branch or owner update
      let username: string;
      if (branch_id) {
        username = updatePasswordDto.managerUsername;
      } else {
        username = updatePasswordDto.ownerUsername;
      }

      // Handle new branch creation
      if (newBranch && branch_id) {
        const hashedBranchPassword = await hash(password, 10);
        const query = `INSERT INTO "AuthMasterMerchant" (username, password, shop_id, branch_ids, user_type, created_at, product) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, 'commerce') RETURNING user_id`;
        const result = await this.databaseService.query(query, [
          username,
          hashedBranchPassword,
          shopId,
          [branch_id],
          'manager',
        ]);
        return {
          success: true,
          message: 'Password updated successfully',
          user_id: result[0].user_id,
          username: username,
        };
      }

      // Get current user
      const user = await this.databaseService.query(
        'SELECT * FROM "AuthMasterMerchant" WHERE username = $1',
        [username],
      );

      if (!user || user.length === 0) {
        throw new NotFoundException('User not found');
      }

      // Hash new password
      const hashedPassword = await hash(password, 10);

      // Update password directly without checking current password
      await this.databaseService.query(
        'UPDATE "AuthMasterMerchant" SET password = $1 WHERE username = $2',
        [hashedPassword, username],
      );

      return {
        success: true,
        message: 'Password updated successfully',
      };
    } catch (error) {
      console.log('🚀 ~ AuthService ~ updatePassword ~ error:', error);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update password');
    }
  }

  async checkUsername(username: string) {
    const user = await this.databaseService.query(
      'SELECT * FROM "AuthMasterMerchant" WHERE username = $1',
      [username],
    );

    return user.length > 0;
  }
}

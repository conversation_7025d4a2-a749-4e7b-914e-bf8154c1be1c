import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/auth/guards/jwt-auth.guard';
import { Public } from '../../common/decorators/public.decorator';
import { Address } from '../branches/dto/branches.dto';
import { ShopsService } from '../shops/shops.service';
import { CustomerQueryService } from './customer-queries';
import { CustomersService } from './customers.service';
import { CustomerOrderReview } from './dto/customers.dto';

@Controller('customers')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('access-token')
@ApiTags('Customers')
export class CustomersController {
  constructor(
    private readonly customersService: CustomersService,
    private readonly customerQueryService: CustomerQueryService,
    private readonly shopService: ShopsService,
  ) {}

  @Post(':customerId/addresses/:addressTag')
  @ApiOperation({ summary: 'Add or update a customer address' })
  @ApiBadRequestResponse({ description: 'Invalid customer ID or address tag' })
  async addOrUpdateCustomerAddress(
    @Param('customerId') customerId: string,
    @Param('addressTag') addressTag: string,
    @Body() address: Address,
  ): Promise<any[]> {
    try {
      const response = await this.customersService.addCustomerAddress(
        customerId,
        addressTag,
        address,
      );
      return response;
    } catch (error) {
      throw new Error('Failed to add or update customer address');
    }
  }

  @Delete(':customerId/addresses/:addressTag')
  @ApiOperation({ summary: 'Delete a customer address tag' })
  @ApiBadRequestResponse({ description: 'Invalid customer ID or address tag' })
  async deleteCustomerAddressTag(
    @Param('customerId') customerId: string,
    @Param('addressTag') addressTag: string,
  ): Promise<void> {
    await this.customersService.deleteCustomerAddressTag(
      customerId,
      addressTag,
    );
  }

  @Public()
  @Get('addresses')
  @ApiOperation({ summary: 'Get customer addresses by ID' })
  @ApiQuery({ name: 'customerId', required: false })
  @ApiNotFoundResponse({ description: 'Customer not found' })
  async getCustomerAddresses(
    @Query('customerId') customerId: string,
  ): Promise<string[]> {
    try {
      if (customerId) {
        return await this.customersService.getCustomerAddresses(customerId);
      } else {
        return [];
      }
    } catch (error) {
      throw new Error('Failed to get customer addresses');
    }
  }

  @Patch(':customerId/favourite-address-tag')
  @ApiOperation({ summary: 'Update customer favourite address tag by ID' })
  @ApiBody({ description: 'Favourite address tag', type: String })
  async updateCustomerFavouriteAddressTag(
    @Param('customerId') customerId: string,
    @Body('favouriteAddressTag') favouriteAddressTag: string,
  ): Promise<void> {
    try {
      await this.customersService.updateFavouriteAddressTag(
        customerId,
        favouriteAddressTag,
      );
    } catch (error) {
      throw new Error('Failed to update customer favourite address');
    }
  }

  @Get(':shopId')
  @ApiOperation({ summary: 'Get all customers for a shop by filter' })
  @ApiNotFoundResponse({ description: 'Customer not found' })
  @ApiQuery({
    name: 'filter',
    enum: ['all', 'active', 'potential', 'opencart'],
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'branchId',
    required: false,
    type: String,
  })
  async getAllShopCustomers(
    @Param('shopId') shopId: string,
    @Query('filter') filter?: string,
    @Query('branchId') branchId?: string,
  ): Promise<any[]> {
    try {
      let branchIds: string[] = [];
      let allowNull: boolean = false;

      if (branchId) {
        branchIds = branchId.includes(',') ? branchId.split(',') : [branchId];
      } else {
        const branches = await this.shopService.getBranchesByShopId(shopId);
        branchIds = branches.map((branch) => branch.branch_id);
        allowNull = true;
      }

      return await this.customersService.getAllCustomers(
        shopId,
        branchIds,
        filter || 'all',
        allowNull,
      );
    } catch (error) {
      throw new Error('Failed to get customers list');
    }
  }

  @Get('latest-order/:shopId/:customerId')
  @ApiOperation({ summary: 'Get latest order for a specific customer' })
  @ApiResponse({
    status: 200,
    description: 'Customer latest order retrieved successfully',
  })
  @ApiParam({
    name: 'shopId',
    type: 'string',
    description: 'The ID of the shop',
  })
  @ApiParam({
    name: 'customerId',
    type: 'string',
    description: 'The ID of the customer',
  })
  @ApiQuery({
    name: 'orderId',
    type: 'string',
    description: 'The ID of the order',
    required: false,
  })
  async getCustomerLatestOrder(
    @Param('shopId') shopId: string,
    @Param('customerId') customerId: string,
    @Query('orderId') orderId?: string | null,
  ): Promise<object[]> {
    try {
      const order = await this.customersService.getCustomerLatestOrder(
        shopId,
        customerId,
        orderId,
      );
      return order;
    } catch (error) {
      throw new Error('Unable to fetch customer latest order');
    }
  }

  @Post('order-review')
  @ApiOperation({
    summary: 'Add a review for an order for a specific customer',
  })
  @ApiResponse({
    status: 201,
    description: 'Customer order review added successfully',
  })
  async addCustomerOrderReview(
    @Body() orderReview: CustomerOrderReview,
  ): Promise<void> {
    try {
      await this.customersService.addCustomerOrderReview(orderReview);
    } catch (error) {
      throw new Error('Unable to add customer order review');
    }
  }

  @Get('orders/:shop_id/:customer_id')
  @ApiOperation({ summary: 'Get order history for a specific customer' })
  @ApiResponse({
    status: 200,
    description: 'Customer order history retrieved successfully',
  })
  @ApiParam({
    name: 'shop_id',
    type: 'string',
    description: 'The ID of the shop',
  })
  @ApiParam({
    name: 'customer_id',
    type: 'string',
    description: 'The ID of the customer',
  })
  @ApiQuery({
    name: 'branch_id',
    description: 'The ID of the branch',
    type: 'string',
    required: false,
  })
  async getCustomerOrderHistory(
    @Param('shop_id') shop_id: string,
    @Param('customer_id') customer_id: string,
    @Query('branch_id') branch_id?: string,
  ): Promise<object[]> {
    try {
      const orders = await this.customersService.getCustomerOrderHistory(
        shop_id,
        customer_id,
        branch_id,
      );
      return orders;
    } catch (error) {
      throw new Error('Unable to fetch customer order history');
    }
  }

  @Get('discounts/:shop_id/:branch_id')
  @ApiOperation({ summary: 'Get discounts for a specific branch' })
  @ApiResponse({
    status: 200,
    description: 'Discounts retrieved successfully',
  })
  @ApiParam({
    name: 'shop_id',
    type: 'string',
    description: 'The ID of the shop',
  })
  @ApiParam({
    name: 'branch_id',
    type: 'string',
    description: 'The ID of the branch',
  })
  @ApiQuery({
    name: 'customer_id',
    description: 'The ID of the customer',
    type: 'string',
  })
  @ApiQuery({
    name: 'date',
    description: 'The date for the discount in DD-MM-YYYY format',
    type: 'string',
  })
  @ApiQuery({
    name: 'promo_code',
    description: 'The promo code for the discount',
    type: 'string',
    required: false,
  })
  async getDiscounts(
    @Param('shop_id') shop_id: string,
    @Param('branch_id') branch_id: string,
    @Query('customer_id') customer_id: string,
    @Query('date') date: string,
    @Query('service_type') service_type: string,
    @Query('promo_code') promo_code?: string,
  ): Promise<object[]> {
    try {
      const discounts = await this.customersService.getDiscounts({
        shop_id,
        branch_id,
        customer_id,
        date,
        service_type,
        promo_code,
      });
      return discounts;
    } catch (error) {
      throw new Error('Unable to fetch discounts');
    }
  }

  @Public()
  @Post('deliverable-branches/:shopId')
  @ApiOperation({ summary: 'Get deliverable zones for customer' })
  @ApiResponse({
    status: 200,
    description: 'Delivery zones retrieved for customer successfully',
  })
  @ApiParam({ name: 'shopId', description: 'Shop ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        latitude: {
          type: 'number',
        },
        longitude: {
          type: 'number',
        },
        service_type: {
          type: 'string',
        },
      },
      required: ['latitude', 'longitude'],
    },
  })
  async getDeliverableZones(
    @Param('shopId') shopId: string,
    @Body()
    body: { latitude: number; longitude: number; service_type?: string },
  ): Promise<any> {
    try {
      const response = await this.customersService.getDeliverableBranches(
        shopId,
        body,
      );
      return response;
    } catch (error) {
      throw new Error('Failed to get deliverable zones for customer');
    }
  }

  @Post('delivery/:shopId/:branchId/:customerId')
  @ApiOperation({ summary: 'Get delivery details for customer addresses' })
  @ApiResponse({
    status: 200,
    description: 'Delivery details retrieved for customer successfully',
  })
  @ApiParam({ name: 'shopId', description: 'Shop ID' })
  @ApiParam({ name: 'branchId', description: 'Branch ID' })
  @ApiParam({ name: 'customerId', description: 'Customer ID' })
  @ApiQuery({ name: 'serviceType', description: 'Service type' })
  async getDeliveryAmount(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Param('customerId') customerId: string,
    @Query('serviceType') serviceType: string,
  ): Promise<any> {
    try {
      const response = await this.customersService.getDeliveryDetails(
        shopId,
        branchId,
        customerId,
        serviceType,
      );
      return response;
    } catch (error) {
      throw new Error('Failed to get delivery details for customer addresses');
    }
  }

  @Post()
  @ApiOperation({
    summary: 'Edit customer name in the customer Shop table',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        customerId: {
          type: 'string',
        },
        shopId: {
          type: 'string',
        },
        newCustomerName: {
          type: 'string',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Customer name updated successfully',
  })
  async editCustomerName(
    @Body()
    editCustomerDto: {
      customerId: string;
      shopId: string;
      newCustomerName: string;
    },
  ): Promise<{ message: string }> {
    const { customerId, shopId, newCustomerName } = editCustomerDto;
    await this.customersService.editCustomerName(
      customerId,
      shopId,
      newCustomerName,
    );
    return { message: 'Customer name updated successfully' };
  }

  @Get('get-customer-profile-details/:customer_id')
  @ApiOperation({ summary: 'Get profile details for a specific customer' })
  @ApiResponse({
    status: 200,
    description: 'Customer profile details retrieved successfully',
  })
  @ApiParam({
    name: 'customer_id',
    type: 'string',
    description: 'The ID of the customer',
  })
  async getCustomerInfo(@Param('customer_id') customer_id: string) {
    try {
      const data = await this.customersService.getCustomerInfo(customer_id);
      if (data.length > 0) {
        return data[0];
      }
      return data;
    } catch (error) {
      throw new Error('Unable to fetch customer profile details');
    }
  }

  @Post('update-customer-profile-details')
  @ApiOperation({ summary: 'Update Customer profile details' })
  @ApiResponse({
    status: 200,
    description: 'Customer profile details updated successfully',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        customerId: {
          type: 'string',
        },
        customerEmail: {
          type: 'string',
        },
        customerName: {
          type: 'string',
        },
        gender: {
          type: 'string',
        },
        nationality: {
          type: 'string',
        },
      },
    },
  })
  async updateCustomerProfile(
    @Body()
    editCustomerDto: {
      customerId: string;
      customerName: string;
      customerEmail: string;
      gender: string;
      nationality: string;
    },
  ): Promise<any> {
    try {
      const response =
        await this.customersService.updateCustomerProfile(editCustomerDto);
      return response;
    } catch (error) {
      throw new Error('Failed to get delivery details for customer addresses');
    }
  }

  @Public()
  @Post('current-time')
  @ApiQuery({
    name: 'branchId',
    required: false,
    type: String,
    description:
      'The branch ID to get the timezone for. If not provided, a default timezone will be used.',
  })
  @ApiQuery({
    name: 'timezone',
    required: false,
    type: String,
    description:
      'The timezone to use. Defaults to "Asia/Dubai" if not provided.',
    example: 'Asia/Dubai',
  })
  async getTimeForBranch(
    @Query('branchId') branchId: string,
    @Query('timezone') timezone: string = 'Asia/Dubai',
  ): Promise<{ time: string; offset: number }> {
    return this.customerQueryService.getCurrentTimeForBranch(
      branchId,
      timezone,
    );
  }
}

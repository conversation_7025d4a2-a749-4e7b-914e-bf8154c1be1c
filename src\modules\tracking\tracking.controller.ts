import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/auth/guards/jwt-auth.guard';
import { Public } from '../../common/decorators/public.decorator';
import { Roles } from '../../common/decorators/roles.decorator';
import { UserTrackingService } from '../../common/websocket/user-tracking.service';
import { CustomWebSocketGateway } from '../../common/websocket/websocket.gateway';
import { TrackingCodeDto } from './dto/tracking-code.dto';
import { TrackingService } from './tracking.service';

@ApiTags('Tracking')
@Controller('tracking')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('access-token')
export class TrackingController {
  constructor(
    private readonly trackingService: TrackingService,
    private readonly userTrackingService: UserTrackingService,
    private readonly webSocketGateway: CustomWebSocketGateway,
  ) {}

  @Post(':shopId')
  @Roles('merchant')
  @ApiOperation({ summary: 'Save analytics tracking code for shop' })
  async saveTrackingCode(
    @Param('shopId') shopId: string,
    @Body() trackingCode: TrackingCodeDto,
  ): Promise<void> {
    await this.trackingService.saveTrackingCode(shopId, trackingCode);
  }

  @Get(':shopId')
  @ApiOperation({ summary: 'Get all tracking codes for shop' })
  async getTrackingCodes(@Param('shopId') shopId: string): Promise<any[]> {
    return await this.trackingService.getTrackingCodes(shopId);
  }

  @Delete(':shopId/:trackingId')
  @Roles('merchant')
  @ApiOperation({ summary: 'Delete tracking code by ID' })
  @ApiResponse({
    status: 200,
    description: 'Tracking code deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Tracking code not found' })
  async deleteTrackingCode(
    @Param('shopId') shopId: string,
    @Param('trackingId') trackingId: string,
  ): Promise<void> {
    await this.trackingService.deleteTrackingCode(shopId, trackingId);
  }

  @Put(':shopId/:trackingId/toggle')
  @Roles('merchant')
  @ApiOperation({ summary: 'Toggle tracking code active status' })
  @ApiResponse({
    status: 200,
    description: 'Tracking code status toggled successfully',
  })
  @ApiResponse({ status: 404, description: 'Tracking code not found' })
  async toggleTrackingCode(
    @Param('shopId') shopId: string,
    @Param('trackingId') trackingId: string,
    @Body('is_active') isActive: boolean,
  ): Promise<void> {
    await this.trackingService.toggleTrackingCode(shopId, trackingId, isActive);
  }

  @Public()
  @Get(':shopId/active')
  @ApiOperation({ summary: 'Get all active tracking codes' })
  async getActiveTrackingCodes(
    @Param('shopId') shopId: string,
  ): Promise<any[]> {
    return await this.trackingService.getActiveTrackingCodes(shopId);
  }

  @Get('user/:userId/activity')
  @ApiOperation({ summary: 'Get specific user activity' })
  @ApiResponse({ status: 200, description: 'User activity details' })
  async getUserActivity(@Param('userId') userId: string) {
    const activity = await this.userTrackingService.getUserActivity(userId);
    return { success: true, data: activity };
  }

  @Post('notify/:userId')
  @ApiOperation({ summary: 'Send notification to specific user' })
  @ApiResponse({ status: 200, description: 'Notification sent' })
  async notifyUser(@Param('userId') userId: string, @Req() req: any) {
    await this.webSocketGateway.notifyUser(userId, 'notification', {
      message: 'You have a new notification',
      from: req.user.sub || req.user.userId,
      timestamp: new Date(),
    });

    return { success: true, message: `Notification sent to user ${userId}` };
  }
}

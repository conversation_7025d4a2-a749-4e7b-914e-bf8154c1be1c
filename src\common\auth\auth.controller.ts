import {
  BadRequestException,
  Body,
  ConflictException,
  Controller,
  Get,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiForbiddenResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { Public } from '../decorators/public.decorator';
import { AuthService } from './auth.service';
import {
  CustomerLoginDto,
  CustomerLoginOTPDto,
  MerchantLoginDto,
  UpdateMerchantPasswordDto,
} from './dto/login.dto';
import { SignupDto } from './dto/signup.dto';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { OTPAuthGuard } from './guards/otp-auth.guard';
import { UpdatePasswordDto } from './dto/update-password.dto';

@ApiTags('Auth')
@Controller('auth')
@Public()
export class AuthController {
  constructor(private readonly authService: AuthService) { }

  @Post('signup')
  @ApiOperation({ summary: 'Sign up a new owner/manager' })
  @ApiCreatedResponse({
    description: 'Owner/manager created successfully',
  })
  @ApiConflictResponse({
    description: 'Owner/manager with the provided email already exists',
  })
  @ApiBadRequestResponse({ description: 'Invalid request data' })
  @ApiBody({ type: SignupDto })
  async signUp(@Body() signupData: SignupDto) {
    const createdUser = await this.authService.signUp(signupData);
    return createdUser;
  }

  @Post('customer/login')
  @ApiOperation({ summary: 'Log in with customer credentials' })
  async customerLogin(@Body() loginData: CustomerLoginDto) {
    try {
      const { whatsAppId, whatsAppToken } =
        await this.authService.getShopWhatsAppDetails(loginData.shop_id);
      const response = await this.authService.generateAndSaveOTP(
        whatsAppId,
        whatsAppToken,
        loginData.username,
        loginData.shop_id,
      );
      return response;
    } catch (error) {
      throw new ConflictException('Invalid credentials');
    }
  }

  @UseGuards(OTPAuthGuard)
  @Post('customer/login/OTP')
  @ApiOperation({ summary: 'Log in with customer credentials and OTP' })
  @ApiCreatedResponse({
    description: 'Customer logged in successfully',
  })
  @ApiConflictResponse({ description: 'Invalid OTP' })
  async customerOTPLogin(@Body() loginData: CustomerLoginOTPDto) {
    if (loginData.otp) {
      if (loginData.encrypted || loginData.otp.toString().length === 6) {
        let phoneNumber = loginData.username;
        let otp = loginData.otp;
        if (loginData.encrypted) {
          phoneNumber = await this.authService.decryptAuthHash(
            decodeURIComponent(loginData.username),
          );
          otp = await this.authService.decryptAuthHash(
            decodeURIComponent(loginData.otp.toString()),
          );
        }

        const isOTPValid = await this.authService.verifyOTP(phoneNumber, otp);

        if (isOTPValid.isOTPValid) {
          const result = await this.authService.login(
            {
              username: phoneNumber,
              userId: isOTPValid.customer_id,
              customer_name: isOTPValid.customer_name,
            },
            'customer',
          );
          return result;
        } else {
          throw new ConflictException(
            isOTPValid.error ? `Error: ${isOTPValid.error}` : 'Invalid OTP',
          );
        }
      } else {
        throw new BadRequestException('Invalid OTP length');
      }
    } else {
      throw new BadRequestException('OTP is required');
    }
  }

  @UseGuards(LocalAuthGuard)
  @Post('merchant/login')
  @ApiOperation({ summary: 'Log in with merchant credentials' })
  @ApiCreatedResponse({
    description: 'Merchant logged in successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid username or password' })
  @ApiUnauthorizedResponse({ description: 'Invalid password for the username' })
  @ApiForbiddenResponse({ description: 'Subscription cancelled for the club' })
  async merchantLogin(@Body() loginData: MerchantLoginDto) {
    const result = await this.authService.login(loginData, 'merchant');
    return result;
  }

  @Public()
  @Post('merchant/create-merchant-accounts')
  @ApiOperation({ summary: 'Create merchant accounts' })
  @ApiCreatedResponse({
    description: 'Merchant accounts created successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid request data' })
  @ApiBody({ type: UpdateMerchantPasswordDto })
  async updateMerchantPassword(@Body() updatePasswordDto: UpdateMerchantPasswordDto) {
    return this.authService.createMerchantAccounts(updatePasswordDto);
  }

  @Public()
  @Post('merchant/update-password')
  @ApiOperation({ summary: 'Update merchant password (owner/branch)' })
  @ApiCreatedResponse({
    description: 'Password updated successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid request data' })
  @ApiUnauthorizedResponse({ description: 'Invalid current password' })
  async updatePassword(@Body() updatePasswordDto: UpdatePasswordDto) {
    return this.authService.updatePassword(updatePasswordDto);
  }

  @Public()
  @Get('shop/:shopId/logins')
  @ApiOperation({
    summary: 'Get shop logins',
    description: 'Get shop logins',
  })
  async getShopLogins(@Param('shopId') shopId: string) {
    return this.authService.getShopLogins(shopId);
  }

  @Public()
  @Get('auth/check-username/:username')
  @ApiOperation({ summary: 'Check if a username exists' })
  @ApiCreatedResponse({
    description: 'Username exists',
  })
  @ApiBadRequestResponse({ description: 'Invalid username' })
  async checkUsername(@Param('username') username: string) {
    return this.authService.checkUsername(username);
  }
}
